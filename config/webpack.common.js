const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');

module.exports = {
    output: {
        path: './src/static/js/app/',
        publicPath: '/static/js/app/',
        filename: 'app.bundle.js'
    },
    resolve: {
        root: './src/',
        modulesDirectories: ['node_modules'],
        alias: {
            '@fec': 'static/lib/FrontEndCentral'
        }
    },
    module: {
        loaders: [
            {
                test: /\.js$/,
                exclude: /(node_modules)/,
                loader: 'babel',
                query: {
                    presets: ['es2015']
                }
            }
        ]
    },
    plugins: [
        new HtmlWebpackPlugin({
            template: './src/templates/directory/index_file_to_change.html',
            filename: '../../../templates/directory/index.html'
        })
    ]
};
