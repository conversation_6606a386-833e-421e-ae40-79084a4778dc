const webpackMerge = require('webpack-merge');
const commonConfig = require('./webpack.common.js');
const webpack = require('webpack');

module.exports = webpackMerge(commonConfig, {
    output: {
        filename: 'app.bundle.js'
    },
    entry: './src/static/js/app/main.ts',
    resolve: {
        extensions: ['', '.web.js', '.ts', '.js']
    },
    module: {
        loaders: [
            { test: /\.ts$/, loader: 'awesome-typescript-loader' }
        ]
    },
    plugins: [
        new webpack.DefinePlugin({
            PRODUCTION: false
        })
    ]
});
