# Presence Builder SDK

## Release Notes

### v6.0.0
- Initial Python 3 migration

### v5.1.0
- AddonClient with resolvePendingAddonActivationAPIV3 endpoint added.

### v5.0.0
- <PERSON>reate<PERSON>artner api call now has ALLOWED_ARGS of only POST

### v4.7.0
- Regen sdk for vapi update

### v4.6.0
- Add /internalApi/v3/listingDirectoryUrl/ endpoint

### v4.5.0
- Add internalApi/v3/partner/update endpoint

### v4.4.0
- Add internalApi/v3/site/getReviewUrl endpoint

### v4.3.0
 - addReviewUrl added to Site Get api response

### v4.2.2
 - latitude/longitude now specified as float for create and update api.

### v4.2.1
  - create no longer requires a page. If none is provided, a default will be used instead.

### v4.2
- Added Instagram, Pinterest, LinkedIn, Foursquare, Google+ to the Social Links
  - create/update site now accepts instagramUrl, pinterestUrl, linkedinUrl, foursquareUrl, googleplusUrl

### v4.1
- Added internal partner create api

### v4.0
- `slug` is now optional on site create api `createSite()`/`createSiteAsync()`.
  *IMPORTANT*: this is a breaking change; `slug` is no longer an arg, it is now a kwarg and
  your existing usages *MUST BE UPDATED!!!*
- an upgrade to VApi means that this SDK also deserializes all `*DateTime`, `*Date`, and `*Time` in responses
  automatically. *IMPORTANT*: this may be a breaking change; you must check your client code to ensure
  you are handling these response values appropriately. Looking at the generated internal documentation, it looks like
  `activateSite()`/`activateSiteAsync()` (deprecated) is impacted, but an extensive survey has not been performed.

### v3.6
- added support for specifying up to 3 taxId values when creating or updating a microsite

### v3.5
- Social Page is no longer supported and twitter_screen_name can no longer be provided with page data

### v3.4
- workNumber now supports multiple phone numbers for v3 site create and update endpoint
- Added callTrackingNumber which also supports multiple numbers for v3 site create and update endpoint

