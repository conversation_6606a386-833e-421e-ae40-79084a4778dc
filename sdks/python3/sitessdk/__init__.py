"""
MS SDK

To call an API endpoint, do something like the following:

    import sdk
    client = sdk.MentionClient('[your-api-user]', '[your-api-key]')
    client.setMentionRank('mid1', rank=4)

You can also use asynchronous NDB tasklet calls to the API:

    import sdk

    mention_client = sdk.MentionClient('[your-api-user]', '[your-api-key]')
    mentions_future = mention_client.getMentionsAsync(['mid1', 'mid2'])

    listing_client = sdk.ListingClient('[your-api-user]', '[your-api-key]')
    listings_future = listing_client.getVerifiedListingsAsync('pid1')

    # both requests are executing in parallel, call get_result() to wait and return the result
    mentions = mentions_future.get_result()
    listings = listings_future.get_result()

Keep in mind that these SDK NDB tasklets can operate in parallel with other NDB tasklets
like Datastore operations:

    @ndb.tasklet
    def domain_method():
          mention_client = sdk.MentionClient('[your-api-user]', '[your-api-key]')
          listing_client = sdk.ListingClient('[your-api-user]', '[your-api-key]')
          mentions, listings, users = yield (mention_client.getMentionsAsync(['mid1', 'mid2']),
                                             listing_client.getVerifiedListingsAsync('pid1'),
                                             UserModel.query.fetch_async())
         raise ndb.Return(mentions, listings, users)

For some API endpoints, you are allowed to "blank" values (meaning a query parameter with no
value will be sent on the wire). You can achieve this using the special value BLANK_ARG:

    import sdk
    client = sdk.ProfileClient('[your-api-user]', '[your-api-key]')
    profile = client.updateProfile('pid1', phone=sdk.BLANK_ARG) # sends "?pid=pid1&phone="

"""
import os
import re
import hashlib
import json
import threading
import time
import urllib.request, urllib.parse, urllib.error
import base64
import datetime
import logging
import urllib.parse
import pickle

from google.appengine.api import memcache
from google.appengine.ext import ndb
from google.appengine.runtime.apiproxy_errors import DeadlineExceededError
from google.appengine.api import full_app_id


__version__ = '6.0.1'
VERSION = __version__

__all__ = [
    'BLANK_ARG',
    'DEBUG_LVL',
    'INFO_LVL',
    'WARNING_LVL',
    'ERROR_LVL',
    'CRITICAL_LVL',
    'VALID_LOGGING_LEVELS',
    'CONFIG_LOCAL',
    'CONFIG_TEST',
    'CONFIG_DEMO',
    'CONFIG_PROD',
    'VALID_CONFIGS',
    'ApiException',
    'Client4XXError',
    'BadRequest400Error',
    'Unauthorized401Error',
    'Forbidden403Error',
    'NotFound404Error',
    'MethodNotAllowed405Error',
    'RequestTimeout408Error',
    'Conflict409Error',
    'PreconditionFailed412Error',
    'UnprocessableEntity422Error',
    'Server5XXError',
    'InternalServer500Error',
    'ServiceUnavailable503Error',
    'MissingVObjectError',
    'AddonClient',
    'CouponsClient',
    'ListingDirectoryClient',
    'MediaClient',
    'PartnerClient',
    'PostClient',
    'SiteClient',
    'SocialClient',
    'UnifiedBillingClient',
]

BLANK_ARG = '__use_this_magic_cookie_to_send_a_blank_argument__'

DEFAULT_DEADLINE = 60
DEBUG_LVL = 'D'
INFO_LVL = 'I'
WARNING_LVL = 'W'
ERROR_LVL = 'E'
CRITICAL_LVL = 'C'
VALID_LOGGING_LEVELS = {None, DEBUG_LVL, INFO_LVL, WARNING_LVL, ERROR_LVL, CRITICAL_LVL}

CONFIG_LOCAL = 'local'
CONFIG_TEST = 'test'
CONFIG_DEMO = 'demo'
CONFIG_PROD = 'prod'
VALID_CONFIGS = {CONFIG_LOCAL, CONFIG_TEST, CONFIG_DEMO, CONFIG_PROD}

TEST_EXCEPTIONS = {'steprep-test-hrd'}
DEMO_EXCEPTIONS = {'steprep-demo-hrd'}
PROD_EXCEPTIONS = {'steprep-prod-hrd', 'partner-central'}

IS_DEV_APPSERVER = 'development' in os.environ.get('SERVER_SOFTWARE', '').lower()
APP_ID = full_app_id.get().lower()
if APP_ID.startswith('s~'):
    APP_ID = APP_ID[2:]
IS_PROD = APP_ID in PROD_EXCEPTIONS or APP_ID.endswith('-prod')
IS_DEMO = APP_ID in DEMO_EXCEPTIONS or APP_ID.endswith('-demo')
IS_TEST = APP_ID in TEST_EXCEPTIONS or APP_ID.endswith('-test')

DEFAULT_CONFIG = CONFIG_LOCAL
if not IS_DEV_APPSERVER:
    if IS_PROD:
        DEFAULT_CONFIG = CONFIG_PROD
    elif IS_DEMO:
        DEFAULT_CONFIG = CONFIG_DEMO
    elif IS_TEST:
        DEFAULT_CONFIG = CONFIG_TEST

PROTOCOL_HTTP = 'http'
PROTOCOL_HTTPS = 'https'
VALID_PROTOCOLS = {PROTOCOL_HTTP, PROTOCOL_HTTPS}

HOST_MAP = {
    CONFIG_LOCAL: 'http://localhost:8085',
    CONFIG_TEST: 'http://microsite-test.appspot.com',
    CONFIG_DEMO: 'http://microsite-demo.appspot.com',
    CONFIG_PROD: 'http://microsite-prod.appspot.com',
}

META_MESSAGE = 'message'
META_VERSION = 'version'
META_RESPONSE_TIME = 'responseTime'
META_REQUEST_ID = 'requestId'
META_DATA = 'data'
META_ERROR_FIELD = 'errorField'
META_NEXT_URL = 'nextUrl'
META_TOTAL_RESULTS = 'totalResults'
META_LOG = 'log'
ISO_DATE_FORMAT_STR = '%Y-%m-%d'
ISO_TIME_FORMAT_STR = '%H:%M:%SZ'
ISO_DATETIME_FORMAT_STR = '%Y-%m-%dT%H:%M:%SZ'
DATE_ENDSWITH_KEY = 'Date'
TIME_ENDSWITH_KEY = 'Time'
DATETIME_ENDSWITH_KEY = 'DateTime'
API_USER = 'apiUser'
API_KEY = 'apiKey'
GET = 'GET'
POST = 'POST'
CURSOR_PREFIX = '__cursor__:'
MEMCACHE_NAMESPACE = '__vapi_MS__'
DATASTORE_CACHE_KIND = '_VApi_Cache'
PRODUCT = 'MS'

THREAD_LOCAL = threading.local()
CALLER_IDENTIFIER_HTTP_HEADER = "x-caller-identifier-session"


def set_caller_identifier(caller_session_id):
    """ Stores the caller identifier in the local thread """
    THREAD_LOCAL.caller_session_id = caller_session_id


def get_caller_identifier():
    """ Looks in the local thread for the caller identifier """
    return getattr(THREAD_LOCAL, "caller_session_id", "")


def parse_date_hook(json_dict):
    for key, value in json_dict.items():
        try:
            if key.endswith(DATETIME_ENDSWITH_KEY) and value and isinstance(value, str):
                json_dict[key] = datetime.datetime.strptime(value, ISO_DATETIME_FORMAT_STR)
            elif key.endswith(DATE_ENDSWITH_KEY) and value and isinstance(value, str):
                json_dict[key] = datetime.datetime.strptime(value, ISO_DATE_FORMAT_STR).date()
            elif key.endswith(TIME_ENDSWITH_KEY) and value and isinstance(value, str):
                json_dict[key] = datetime.datetime.strptime(value, ISO_TIME_FORMAT_STR).time()
        except ValueError:
            logging.warn('Unable to deserialize %s: %s, value will remain as string', key, value)
    return json_dict


class ApiException(Exception):
    def __init__(self, *args, **kwargs):
        super(ApiException, self).__init__(*args)
        self.statusCode = kwargs.pop('statusCode', None)
        self.data = kwargs.pop('data', None)
        self.errorField = kwargs.pop('errorField', None)
class Client4XXError(ApiException):
    statusCode = 400
class BadRequest400Error(Client4XXError):
    statusCode = 400
class Unauthorized401Error(Client4XXError):
    statusCode = 401
class Forbidden403Error(Client4XXError):
    statusCode = 403
class NotFound404Error(Client4XXError):
    statusCode = 404
class MethodNotAllowed405Error(Client4XXError):
    statusCode = 405
class RequestTimeout408Error(Client4XXError):
    statusCode = 408
class Conflict409Error(Client4XXError):
    statusCode = 409
class PreconditionFailed412Error(Client4XXError):
    statusCode = 412
class UnprocessableEntity422Error(Client4XXError):
    statusCode = 422
class Server5XXError(ApiException):
    statusCode = 500
class InternalServer500Error(Server5XXError):
    statusCode = 500
class ServiceUnavailable503Error(Server5XXError):
    statusCode = 503
STATUS_CODE_MAP = {
    400: BadRequest400Error,
    401: Unauthorized401Error,
    403: Forbidden403Error,
    404: NotFound404Error,
    405: MethodNotAllowed405Error,
    408: RequestTimeout408Error,
    409: Conflict409Error,
    412: PreconditionFailed412Error,
    422: UnprocessableEntity422Error,
    500: InternalServer500Error,
    503: ServiceUnavailable503Error,
}
class ApiUrlFetchError(Exception):
    pass
class ApiDeadlineExceededError(Exception):
    pass
class MissingVObjectError(Exception):
    """ raised when a vobject cannot be imported for response inflation """
    pass


class ApiResponse(dict):
    """
    API Response Object
    """
    def __init__(self, raw_json, status_code):
        """
        init
        """
        super(ApiResponse, self).__init__()
        self.raw_json = raw_json
        self.status_code = status_code
        self.__loaded = False

    @property
    def message(self):
        """
        The message from the API. May be None.
        """
        self.__loadJson()
        return self.get(META_MESSAGE, None)

    @property
    def version(self):
        """
        The version from the API.
        """
        self.__loadJson()
        return self[META_VERSION]

    @property
    def responseTime(self):
        """
        The response time, in milliseconds, from the API.
        """
        self.__loadJson()
        return self.get(META_RESPONSE_TIME, -1)

    @property
    def requestId(self):
        """
        The request id (os.environ['REQUEST_LOG_ID']) from the API.
        """
        self.__loadJson()
        return self.get(META_REQUEST_ID, None)

    @property
    def data(self):
        """
        The data from the API. May be None.
        """
        self.__loadJson()
        return self.get(META_DATA, None)

    @property
    def errorField(self):
        """
        The data from the API. May be None.
        """
        self.__loadJson()
        return self.get(META_ERROR_FIELD, None)

    @property
    def nextUrl(self):
        """
        The next page url from the API for paged responses. May be None.
        """
        self.__loadJson()
        return self.get(META_NEXT_URL, None)

    @property
    def totalResults(self):
        """
        The total results in the results set for paged that support it. May be None.
        """
        self.__loadJson()
        return self.get(META_TOTAL_RESULTS, None)

    @property
    def log(self):
        """
        The logs returned by the API if supported. May be None.
        """
        self.__loadJson()
        return self.get(META_LOG, None)

    def get(self, k, d=None):
        """
        D.get(k[,d]) -> D[k] if k in D, else d.  d defaults to None.
        Lazy load the json before get
        """
        self.__loadJson()
        return super(ApiResponse, self).get(k, d)

    def __loadJson(self):
        """
        load json into self
        """
        if not self.__loaded:
            self.update(json.loads(self.raw_json, object_hook=parse_date_hook))
            self.__loaded = True

    def __repr__(self):
        """
        return rawJson
        """
        return self.raw_json

    def __str__(self):
        """
        return rawJson
        """
        return repr(self)


class _LocalCacheModel(ndb.Model):
    """
    Stores locally cached values.
    """
    _use_cache = False
    _use_memcache = False

    value = ndb.BlobProperty(indexed=False)
    expires = ndb.DateTimeProperty()

    @classmethod
    def _get_kind(cls):
        """
        Returns the datastore kind.
        """
        return DATASTORE_CACHE_KIND

    @classmethod
    def build_key(cls, cache_key):
        """
        Builds a key for the cache entry.
        """
        return ndb.Key(cls, cache_key, namespace='')

    @classmethod
    def get(cls, cache_key, use_memcache=False):
        """
        Gets a cache entry, return None if not found.
        """
        key = cls.build_key(cache_key)
        entity = key.get(use_memcache=use_memcache)
        if not entity:
            return None
        now = datetime.datetime.utcnow()
        if entity.expires and entity.expires <= now:
            return None
        return pickle.loads(entity.value)

    @classmethod
    def set(cls, cache_key, value, time=None, use_memcache=False):
        """
        Sets a cache entry. Time is the cache timeout in seconds.
        """
        key = cls.build_key(cache_key)
        expires = None
        if time:
            expires = datetime.datetime.utcnow() + datetime.timedelta(seconds=time)
        entity = cls(key=key, value=pickle.dumps(value), expires=expires)
        entity.put(use_memcache=use_memcache, memcache_timeout=time)
        return entity


class LocalCache(object):
    """
    A local cache.
    """
    def __init__(self, use_datastore=False, use_ndb_memcache=False, logging_fn=None):
        """
        Initialize.

        If use_datastore is True, datastore will be used for storage - basically a slower, more durable cache.
        Additionally, if use_datastore is True and use_ndb_memcache is True, then the automatic NDB memcache
        layer will be enabled in front of the datastore layer.

        If use_datastore is False (the default), memcache will be used. In this case, the
        use_ndb_memcache setting is unused.
        """
        self.use_datastore = use_datastore
        self.use_ndb_memcache = use_ndb_memcache
        self.logging_fn = logging_fn

    def get(self, key):
        """
        Gets a cache entry, return None if not found.
        """
        if self.use_datastore:
            value = _LocalCacheModel.get(key, use_memcache=self.use_ndb_memcache)
        else:
            value = memcache.get(key, namespace=MEMCACHE_NAMESPACE)
        if self.logging_fn:
            if value is not None:
                self.logging_fn('Cache hit for "%s".', key, logLevel=logging.DEBUG)
            else:
                self.logging_fn('Cache miss for "%s".', key, logLevel=logging.DEBUG)
        return value

    def set(self, key, value, time=0):
        """
        Sets a cache entry. Time is the cache timeout in seconds.
        """
        time = time or 0
        if self.use_datastore:
            _LocalCacheModel.set(key, value, time=time, use_memcache=self.use_ndb_memcache)
        else:
            memcache.set(key, value, time=time, namespace=MEMCACHE_NAMESPACE)
        if self.logging_fn:
            self.logging_fn('Cache set for "%s".', key, logLevel=logging.DEBUG)


class AbstractClient(object):
    """
    Abstract Client
    """

    QUERY_PARAMS_ARGS_SPLITTER = re.compile(r"""(\w*password\w*[=])\s*([^&]*)""", flags=re.IGNORECASE)
    MAP_ARGS_SPLITTER = re.compile(r"""(\w*password\w*['"][:])(\s*['"])([^'"]*)""", flags=re.IGNORECASE)

    def __init__(self,
                 apiUser,
                 apiKey,
                 configuration=None,
                 loggingEnabled=True,
                 deadline=None,
                 remoteLogLevel=None,
                 allowVObjectResponse=False
                 ):
        """
        Initialize this manager.
        when set includeRemoteLogs is None on prod/demo, the request will not request remote logs
        """
        if not apiUser:
            raise ValueError('apiUser is required.')
        if not apiKey:
            raise ValueError('apiKey is required.')
        self.BLANK_ARG = BLANK_ARG
        self.apiUser = apiUser
        self.apiKey = apiKey
        self.configuration = configuration or DEFAULT_CONFIG
        if self.configuration not in VALID_CONFIGS:
            raise ValueError('configuration must be in %s.' % VALID_CONFIGS)
        self.host = HOST_MAP[self.configuration]
        self.loggingEnabled = loggingEnabled
        self.deadline = deadline or DEFAULT_DEADLINE
        self.remoteLogLevel = remoteLogLevel
        self.allowVObjectResponse = allowVObjectResponse
        if self.remoteLogLevel not in VALID_LOGGING_LEVELS:
            raise ValueError("%s is not valid logging level" % self.remoteLogLevel)

    @classmethod
    def _clean_password_values(cls, params):
        """ Returns a multi-dict with password values as a string of asterisks. """
        raw_output = repr(params)
        result = cls.QUERY_PARAMS_ARGS_SPLITTER.sub(r"\1******", raw_output)
        result = cls.MAP_ARGS_SPLITTER.sub(r"\1\2******", result)
        return result

    def log(self, msg, *args, **kwargs):
        """
        Emit a log message if so configured.
        """
        msg = 'vapi-sdk-ms %s' % msg
        logLevel = kwargs.pop('logLevel', logging.INFO)
        if self.loggingEnabled:
            logging.log(logLevel, msg, *args, **kwargs)

    def _createApiResponse(self, response_content, response_status_code):
        """
        Returns an ApiResponse from the HTTP response. Raises exception if status code is 400- or 500-series.
        """
        api_response = ApiResponse(response_content, response_status_code)
        try:
            message = api_response.message
            data = api_response.data
        except ValueError:
            message = response_content
            data = None
        if response_status_code >= 400 and response_status_code < 500:
            exc_class = STATUS_CODE_MAP.get(response_status_code, Client4XXError)
            raise exc_class(message, statusCode=response_status_code, data=data, errorField=api_response.errorField)
        if response_status_code >= 500:
            exc_class = STATUS_CODE_MAP.get(response_status_code, Server5XXError)
            raise exc_class(message, statusCode=response_status_code, data=data)
        return api_response

    def doPost(self, path, params=None, requires_https=False,
               cache_key=None, cache_timeout=0, cache_use_datastore=False, cache_use_ndb_memcache=False):
        """
        Make a synchronous POST call to API.

        Returns an ApiResponse.
        """
        return self.doPostAsync(path,
                                params=params,
                                requires_https=requires_https,
                                cache_key=cache_key,
                                cache_timeout=cache_timeout,
                                cache_use_datastore=cache_use_datastore,
                                cache_use_ndb_memcache=cache_use_ndb_memcache).get_result()

    @ndb.tasklet
    def doPostAsync(self, path, params=None, requires_https=False,
                    cache_key=None, cache_timeout=0, cache_use_datastore=False, cache_use_ndb_memcache=False):
        """
        Make an asynchronous POST call to API.

        Returns an RPC. Call rpc.wait() to block then rpc.result will contain the ApiResponse.
        """
        protocol = PROTOCOL_HTTPS if requires_https else PROTOCOL_HTTP
        url = self.makeUrl(path, protocol=protocol)
        params = params or {}
        form_data = self._urlencode(params)
        api_response = yield self._fetchAsync(url,
                                              payload=form_data,
                                              method=POST,
                                              cache_key=cache_key,
                                              cache_timeout=cache_timeout,
                                              cache_use_datastore=cache_use_datastore,
                                              cache_use_ndb_memcache=cache_use_ndb_memcache)
        raise ndb.Return(api_response)

    def doGet(self, path, params=None, requires_https=False,
              cache_key=None, cache_timeout=0, cache_use_datastore=False, cache_use_ndb_memcache=False):
        """
        Make a synchronous GET call to API.

        Returns an ApiResponse.
        """
        return self.doGetAsync(path,
                               params=params,
                               requires_https=requires_https,
                               cache_key=cache_key,
                               cache_timeout=cache_timeout,
                               cache_use_datastore=cache_use_datastore,
                               cache_use_ndb_memcache=cache_use_ndb_memcache).get_result()

    @ndb.tasklet
    def doGetAsync(self, path, params=None, requires_https=False,
                   cache_key=None, cache_timeout=0, cache_use_datastore=False, cache_use_ndb_memcache=False):
        """
        Make an asynchronous GET call to API.

        Returns an RPC. Call rpc.wait() to block then rpc.result will contain the ApiResponse.
        """
        protocol = PROTOCOL_HTTPS if requires_https else PROTOCOL_HTTP
        url = self.makeUrl(path, params=params, protocol=protocol)
        # method = GET if len(url) <= 2000 else POST # TODO? pay attention to credentials
        api_response = yield self._fetchAsync(url,
                                              method=GET,
                                              cache_key=cache_key,
                                              cache_timeout=cache_timeout,
                                              cache_use_datastore=cache_use_datastore,
                                              cache_use_ndb_memcache=cache_use_ndb_memcache)
        raise ndb.Return(api_response)

    def _fetch(self, url, payload=None, method=GET, headers=None, allow_truncated=False, follow_redirects=False,
               cache_key=None, cache_timeout=0, cache_use_datastore=False, cache_use_ndb_memcache=False):
        """
        Fetches a url using either the supplied method or appengines urlfetch.
        """
        return self._fetchAsync(url,
                                payload=payload,
                                method=method,
                                headers=headers,
                                allow_truncated=allow_truncated,
                                follow_redirects=follow_redirects,
                                cache_key=cache_key,
                                cache_timeout=cache_timeout,
                                cache_use_datastore=cache_use_datastore,
                                cache_use_ndb_memcache=cache_use_ndb_memcache).get_result()

    @ndb.tasklet
    def _fetchAsync(self, url, payload=None, method=GET, headers=None, allow_truncated=False, follow_redirects=False,
                    cache_key=None, cache_timeout=0, cache_use_datastore=False, cache_use_ndb_memcache=False):
        """
        Fetches a url using either the supplied method or appengines urlfetch.
        """
        if not url:
            raise ValueError('url is required.')
        assert url.startswith('http://') or url.startswith('https://'), "url must start with 'http://' or 'https://'"

        headers = headers or {}

        caller_session_id = get_caller_identifier()
        if caller_session_id:
            headers[CALLER_IDENTIFIER_HTTP_HEADER] = caller_session_id

        # check cache
        api_response = None
        if cache_key:
            try:
                local_cache = LocalCache(use_datastore=cache_use_datastore, use_ndb_memcache=cache_use_ndb_memcache,
                                         logging_fn=self.log)
                cache_entry = local_cache.get(cache_key)
                if cache_entry is not None:
                    response_content, response_status_code = cache_entry
                    api_response = self._createApiResponse(response_content, response_status_code)
            except Exception:
                self.log('Failed checking for cached result for "%s". Going over wire instead.', cache_key,
                         exc_info=True, logLevel=logging.INFO)
        if api_response is not None:
            raise ndb.Return(api_response)

        # go on the wire
        ctx = ndb.get_context()
        self.log('%s "%s"', ((method == 1 or method == GET) and GET or POST), self._clean_password_values(url),
                 logLevel=logging.DEBUG)
        if payload:
            self.log('payload %s', self._clean_password_values(payload), logLevel=logging.DEBUG)
        if headers:
            self.log('headers %s', self._clean_password_values(headers), logLevel=logging.DEBUG)
        if self.remoteLogLevel:
            # the log level is already present on a cursor url, so we need to make sure we don't double up
            qs = urllib.parse.urlparse(url).query
            params = urllib.parse.parse_qs(qs)
            if 'log' not in params:
                appendChar = '&' if '?' in url else '?'
                url = url + appendChar + 'log=' + self.remoteLogLevel
        start_time = end_time = 0
        try:
            start_time = time.time()
            response = yield ctx.urlfetch(url,
                                          payload=payload,
                                          method=method,
                                          headers=headers,
                                          allow_truncated=allow_truncated,
                                          follow_redirects=follow_redirects,
                                          deadline=self.deadline)
            end_time = time.time() # this timing is not perfect, because it depends on when the client calls wait()
            self.log('Response %s, content: %s', response.status_code, self._clean_password_values(response.content),
                     logLevel=logging.DEBUG)
        except DeadlineExceededError as e:
            self.log('Deadline Exceeded when fetching url "%s". Raising exception.', url, exc_info=True,
                     logLevel=logging.ERROR)
            raise ApiDeadlineExceededError(e)
        except Exception as e:
            self.log('Failed to fetch url "%s". Raising exception.', url, exc_info=True, logLevel=logging.ERROR)
            raise ApiUrlFetchError(e)

        api_response = self._createApiResponse(response.content, response.status_code)

        url_parts = urllib.parse.urlparse(url)
        self.log('Url: %s://%s%s Status: %s Server: %dms Client: ~%dms RequestId: %s',
                 url_parts.scheme, url_parts.netloc, url_parts.path,
                 response.status_code, api_response.responseTime,
                 (end_time-start_time)*1000, api_response.requestId)

        # cache result
        if cache_key:
            # Only cache 200 responses; it feels incorrect to cache 201, 301, 302, etc. responses and
            # it is probably a misconfiguration to add caching to an endpoint that returns these
            # types of responses.
            if response.status_code == 200:
                self.log('Caching response on "%s".', cache_key, logLevel=logging.DEBUG)
                try:
                    local_cache = LocalCache(use_datastore=cache_use_datastore, use_ndb_memcache=cache_use_ndb_memcache,
                                             logging_fn=self.log)
                    local_cache.set(cache_key, (response.content, response.status_code), time=cache_timeout)
                except Exception:
                    self.log('Failed to cache response for "%s". Continuing.', cache_key,
                             exc_info=True, logLevel=logging.INFO)
                    # do not re-raise
            elif response.status_code < 400:
                self.log('Caching for "%s" is configured, but only 200 responses are able to be' + \
                         'cached (received response code %s). Response will not be cached.',
                         cache_key, response.statusCode, logLevel=logging.WARN)

        raise ndb.Return(api_response)

    def addCredentialsToUrl(self, url):
        """
        Adds the credentials to a url.
        """
        credentials = '%s=%s&%s=%s' % (API_USER, self.apiUser, API_KEY, self.apiKey)
        separator = url.find('?') < 0 and '?' or '&'
        return '%s%s%s' % (url, separator, credentials)

    def addReturnVObjectFlagToUrl(self, url):
        """ Add the flag to indicate if the user is requesting a vObject """
        args = 'returnVObjectFlag=%s' % self.allowVObjectResponse
        separator = url.find('?') < 0 and '?' or '&'
        return '%s%s%s' % (url, separator, args)

    def makeUrl(self, path, params=None, protocol=PROTOCOL_HTTP):
        """
        Make a URL to call the API.
        """
        if not path:
            raise ValueError('path is required.')
        if self.configuration == CONFIG_LOCAL:
            protocol = PROTOCOL_HTTP
        params = params or {}
        params = self._urlencode(params)
        url = self.host + path
        if params:
            url = url + "?" + params
        url = self.addCredentialsToUrl(url)
        url = self.addReturnVObjectFlagToUrl(url)
        if protocol and not url.startswith(protocol + '://'):
            url = protocol + '://' + url[url.find('://')+3:]
        return url

    def _encodedValue(self, value):
        """
        Encode a single parameter value.
        """
        if value == BLANK_ARG:
            return ''
        elif isinstance(value, datetime.datetime):
            return value.strftime(ISO_DATETIME_FORMAT_STR)
        elif isinstance(value, str):
            return value.encode('utf-8')
        return value

    def _urlencode(self, params):
        """
        Encode the parameters.
        """
        if not isinstance(params, dict):
            raise ValueError("params must be a dict.")
        params_list = []
        for key, value in list(params.items()):
            if isinstance(value, list):
                if len(value) == 0:
                    params_list.append((key, ''))
                else:
                    params_list.extend([(key, self._encodedValue(v)) for v in value])
            else:
                params_list.append((key, self._encodedValue(value)))
        return urllib.parse.urlencode(params_list)

    def getEncryptedCursor(self, url):
        """
        Pulls the cursor out of the url and base64 encodes it
        """
        pieces = urllib.parse.urlparse(url)
        queryString = pieces.query
        path = pieces.path
        cursor = base64.b64encode('%s?%s' % (path, queryString))
        # add prefix to indicate it is a encrypted cursor
        return '%s%s' % (CURSOR_PREFIX, cursor)

    def generateNextUrlFromEncryptedCursor(self, cursor):
        """
        Decodes the cursor and returns the nextUrl
        """
        # remove the prefix
        cursor = cursor[len(CURSOR_PREFIX):]
        queryString = base64.b64decode(cursor)
        return self.host + queryString

    def validate_max_string_length(self, arg_name, value, max_length):
        """
        Ensures that the value is not longer than max_length (may be a list of items).
        """
        if isinstance(value, list):
            if not all(len(v) <= max_length for v in value):
                raise ValueError('All items for "%s" must be no longer than %d.' % (arg_name, max_length))
        elif value is not None:
            if len(value) > max_length:
                raise ValueError('"%s" must be no longer than %d.' % (arg_name, max_length))

    def validate_max_list_length(self, arg_name, value, max_length):
        """
        Ensures that the value list is not longer than the max_length
        """
        if not isinstance(value, list):
            return
        if len(value) > max_length:
            raise ValueError('"%s" must not have more than %d items.' % (arg_name, max_length))

    @ndb.tasklet
    def fetchNextPageAsync(self, nextUrl, encrypted=True):
        """
        Asynchronously fetch the next set of result from the nextUrl
        encrypted=True: the nextUrl is encrypted (also see getEncryptedCursor)
        encrypted=False: the nextUrl is not encrypted
        """
        if encrypted and nextUrl.startswith(CURSOR_PREFIX):
            nextUrl = self.generateNextUrlFromEncryptedCursor(nextUrl)
        url = self.addCredentialsToUrl(nextUrl)
        api_response = yield self._fetchAsync(url, method=GET)
        raise ndb.Return(api_response.data, api_response.nextUrl, api_response.totalResults)

    def fetchNextPage(self, nextUrl, encrypted=True):
        """
        Return the next set of result from the nextUrl
        encrypted=True: the nextUrl is encrypted (also see getEncryptedCursor)
        encrypted=False: the nextUrl is not encrypted
        """
        return self.fetchNextPageAsync(nextUrl, encrypted=encrypted).get_result()

    @classmethod
    def generateCacheKey(cls, base, path, params):
        """
        Generates a general purpose cache key for SDK caching.
        """
        # for a stable key, we need to get the params from the dictionary, sorted
        params = params or {}
        parts = [str(key) + '=' + str(params[key]) for key in sorted(params.keys())]
        params_str = '&'.join(parts)
        hash_ = hashlib.sha1((path.encode('utf-8') + params_str.encode('utf-8'))).hexdigest()
        key = '%s:%s:%s:%s' % (base, PRODUCT, VERSION, hash_)
        return key

    def inflate_response(self, data, response_class):
        data = response_class.get_mapped_data(data)
        if self.allowVObjectResponse and response_class.VOBJECT_PATH:
            try:
                vobject_module, _, vobject_class = response_class.VOBJECT_PATH.rpartition('.')
                vobject = getattr(__import__(vobject_module, fromlist=[vobject_class]), vobject_class)
                return vobject(data)
            except (ImportError, AttributeError):
                raise MissingVObjectError("Unable to import '%s' for response inflation", response_class.VOBJECT_PATH)
        else:
            return response_class(data)

    def format_response(self, data, response_class=None, response_dict_key=None, response_vobject=None):
        if response_class and response_class.IS_INFLATABLE:
            if response_dict_key and isinstance(data, dict):
                data = dict((item[0], self.inflate_response(item[1], response_class)) for item in data.items())
            elif isinstance(data, list):
                data = [self.inflate_response(item, response_class) for item in data]
            else:
                data = self.inflate_response(data, response_class)
        elif response_vobject and self.allowVObjectResponse:
            if isinstance(data, dict):
                data = {key: response_vobject.deserialize(value) for key, value in data.items()}
            elif isinstance(data, list):
                data = [response_vobject.deserialize(d) for d in data]
            elif data:
                data = response_vobject.deserialize(data)
        return data

    def get_vobject_class(self, vobject_path):
        """ get the vobject class from its path """
        vobject_module, _, vobject_class = vobject_path.rpartition('.')
        return getattr(__import__(vobject_module, fromlist=[vobject_class]), vobject_class)


class CouponsClient(AbstractClient):
    """
    V3 APIs used for managing Coupons.
    """

    def getCouponsV3(self, msid=None, pid=None):
        """
        Get all the coupons titles that exist under a given msid, along with their associated urls.

        Kwargs:
            msid (required)
                A short system defined ID assigned to a microsite during creation process
                An internal identifier that is unique across the entire product.
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
        """

        return self.getCouponsV3Async(msid=msid, pid=pid).get_result()

    @ndb.tasklet
    def getCouponsV3Async(self, msid=None, pid=None):
        """
        Get all the coupons titles that exist under a given msid, along with their associated urls.

        Kwargs:
            msid (required)
                A short system defined ID assigned to a microsite during creation process
                An internal identifier that is unique across the entire product.
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
        """

        if pid is None or (isinstance(pid, str) and pid == '') or pid == BLANK_ARG:
            raise ValueError('"pid" is required.')
        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        if msid is None or (isinstance(msid, str) and msid == '') or msid == BLANK_ARG:
            raise ValueError('"msid" is required.')
        if isinstance(msid, list):
            raise ValueError('"msid" may not be a list.')

        params = {}
        if pid is not None:
            params['pid'] = pid
        if msid is not None:
            params['msid'] = msid
        path = '/internalApi/v3/coupons/get/'
        cache_key = cache_time = None
        api_response = yield self.doGetAsync(path, params=params, requires_https=False,
                                             cache_key=cache_key, cache_timeout=cache_time,
                                             cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)


class ListingDirectoryClient(AbstractClient):
    """
    V3 Internal APIs used for listing directory.
    """

    def getListingDirectoryUrlV3(self, partnerId=None, marketId=None):
        """
        Retrieve the listing directory URL.

        Kwargs:
            partnerId (required)
            marketId
        """

        return self.getListingDirectoryUrlV3Async(partnerId=partnerId, marketId=marketId).get_result()

    @ndb.tasklet
    def getListingDirectoryUrlV3Async(self, partnerId=None, marketId=None):
        """
        Retrieve the listing directory URL.

        Kwargs:
            partnerId (required)
            marketId
        """

        if partnerId is None or (isinstance(partnerId, str) and partnerId == '') or partnerId == BLANK_ARG:
            raise ValueError('"partnerId" is required.')
        if isinstance(partnerId, list):
            raise ValueError('"partnerId" may not be a list.')

        if isinstance(marketId, list):
            raise ValueError('"marketId" may not be a list.')

        params = {}
        if partnerId is not None:
            params['partnerId'] = partnerId
        if marketId is not None:
            params['marketId'] = marketId
        path = '/internalApi/v3/listingDirectoryUrl/'
        cache_key = cache_time = None
        api_response = yield self.doGetAsync(path, params=params, requires_https=False,
                                             cache_key=cache_key, cache_timeout=cache_time,
                                             cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)


class MediaClient(AbstractClient):
    """
    V3 Internal APIs used for managing Media.
    """

    def getUploadUrlV3(self, msid=None, pid=None):
        """
        Get url for uploading to a site's blobstore.

        Kwargs:
            msid (required)
                A short system defined ID assigned to a microsite during creation process
                An internal identifier that is unique across the entire product.
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
        """

        return self.getUploadUrlV3Async(msid=msid, pid=pid).get_result()

    @ndb.tasklet
    def getUploadUrlV3Async(self, msid=None, pid=None):
        """
        Get url for uploading to a site's blobstore.

        Kwargs:
            msid (required)
                A short system defined ID assigned to a microsite during creation process
                An internal identifier that is unique across the entire product.
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
        """

        if pid is None or (isinstance(pid, str) and pid == '') or pid == BLANK_ARG:
            raise ValueError('"pid" is required.')
        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        if msid is None or (isinstance(msid, str) and msid == '') or msid == BLANK_ARG:
            raise ValueError('"msid" is required.')
        if isinstance(msid, list):
            raise ValueError('"msid" may not be a list.')

        params = {}
        if pid is not None:
            params['pid'] = pid
        if msid is not None:
            params['msid'] = msid
        path = '/internalApi/v3/uploadUrl/get/'
        cache_key = cache_time = None
        api_response = yield self.doGetAsync(path, params=params, requires_https=False,
                                             cache_key=cache_key, cache_timeout=cache_time,
                                             cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)


class PartnerClient(AbstractClient):
    """
    Internal APIs for managing partners.
    """

    def createPartnerV3(self, partnerApiKey=None, partnerId=None, partnerName=None, partnerHostname=None, validateOnlyFlag=None):
        """
        API to create a new partner.

        Kwargs:
            partnerApiKey (required)
                The API key that the partner uses to call external APIS.
            partnerId (required)
                The global partner ID. Must be globally unique.
                Partner id must not already be in use
            partnerName (required)
                The partner's name.
            partnerHostname
                The fully qualified hostname, e.g., <code>www.example.com</code>. If provided, it must be globally unique. If not provided, one will be generated of the form <code>[pid].localhost:8085</code>.
                Checks the hostname for uniqueness
            validateOnlyFlag
                When this flag is set, the partner creation arguments are validated. No partner is created.
        """

        return self.createPartnerV3Async(partnerApiKey=partnerApiKey, partnerId=partnerId, partnerName=partnerName, partnerHostname=partnerHostname, validateOnlyFlag=validateOnlyFlag).get_result()

    @ndb.tasklet
    def createPartnerV3Async(self, partnerApiKey=None, partnerId=None, partnerName=None, partnerHostname=None, validateOnlyFlag=None):
        """
        API to create a new partner.

        Kwargs:
            partnerApiKey (required)
                The API key that the partner uses to call external APIS.
            partnerId (required)
                The global partner ID. Must be globally unique.
                Partner id must not already be in use
            partnerName (required)
                The partner's name.
            partnerHostname
                The fully qualified hostname, e.g., <code>www.example.com</code>. If provided, it must be globally unique. If not provided, one will be generated of the form <code>[pid].localhost:8085</code>.
                Checks the hostname for uniqueness
            validateOnlyFlag
                When this flag is set, the partner creation arguments are validated. No partner is created.
        """

        if partnerName is None or (isinstance(partnerName, str) and partnerName == '') or partnerName == BLANK_ARG:
            raise ValueError('"partnerName" is required.')
        if isinstance(partnerName, list):
            raise ValueError('"partnerName" may not be a list.')

        if partnerApiKey is None or (isinstance(partnerApiKey, str) and partnerApiKey == '') or partnerApiKey == BLANK_ARG:
            raise ValueError('"partnerApiKey" is required.')
        if isinstance(partnerApiKey, list):
            raise ValueError('"partnerApiKey" may not be a list.')

        if partnerId is None or (isinstance(partnerId, str) and partnerId == '') or partnerId == BLANK_ARG:
            raise ValueError('"partnerId" is required.')
        if isinstance(partnerId, list):
            raise ValueError('"partnerId" may not be a list.')

        if isinstance(partnerHostname, list):
            raise ValueError('"partnerHostname" may not be a list.')

        if isinstance(validateOnlyFlag, list):
            raise ValueError('"validateOnlyFlag" may not be a list.')

        params = {}
        if partnerName is not None:
            params['partnerName'] = partnerName
        if partnerApiKey is not None:
            params['partnerApiKey'] = partnerApiKey
        if partnerId is not None:
            params['partnerId'] = partnerId
        if partnerHostname is not None:
            params['partnerHostname'] = partnerHostname
        if validateOnlyFlag is not None:
            params['validateOnlyFlag'] = validateOnlyFlag
        path = '/internalApi/v3/partner/create/'
        cache_key = cache_time = None
        api_response = yield self.doPostAsync(path, params=params, requires_https=False,
                                              cache_key=cache_key, cache_timeout=cache_time,
                                              cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)

    def updatePartnerV3(self, partnerId=None, partnerApiKey=None, partnerName=None):
        """
        Updates an existing partner

        Kwargs:
            partnerId (required)
            partnerApiKey
            partnerName
        """

        return self.updatePartnerV3Async(partnerId=partnerId, partnerApiKey=partnerApiKey, partnerName=partnerName).get_result()

    @ndb.tasklet
    def updatePartnerV3Async(self, partnerId=None, partnerApiKey=None, partnerName=None):
        """
        Updates an existing partner

        Kwargs:
            partnerId (required)
            partnerApiKey
            partnerName
        """

        if partnerId is None or (isinstance(partnerId, str) and partnerId == '') or partnerId == BLANK_ARG:
            raise ValueError('"partnerId" is required.')
        if isinstance(partnerId, list):
            raise ValueError('"partnerId" may not be a list.')

        if isinstance(partnerName, list):
            raise ValueError('"partnerName" may not be a list.')

        if isinstance(partnerApiKey, list):
            raise ValueError('"partnerApiKey" may not be a list.')

        params = {}
        if partnerId is not None:
            params['partnerId'] = partnerId
        if partnerName is not None:
            params['partnerName'] = partnerName
        if partnerApiKey is not None:
            params['partnerApiKey'] = partnerApiKey
        path = '/internalApi/v3/partner/update/'
        cache_key = cache_time = None
        api_response = yield self.doPostAsync(path, params=params, requires_https=False,
                                              cache_key=cache_key, cache_timeout=cache_time,
                                              cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)


class PostClient(AbstractClient):
    """
    These end-points are used for managing Posts.
    """

    def createImagePostV2(self, blobKey=None, pid=None, servingUrl=None, hiddenFlag=None, identifier=None, imageHeight=None, imageWidth=None, msid=None, publishDateTime=None):
        """
        This endpoint is used to publish an image-only longform post.

        Kwargs:
            blobKey (required)
                The blob key of the image
            pid (required)
                A partner identifier.
            servingUrl (required)
                The serving URL of the image
            hiddenFlag
                The post will be inaccessible via the previous/next navigation if set to True.
            identifier
                A short system defined ID assigned to a microsite during creation process.
                Either msid or identifier must be specified but not both.
            imageHeight
                Image height in pixels
                Validate image height argument
            imageWidth
                Image width in pixels
                Validate image width argument
            msid
                A short system defined ID assigned to a microsite during creation process.
                Either msid or identifier must be specified but not both.
            publishDateTime
                The UTC datetime to publish this post.
                If not set, the current time is used.
        """

        return self.createImagePostV2Async(blobKey=blobKey, pid=pid, servingUrl=servingUrl, hiddenFlag=hiddenFlag, identifier=identifier, imageHeight=imageHeight, imageWidth=imageWidth, msid=msid, publishDateTime=publishDateTime).get_result()

    @ndb.tasklet
    def createImagePostV2Async(self, blobKey=None, pid=None, servingUrl=None, hiddenFlag=None, identifier=None, imageHeight=None, imageWidth=None, msid=None, publishDateTime=None):
        """
        This endpoint is used to publish an image-only longform post.

        Kwargs:
            blobKey (required)
                The blob key of the image
            pid (required)
                A partner identifier.
            servingUrl (required)
                The serving URL of the image
            hiddenFlag
                The post will be inaccessible via the previous/next navigation if set to True.
            identifier
                A short system defined ID assigned to a microsite during creation process.
                Either msid or identifier must be specified but not both.
            imageHeight
                Image height in pixels
                Validate image height argument
            imageWidth
                Image width in pixels
                Validate image width argument
            msid
                A short system defined ID assigned to a microsite during creation process.
                Either msid or identifier must be specified but not both.
            publishDateTime
                The UTC datetime to publish this post.
                If not set, the current time is used.
        """

        if blobKey is None or (isinstance(blobKey, str) and blobKey == '') or blobKey == BLANK_ARG:
            raise ValueError('"blobKey" is required.')
        if isinstance(blobKey, list):
            raise ValueError('"blobKey" may not be a list.')

        if pid is None or (isinstance(pid, str) and pid == '') or pid == BLANK_ARG:
            raise ValueError('"pid" is required.')
        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        if servingUrl is None or (isinstance(servingUrl, str) and servingUrl == '') or servingUrl == BLANK_ARG:
            raise ValueError('"servingUrl" is required.')
        if isinstance(servingUrl, list):
            raise ValueError('"servingUrl" may not be a list.')

        if isinstance(hiddenFlag, list):
            raise ValueError('"hiddenFlag" may not be a list.')

        if isinstance(publishDateTime, list):
            raise ValueError('"publishDateTime" may not be a list.')

        if isinstance(imageHeight, list):
            raise ValueError('"imageHeight" may not be a list.')

        if isinstance(imageWidth, list):
            raise ValueError('"imageWidth" may not be a list.')

        if isinstance(identifier, list):
            raise ValueError('"identifier" may not be a list.')

        if isinstance(msid, list):
            raise ValueError('"msid" may not be a list.')

        params = {}
        if blobKey is not None:
            params['blobKey'] = blobKey
        if pid is not None:
            params['pid'] = pid
        if servingUrl is not None:
            params['servingUrl'] = servingUrl
        if hiddenFlag is not None:
            params['hiddenFlag'] = hiddenFlag
        if publishDateTime is not None:
            params['publishDateTime'] = publishDateTime
        if imageHeight is not None:
            params['imageHeight'] = imageHeight
        if imageWidth is not None:
            params['imageWidth'] = imageWidth
        if identifier is not None:
            params['identifier'] = identifier
        if msid is not None:
            params['msid'] = msid
        path = '/api/v2/imagePost/create/'
        cache_key = cache_time = None
        api_response = yield self.doPostAsync(path, params=params, requires_https=True,
                                              cache_key=cache_key, cache_timeout=cache_time,
                                              cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)

    def createPostV2(self, content=None, pid=None, hiddenFlag=None, identifier=None, msid=None, publishDateTime=None, title=None):
        """
        This endpoint is used to publish a longform post.

        Kwargs:
            content (required)
                The post content. This can be abritrary HTML content. Maximum 1000000 characters
                or roughly 1MB of data.
            pid (required)
                A partner identifier.
            hiddenFlag
                The post will be inaccessible via the previous/next navigation if set to True.
            identifier
                A short system defined ID assigned to a microsite during creation process.
                Either msid or identifier must be specified but not both.
            msid
                A short system defined ID assigned to a microsite during creation process.
                Either msid or identifier must be specified but not both.
            publishDateTime
                The datetime to publish this post. If not set, the current time is used.
            title
                The post title. Maximum 500 characters. If title is provided, slug is created using the
                provided title otherwise post identifier is used which is generated automatically. When
                title is provided, it cannot be blank string.
        """

        return self.createPostV2Async(content=content, pid=pid, hiddenFlag=hiddenFlag, identifier=identifier, msid=msid, publishDateTime=publishDateTime, title=title).get_result()

    @ndb.tasklet
    def createPostV2Async(self, content=None, pid=None, hiddenFlag=None, identifier=None, msid=None, publishDateTime=None, title=None):
        """
        This endpoint is used to publish a longform post.

        Kwargs:
            content (required)
                The post content. This can be abritrary HTML content. Maximum 1000000 characters
                or roughly 1MB of data.
            pid (required)
                A partner identifier.
            hiddenFlag
                The post will be inaccessible via the previous/next navigation if set to True.
            identifier
                A short system defined ID assigned to a microsite during creation process.
                Either msid or identifier must be specified but not both.
            msid
                A short system defined ID assigned to a microsite during creation process.
                Either msid or identifier must be specified but not both.
            publishDateTime
                The datetime to publish this post. If not set, the current time is used.
            title
                The post title. Maximum 500 characters. If title is provided, slug is created using the
                provided title otherwise post identifier is used which is generated automatically. When
                title is provided, it cannot be blank string.
        """

        if content is None or (isinstance(content, str) and content == '') or content == BLANK_ARG:
            raise ValueError('"content" is required.')
        if isinstance(content, list):
            raise ValueError('"content" may not be a list.')

        if pid is None or (isinstance(pid, str) and pid == '') or pid == BLANK_ARG:
            raise ValueError('"pid" is required.')
        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        if isinstance(hiddenFlag, list):
            raise ValueError('"hiddenFlag" may not be a list.')

        if isinstance(identifier, list):
            raise ValueError('"identifier" may not be a list.')

        if isinstance(publishDateTime, list):
            raise ValueError('"publishDateTime" may not be a list.')

        if isinstance(msid, list):
            raise ValueError('"msid" may not be a list.')

        if isinstance(title, list):
            raise ValueError('"title" may not be a list.')

        params = {}
        if content is not None:
            params['content'] = content
        if pid is not None:
            params['pid'] = pid
        if hiddenFlag is not None:
            params['hiddenFlag'] = hiddenFlag
        if identifier is not None:
            params['identifier'] = identifier
        if publishDateTime is not None:
            params['publishDateTime'] = publishDateTime
        if msid is not None:
            params['msid'] = msid
        if title is not None:
            params['title'] = title
        path = '/api/v2/post/create/'
        cache_key = cache_time = None
        api_response = yield self.doPostAsync(path, params=params, requires_https=True,
                                              cache_key=cache_key, cache_timeout=cache_time,
                                              cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)

    def deletePostV2(self, pid=None, postId=None, identifier=None, msid=None):
        """
        This endpoint is used to delete a longform post.

        Kwargs:
            pid (required)
                A partner identifier.
            postId (required)
                A system defined ID assigned to a post during the creation process.
            identifier
                A short system defined ID assigned to a microsite during creation process.
                Either msid or identifier must be specified but not both.
            msid
                A short system defined ID assigned to a microsite during creation process.
                Either msid or identifier must be specified but not both.
        """

        return self.deletePostV2Async(pid=pid, postId=postId, identifier=identifier, msid=msid).get_result()

    @ndb.tasklet
    def deletePostV2Async(self, pid=None, postId=None, identifier=None, msid=None):
        """
        This endpoint is used to delete a longform post.

        Kwargs:
            pid (required)
                A partner identifier.
            postId (required)
                A system defined ID assigned to a post during the creation process.
            identifier
                A short system defined ID assigned to a microsite during creation process.
                Either msid or identifier must be specified but not both.
            msid
                A short system defined ID assigned to a microsite during creation process.
                Either msid or identifier must be specified but not both.
        """

        if postId is None or (isinstance(postId, str) and postId == '') or postId == BLANK_ARG:
            raise ValueError('"postId" is required.')
        if isinstance(postId, list):
            raise ValueError('"postId" may not be a list.')

        if pid is None or (isinstance(pid, str) and pid == '') or pid == BLANK_ARG:
            raise ValueError('"pid" is required.')
        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        if isinstance(identifier, list):
            raise ValueError('"identifier" may not be a list.')

        if isinstance(msid, list):
            raise ValueError('"msid" may not be a list.')

        params = {}
        if postId is not None:
            params['postId'] = postId
        if pid is not None:
            params['pid'] = pid
        if identifier is not None:
            params['identifier'] = identifier
        if msid is not None:
            params['msid'] = msid
        path = '/api/v2/post/delete/'
        cache_key = cache_time = None
        api_response = yield self.doPostAsync(path, params=params, requires_https=True,
                                              cache_key=cache_key, cache_timeout=cache_time,
                                              cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)

    def mapPostBlobV2(self, blobsInfo=None, expiry=None, msid=None, pid=None, socialPostId=None):
        """
        This end point is used to map a post's social post id to blobs. When expiry arrives, the social post will be
        checked for success. If it failed, then the blobs and mappings associated with that social post will be removed.

        Kwargs:
            blobsInfo (required)
                The blobInfo is used to identify list of blob information dictionaries.
                You must have at least 1 blob information. <br />
                <strong>Blobs information attributes (* means required): </strong>
                <table>
                <tr>
                <td>blobkey*</td>
                <td>Blob key that is to be mapped.</td>
                </tr>
                <tr>
                <td>serving_url*</td>
                <td>Url at which blob can be accessed.</td>
                </tr>
                </table>
                <br />
                This is an example how blob information should look like:
                <br />
                <pre>
                "blobInfo": [
                {
                "blobkey": "wOIoDlo4ZlUwMrbGPSBiLg==",
                "serving_url": "http://www.example.com/wOIoDlo4ZlUwMrbGPSBiLg=="
                },
                {
                "blobkey": "a0koDlo4ZlUwMrbGPSBiLg==",
                "serving_url": "http://www.example.com/a0koDlo4ZlUwMrbGPSBiLg=="
                }]
                </pre>
                Validate blobs information argument
                Supports lists.
            expiry (required)
                Expiry datetime (utc) of the blobs after which they are deleted if post was unsuccessful.
                Validate expiry argument
            msid (required)
                A microsite identifier.
            pid (required)
                A partner identifier.
            socialPostId (required)
                A social post identifier created for the post.
        """

        return self.mapPostBlobV2Async(blobsInfo=blobsInfo, expiry=expiry, msid=msid, pid=pid, socialPostId=socialPostId).get_result()

    @ndb.tasklet
    def mapPostBlobV2Async(self, blobsInfo=None, expiry=None, msid=None, pid=None, socialPostId=None):
        """
        This end point is used to map a post's social post id to blobs. When expiry arrives, the social post will be
        checked for success. If it failed, then the blobs and mappings associated with that social post will be removed.

        Kwargs:
            blobsInfo (required)
                The blobInfo is used to identify list of blob information dictionaries.
                You must have at least 1 blob information. <br />
                <strong>Blobs information attributes (* means required): </strong>
                <table>
                <tr>
                <td>blobkey*</td>
                <td>Blob key that is to be mapped.</td>
                </tr>
                <tr>
                <td>serving_url*</td>
                <td>Url at which blob can be accessed.</td>
                </tr>
                </table>
                <br />
                This is an example how blob information should look like:
                <br />
                <pre>
                "blobInfo": [
                {
                "blobkey": "wOIoDlo4ZlUwMrbGPSBiLg==",
                "serving_url": "http://www.example.com/wOIoDlo4ZlUwMrbGPSBiLg=="
                },
                {
                "blobkey": "a0koDlo4ZlUwMrbGPSBiLg==",
                "serving_url": "http://www.example.com/a0koDlo4ZlUwMrbGPSBiLg=="
                }]
                </pre>
                Validate blobs information argument
                Supports lists.
            expiry (required)
                Expiry datetime (utc) of the blobs after which they are deleted if post was unsuccessful.
                Validate expiry argument
            msid (required)
                A microsite identifier.
            pid (required)
                A partner identifier.
            socialPostId (required)
                A social post identifier created for the post.
        """

        if pid is None or (isinstance(pid, str) and pid == '') or pid == BLANK_ARG:
            raise ValueError('"pid" is required.')
        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        if msid is None or (isinstance(msid, str) and msid == '') or msid == BLANK_ARG:
            raise ValueError('"msid" is required.')
        if isinstance(msid, list):
            raise ValueError('"msid" may not be a list.')

        if socialPostId is None or (isinstance(socialPostId, str) and socialPostId == '') or socialPostId == BLANK_ARG:
            raise ValueError('"socialPostId" is required.')
        if isinstance(socialPostId, list):
            raise ValueError('"socialPostId" may not be a list.')

        if blobsInfo is None or (isinstance(blobsInfo, str) and blobsInfo == '') or blobsInfo == BLANK_ARG:
            raise ValueError('"blobsInfo" is required.')

        if expiry is None or (isinstance(expiry, str) and expiry == '') or expiry == BLANK_ARG:
            raise ValueError('"expiry" is required.')
        if isinstance(expiry, list):
            raise ValueError('"expiry" may not be a list.')

        params = {}
        if pid is not None:
            params['pid'] = pid
        if msid is not None:
            params['msid'] = msid
        if socialPostId is not None:
            params['socialPostId'] = socialPostId
        if blobsInfo is not None:
            params['blobsInfo'] = blobsInfo
        if expiry is not None:
            params['expiry'] = expiry
        path = '/api/v2/post/mapBlob/'
        cache_key = cache_time = None
        api_response = yield self.doGetAsync(path, params=params, requires_https=True,
                                             cache_key=cache_key, cache_timeout=cache_time,
                                             cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)

    def postCleanupV2(self, expiry=None, pid=None, postId=None, socialPostId=None):
        """
        set scheduled date so that a cron will remove ms posts, images associated with it, and the post-image mapping for
        each image in the post if the social post that was going to show these urls failed

        Kwargs:
            expiry (required)
                The date by which the social posts using these image pages should be created
            pid (required)
                A partner ID
            postId (required)
                A list of microsite Post page IDs that are being linked to in the social post. If the social post fails at the expiry time, these image posts will be removed
            socialPostId (required)
                The pastSocialPostId or futureSocialPostId from core for the social post associated with the pages being cleaned up
        """

        return self.postCleanupV2Async(expiry=expiry, pid=pid, postId=postId, socialPostId=socialPostId).get_result()

    @ndb.tasklet
    def postCleanupV2Async(self, expiry=None, pid=None, postId=None, socialPostId=None):
        """
        set scheduled date so that a cron will remove ms posts, images associated with it, and the post-image mapping for
        each image in the post if the social post that was going to show these urls failed

        Kwargs:
            expiry (required)
                The date by which the social posts using these image pages should be created
            pid (required)
                A partner ID
            postId (required)
                A list of microsite Post page IDs that are being linked to in the social post. If the social post fails at the expiry time, these image posts will be removed
            socialPostId (required)
                The pastSocialPostId or futureSocialPostId from core for the social post associated with the pages being cleaned up
        """

        if pid is None or (isinstance(pid, str) and pid == '') or pid == BLANK_ARG:
            raise ValueError('"pid" is required.')
        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        if expiry is None or (isinstance(expiry, str) and expiry == '') or expiry == BLANK_ARG:
            raise ValueError('"expiry" is required.')
        if isinstance(expiry, list):
            raise ValueError('"expiry" may not be a list.')

        if socialPostId is None or (isinstance(socialPostId, str) and socialPostId == '') or socialPostId == BLANK_ARG:
            raise ValueError('"socialPostId" is required.')
        if isinstance(socialPostId, list):
            raise ValueError('"socialPostId" may not be a list.')

        if postId is None or (isinstance(postId, str) and postId == '') or postId == BLANK_ARG:
            raise ValueError('"postId" is required.')
        if isinstance(postId, list):
            raise ValueError('"postId" may not be a list.')

        params = {}
        if pid is not None:
            params['pid'] = pid
        if expiry is not None:
            params['expiry'] = expiry
        if socialPostId is not None:
            params['socialPostId'] = socialPostId
        if postId is not None:
            params['postId'] = postId
        path = '/api/v2/post/cleanup/'
        cache_key = cache_time = None
        api_response = yield self.doGetAsync(path, params=params, requires_https=True,
                                             cache_key=cache_key, cache_timeout=cache_time,
                                             cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)


class SiteClient(AbstractClient):
    """
    V3 Internal APIs used for managing Sites.
    """

    def activateSiteV3(self, msid=None, pid=None):
        """
        Upgrades a lite microsite account to a full paid account

        Kwargs:
            msid (required)
                A short system defined ID assigned to a microsite during creation process
                An internal identifier that is unique across the entire product.
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
        """

        return self.activateSiteV3Async(msid=msid, pid=pid).get_result()

    @ndb.tasklet
    def activateSiteV3Async(self, msid=None, pid=None):
        """
        Upgrades a lite microsite account to a full paid account

        Kwargs:
            msid (required)
                A short system defined ID assigned to a microsite during creation process
                An internal identifier that is unique across the entire product.
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
        """

        if pid is None or (isinstance(pid, str) and pid == '') or pid == BLANK_ARG:
            raise ValueError('"pid" is required.')
        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        if msid is None or (isinstance(msid, str) and msid == '') or msid == BLANK_ARG:
            raise ValueError('"msid" is required.')
        if isinstance(msid, list):
            raise ValueError('"msid" may not be a list.')

        params = {}
        if pid is not None:
            params['pid'] = pid
        if msid is not None:
            params['msid'] = msid
        path = '/internalApi/v3/site/activate/'
        cache_key = cache_time = None
        api_response = yield self.doGetAsync(path, params=params, requires_https=False,
                                             cache_key=cache_key, cache_timeout=cache_time,
                                             cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)

    def createSiteV3(self, pid=None, address=None, agid=None, announcement=None, callTrackingNumber=None, city=None, color=None, companyName=None, country=None, customDomain=None, customerIdentifier=None, email=None, facebookUrl=None, filename=None, foursquareUrl=None, googleplusUrl=None, host=None, hours=None, identifier=None, instagramUrl=None, jobId=None, latitude=None, layout=None, linkedinUrl=None, logoUrl=None, longitude=None, marketId=None, notificationUrl=None, page=None, pinterestUrl=None, place=None, rssUrl=None, service=None, slug=None, spid=None, ssoToken=None, state=None, tagline=None, taxId=None, twitterUrl=None, validateOnlyFlag=None, website=None, workNumber=None, youtubeUrl=None, zip=None):
        """
        A single microsite is specified using a JSON dictionary encoding. The dictionary for a single microsite contains all
        the details necessary to create the microsite, including titles, headings, images, videos, pages, etc.

        Kwargs:
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
            address
                A street address
            agid
                If an <code>agid</code> is specified, the following fields will be ignored (though they may still be required, dummy values may be provided): <code>address</code>, <code>city</code>, <code>state</code>, <code>country</code>, <code>zip</code>, <code>companyName</code>, <code>latitude</code>, <code>longitude</code>, <code>marketId</code>, <code>taxId</code>, <code>workNumber</code>. These values will be pulled from the current data for the account group specified by the given <code>agid</code>.
                must be a valid account group
            announcement
                An announcement that is intended to be updated frequently and appears prominently in a call-to-action location.
                DEPRECATED! This field is deprecated.
            callTrackingNumber
                The list of call tracking numbers. Should be formatted for display. If a valid country code is given, phone number is formatted according to local phone number format. E.g., if 'CA' is provided as country, phone number is formatted as (*************
                Supports lists.
            city
                The name of a city
            color
                The primary color for the microsite. For hex values, you must include the "#" leading character. E.g., "#99CC00".
                must be a valid hex color
            companyName
                The name of the microsite appears in the header of each page.
            country
                A country name or 2 character code. If valid country name is provided, it is converted to corresponding country code.
            customDomain
                A custom domain e.g. "customDomain" : "m.abc.com". Appropriate setup on the customer side needs to be performed to make this active.
                must be a valid url
            customerIdentifier
                A partner's microsite identifier.
                must be unique
            email
                An e-mail address
                must be a valid email address
            facebookUrl
                A link to a Facebook Page associated with this microsite.
            filename
            foursquareUrl
                A link to a Foursquare Venue associated with this microsite.
            googleplusUrl
                A link to a YouTube channel associated with this microsite.
            host
                Host for the slug to be appended to. Must be one of the valid partner's hosts that uses slugs. If not provided, the first host (default) will be used.
                must already exist
            hours
                The hours attribute is used to specify the Hours of Operation section, if applicable. The attribute is a nested JSON dictionary, yielding something like the following: <pre> "hours": { "monday": {"open": "9:00 AM", "close": "5:00 AM"}, ...} </pre> Each day of the week can have an open and a close string, plus an open2 and a close2 string for split hours (e.g., "9:00AM - 12:00PM, 1:00PM - 5:00PM"). Additionally, each day of the week can support an arbitrary note. There are also some attributes specified globally: open24, holidays, and other. open24 is a boolean flag, and needs to be encoded like the following: <pre> "open24": true (or) "open24": false </pre> <strong>Hours dictionary attributes:</strong> <table> <tr> <td> monday, tuesday, wednesday, thursday, friday, saturday, sunday </td> <td> day of week dictionary (see below) </td> </tr> <tr> <td>open24</td> <td>boolean, either true or false</td> </tr> <tr> <td>holidays</td><td>string</td> </tr> <tr> <td>other</td><td>string</td> </tr> </table> <strong>Day of week dictionary attributes:</strong> <table> <tr> <td>open</td><td>Should be a locale-appropriate time. E.g., "9:00 AM", "1:30pm", "17:15", etc.</td></tr> <tr><td>close</td><td>As above</td><tr> <tr><td>open2</td><td>As above</td><tr> <tr><td>close2</td><td>As above</td><tr> <tr><td>note</td><td>An arbitrary note that can be used in addition to, or in place of, the above attributes.</td><tr> </table> In an hours dictionary specification, all fields are optional, which provides for a very flexible interface. Below is an example, though your approach may be very different. <pre> "hours": { "monday": { "open": "9:00 am", "close": "5:00 pm", "note": "closed for lunch" }, "tuesday": { "open": "9:00 am", "close": "5:00 pm" }, "friday": { "open": "9:00 am", "close": "5:00 pm" }, "wednesday": { "open": "9:00 am", "close": "5:00 pm" }, "thursday": { "open": "9:00 am", "close": "5:00 pm", "open2": "6:00pm", "close2": "9:00pm" }, "holiday": "Closed for stat holidays", "open24": false } </pre> Note the above example of split hours on Thursday.
            identifier
                A short ID for the microsite. Must be unique across the entire partner. One-to-one with a particular microsite.
                Validate a site does not already exist with the provided pmsid identifier.
            instagramUrl
                A link to a Instagram Profile associated with this microsite.
            jobId
                Custom identifier used to track requests through the pipeline to ease debugging.
            latitude
                E.g., "45.234887".
                Must be on the range [-90.0, 90.0]. If specified, <code>longitude</code> must
                also be specified.
            layout
                The layout to use for the website. Choices are: 'short', 'tall', 'responsive', and 'location'.
            linkedinUrl
                A link to a LinkedIn Profile associated with this microsite.
            logoUrl
                E.g., "http://www.example.com/foo.png". The logo will be read and imported into our system. Each batch import will provoke a check to see if the image has been updated and requires re-import.
                must be a valid image
            longitude
                E.g., "-58.245234".
                Must be on the range [-180.0, 180.0]. If specified, <code>latitude</code> must
                also be specified.
            marketId
                Determines which market is set for the account. Takes Market Key value from list of markets.
                must be a valid market id
            notificationUrl
            page
                The pages attribute is used to identify the set of pages for the microsite, as a list of page dictionaries. You must have at least 1 page, but you may not exceed 15 pages per microsite. When updating a microsite page, the slug attribute is used to identify the existing page. If a slug is used that is not present, a new page with that slug will be created. If a slug that previously existed is not in the pages list, the old page will be deleted and will return a 404 on subsequent requests. If the slug matches an existing page, that page will be updated with the new values. <br /> <strong>Page dictionary attributes (* means required):</strong> <table> <tr> <td>slug*</td> <td>The page slug is the string that identifies the page within a microsite. If a slug changes, the old page will be deleted (and requests for it will return a 404), and a new page will be created. Can only be letters, numbers, and dashes.</td> </tr> <tr> <td>template*</td> <td>One of: "Custom", "Images", "Contact", "Videos", "Coupons". Identifies the type of page content. Additional page dictionary attributes may be specified depending on the selected template - see below. This field is case-sensitive.</td> </tr> <tr> <td>tab_name*</td> <td>The name that appears on the navigation item.</td> </tr> <tr> <td>h1*</td> <td>The content that appears in the h1 tag for the page. Do not include the <h1></h1> tags themselves.</td> </tr> <tr> <td>title*</td> <td>The content that appears in the meta title tag for the page. Do not include the <title></title> tags themselves.</td> </tr> <tr> <td>meta_keywords</td> <td>The content for the meta keywords tag for the page. Do not include the <meta> tag itself.</td> </tr> <tr> <td>meta_description</td> <td>The content for the meta description tag for the page. Do not include the <meta> tag itself. </td> </tr> <tr> <td>top_content</td> <td>Free content that appears before any content determined by the page template. </td> </tr> <tr> <td>bottom_content</td> <td>Free content that appears after any content determined by the page template.</td> </tr> <tr> <td>email</td> <td>Only for Contact template. This is the email address that will receive contact requests from this page.</td> </tr> <tr> <td>images</td> <td>List of image urls. Only for Images template. The images that should be displayed on this page. We will import these images into our system and resize them as appropriate. On subsequent imports, we will check to see if the image has been updated and re-import if necessary.</td> </tr> <tr> <td>mobile_gallery_style</td> <td>Only for Images template. How the images will be displayed on a mobile device. E.g., "table", "photoswipe". Default is "table".</td> </tr> <tr> <td>desktop_gallery_style</td> <td>Only for Images template. How the images will be displayed on a desktop computer. E.g., "table", "lightbox". Default is "table".</td> </tr> <tr> <td>videos</td> <td>List of video urls or video dictionaries. Only for Videos template. The videos that should be displayed on this page.</td> </tr> <tr> <td>coupons</td> <td>List of coupon dictionaries. Only for Coupons template. The coupons that should be displayed on this page.</td> </tr> </table> <br /> <strong>Default Page</strong> <br /> If a page is not supplied, the default page is as follows: <pre> "page": [{ 'slug': 'home', 'template': 'Custom', 'tab_name': 'Home', 'title': 'Home', 'h1': 'Home', }] </pre> <br /> <strong>Images</strong> <br /> The images attribute is used to specify a list of images for an Images page. The list can either be a list of string urls, or a list of dictionaries depending on how much you wish to specify. You can specify up to 20 images per Images page. JPG, GIF, PNG, and WEBP images are supported. An example images specification is as follows: <pre> "images": [ "http://www.example.com/img1.png", "http://www.example.com/img2.jpg", "http://www.example.com/img3.gif" ] </pre> These images will be imported into our system and served from our servers. On subsequent batch imports, we will check to see if the images have been updated and re-import them if necessary. It is an important optimization for the server serving the original images to emit Etag or Last-Modified headers. These headers will be used to determine if an image has been updated or not. <br /> <strong>Image dictionary attributes (* means required):</strong> <table> <tr> <td>url*</td> <td>The url for the image content. The image will be imported into our system.</td> </tr> <tr> <td>caption</td> <td>The caption for the image.</td> </tr> <tr> <td>alt_text</td> <td>The alt text for the image.</td> </tr> <tr> <td>title</td> <td>The title text for the image.</td> </tr> </table> <br /> <strong>Videos</strong> <br /> The videos attribute specifies a list of videos to appear on the Videos page. The list can either be a list of string urls, or a list of dictionaries depending on how much you wish to specify. Note that video and thumbnail content are served from the urls provided; these items are not imported into our system. <br /> <strong>Video dictionary attributes (* means required):</strong> <table> <tr> <td>url*</td> <td> The url for the video content. The video will be served from this location and not imported into our system. For YouTube urls, we will automatically retrieve thumbnails, titles, etc. if they are not specified. </td> </tr> <tr> <td>phone_url</td> <td> A secondary URL for a video that is served on the mobile site, the "url" is embedded while the "phone_url" is directly linked. </td> </tr> <tr> <td>thumbnail_url</td> <td> The url for a thumbnail for the video. The thumbnail will be served from this location and not imported into our system. </td> </tr> <tr> <td>title</td> <td>The title for the video.</td> </tr> <tr> <td>caption</td> <td>A caption or description for the video.</td> </tr> <tr> <td>duration</td> <td>The duration of the video.</td> </tr> </table> Minimally, the videos page attribute can be a simple list of urls to videos, like the following: <pre> "videos": [ "http://www.youtube.com/watch?v=zzfQwXEqYaI", "http://www.youtube.com/watch?v=ntT7v47RIds" ]</pre> Alternatively, if you want to specify thumbnails, captions, duration, etc., you can provide a list of video dictionaries: <pre> "videos": [ { "url": "http://www.example.com/vid1.mp4", "thumbnail_url": "http://www.example.com/th1.jpg", "title": "Watch us in action!" }, { "url": "http://www.example.com/vid2.mp4", "title": "Last Thursday's Outing" }] </pre> <br /> <strong>Coupons</strong> <br /> The coupons attribute specifies a list of coupons to appear on the Coupons page. The list is a list of coupon dictionaries. Note that the coupon image is served from the url provided; this item is not imported into our system. <br /> <strong>Coupon dictionary attributes (* means required):</strong> <table> <tr> <td>title*</td> <td> The title of the coupon. This is the minimal amount of data required to display a coupon. </td> </tr> <tr> <td>detail</td> <td>The detail text for the coupon giving additional information.</td> </tr> <tr> <td>image_url</td> <td>A small image that is displayed at the top of the coupon, it is served from this url and not imported. Maximum size 150x100 pixels. </td> </tr> <tr> <td>category</td> <td> The Category of the coupon, any text string is possible, can be empty. </td> </tr> <tr> <td>start</td> <td> The start date at which the coupon becomes active formatted as YYYY-MM-DD eg. 2011-05-25. </td> </tr> <tr> <td>end</td> <td> The end date after which the coupon is no longer available formatted the same way the start date is. </td> </tr> <tr> <td>expiry</td> <td> The expiry date of the coupon, the day when the customer can no longer use the coupon. </td> </tr> </table> This is an example how the list of coupon dictionaries can look: <pre> "coupons": [ { "end": "2011-11-30", "title": "Save 20 %", "detail": "On any purchase", "category" : "Auto", "expiry": "2011-11-30", "start": "2011-08-15", "image_url": "http://example.com/image.png" }, { "title": "Buy one get one free every day after 5pm" }] </pre>
                Supports lists. Maximum instances: 15.
            pinterestUrl
                A link to a Pinterest Profile associated with this microsite.
            place
                A colloquial name that may be used in keywords, etc. E.g., "Big Apple" or "Twin Cities".
            rssUrl
                A link to a blob or an RSS feed associated with this microsite.
            service
                A comma separated list of likely search terms for the business. Used in meta-content for SEO. e.g. "plumbing, sewer service, commercial plumbing".
                This will validate the service arg accepts up to 3 items
                Supports lists.
            slug
                For a slug "this-is-the-slug", the url for the microsite becomes: "http://m.partner.com/this-is-the-slug/". One-to-one with a particular microsite. Can only be letters, numbers, and dashes. When the provided slug already exists for the host, a unique slug is generated. The desired slug is used in combination with either the sites city, zip or if all else fails a random identifier. E.g., if the slug "this-is-the-slug" exists, then a unique slug "this-is-the-slug-portland" is generated, and the url for the microsite becomes: "http://m.partner.com/this-is-the-slug-portland/
            spid
                A social profile identifier from core that the site is associated with.
            ssoToken
                An SSO token to grant authorization for this site. Maximum length of 200.
            state
                A state name or 2 character code. It is also validated if valid country is provided.
            tagline
                A short tagline that appears in the header beneath the name.
            taxId
                must be a valid tax id
                Supports lists. Maximum instances: 3.
            twitterUrl
                A link to a twitter profile associated with this microsite.
            validateOnlyFlag
                If provided, the site will not actually be committed, but the arguments will be validated to ensure the site creation would succeed.
            website
                The company website, if one exists other than the microsite. Must start with http:// or https://
                must be a valid url
            workNumber
                The list of work phone number for the site. Should be formatted for display. If a valid country code is given, phone number is formatted according to local phone number format. E.g., if 'CA' is provided as country, phone number is formatted as (*************
                Supports lists.
            youtubeUrl
                A link to a YouTube channel associated with this microsite.
            zip
                A zip or postal code
        """

        return self.createSiteV3Async(pid=pid, address=address, agid=agid, announcement=announcement, callTrackingNumber=callTrackingNumber, city=city, color=color, companyName=companyName, country=country, customDomain=customDomain, customerIdentifier=customerIdentifier, email=email, facebookUrl=facebookUrl, filename=filename, foursquareUrl=foursquareUrl, googleplusUrl=googleplusUrl, host=host, hours=hours, identifier=identifier, instagramUrl=instagramUrl, jobId=jobId, latitude=latitude, layout=layout, linkedinUrl=linkedinUrl, logoUrl=logoUrl, longitude=longitude, marketId=marketId, notificationUrl=notificationUrl, page=page, pinterestUrl=pinterestUrl, place=place, rssUrl=rssUrl, service=service, slug=slug, spid=spid, ssoToken=ssoToken, state=state, tagline=tagline, taxId=taxId, twitterUrl=twitterUrl, validateOnlyFlag=validateOnlyFlag, website=website, workNumber=workNumber, youtubeUrl=youtubeUrl, zip=zip).get_result()

    @ndb.tasklet
    def createSiteV3Async(self, pid=None, address=None, agid=None, announcement=None, callTrackingNumber=None, city=None, color=None, companyName=None, country=None, customDomain=None, customerIdentifier=None, email=None, facebookUrl=None, filename=None, foursquareUrl=None, googleplusUrl=None, host=None, hours=None, identifier=None, instagramUrl=None, jobId=None, latitude=None, layout=None, linkedinUrl=None, logoUrl=None, longitude=None, marketId=None, notificationUrl=None, page=None, pinterestUrl=None, place=None, rssUrl=None, service=None, slug=None, spid=None, ssoToken=None, state=None, tagline=None, taxId=None, twitterUrl=None, validateOnlyFlag=None, website=None, workNumber=None, youtubeUrl=None, zip=None):
        """
        A single microsite is specified using a JSON dictionary encoding. The dictionary for a single microsite contains all
        the details necessary to create the microsite, including titles, headings, images, videos, pages, etc.

        Kwargs:
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
            address
                A street address
            agid
                If an <code>agid</code> is specified, the following fields will be ignored (though they may still be required, dummy values may be provided): <code>address</code>, <code>city</code>, <code>state</code>, <code>country</code>, <code>zip</code>, <code>companyName</code>, <code>latitude</code>, <code>longitude</code>, <code>marketId</code>, <code>taxId</code>, <code>workNumber</code>. These values will be pulled from the current data for the account group specified by the given <code>agid</code>.
                must be a valid account group
            announcement
                An announcement that is intended to be updated frequently and appears prominently in a call-to-action location.
                DEPRECATED! This field is deprecated.
            callTrackingNumber
                The list of call tracking numbers. Should be formatted for display. If a valid country code is given, phone number is formatted according to local phone number format. E.g., if 'CA' is provided as country, phone number is formatted as (*************
                Supports lists.
            city
                The name of a city
            color
                The primary color for the microsite. For hex values, you must include the "#" leading character. E.g., "#99CC00".
                must be a valid hex color
            companyName
                The name of the microsite appears in the header of each page.
            country
                A country name or 2 character code. If valid country name is provided, it is converted to corresponding country code.
            customDomain
                A custom domain e.g. "customDomain" : "m.abc.com". Appropriate setup on the customer side needs to be performed to make this active.
                must be a valid url
            customerIdentifier
                A partner's microsite identifier.
                must be unique
            email
                An e-mail address
                must be a valid email address
            facebookUrl
                A link to a Facebook Page associated with this microsite.
            filename
            foursquareUrl
                A link to a Foursquare Venue associated with this microsite.
            googleplusUrl
                A link to a YouTube channel associated with this microsite.
            host
                Host for the slug to be appended to. Must be one of the valid partner's hosts that uses slugs. If not provided, the first host (default) will be used.
                must already exist
            hours
                The hours attribute is used to specify the Hours of Operation section, if applicable. The attribute is a nested JSON dictionary, yielding something like the following: <pre> "hours": { "monday": {"open": "9:00 AM", "close": "5:00 AM"}, ...} </pre> Each day of the week can have an open and a close string, plus an open2 and a close2 string for split hours (e.g., "9:00AM - 12:00PM, 1:00PM - 5:00PM"). Additionally, each day of the week can support an arbitrary note. There are also some attributes specified globally: open24, holidays, and other. open24 is a boolean flag, and needs to be encoded like the following: <pre> "open24": true (or) "open24": false </pre> <strong>Hours dictionary attributes:</strong> <table> <tr> <td> monday, tuesday, wednesday, thursday, friday, saturday, sunday </td> <td> day of week dictionary (see below) </td> </tr> <tr> <td>open24</td> <td>boolean, either true or false</td> </tr> <tr> <td>holidays</td><td>string</td> </tr> <tr> <td>other</td><td>string</td> </tr> </table> <strong>Day of week dictionary attributes:</strong> <table> <tr> <td>open</td><td>Should be a locale-appropriate time. E.g., "9:00 AM", "1:30pm", "17:15", etc.</td></tr> <tr><td>close</td><td>As above</td><tr> <tr><td>open2</td><td>As above</td><tr> <tr><td>close2</td><td>As above</td><tr> <tr><td>note</td><td>An arbitrary note that can be used in addition to, or in place of, the above attributes.</td><tr> </table> In an hours dictionary specification, all fields are optional, which provides for a very flexible interface. Below is an example, though your approach may be very different. <pre> "hours": { "monday": { "open": "9:00 am", "close": "5:00 pm", "note": "closed for lunch" }, "tuesday": { "open": "9:00 am", "close": "5:00 pm" }, "friday": { "open": "9:00 am", "close": "5:00 pm" }, "wednesday": { "open": "9:00 am", "close": "5:00 pm" }, "thursday": { "open": "9:00 am", "close": "5:00 pm", "open2": "6:00pm", "close2": "9:00pm" }, "holiday": "Closed for stat holidays", "open24": false } </pre> Note the above example of split hours on Thursday.
            identifier
                A short ID for the microsite. Must be unique across the entire partner. One-to-one with a particular microsite.
                Validate a site does not already exist with the provided pmsid identifier.
            instagramUrl
                A link to a Instagram Profile associated with this microsite.
            jobId
                Custom identifier used to track requests through the pipeline to ease debugging.
            latitude
                E.g., "45.234887".
                Must be on the range [-90.0, 90.0]. If specified, <code>longitude</code> must
                also be specified.
            layout
                The layout to use for the website. Choices are: 'short', 'tall', 'responsive', and 'location'.
            linkedinUrl
                A link to a LinkedIn Profile associated with this microsite.
            logoUrl
                E.g., "http://www.example.com/foo.png". The logo will be read and imported into our system. Each batch import will provoke a check to see if the image has been updated and requires re-import.
                must be a valid image
            longitude
                E.g., "-58.245234".
                Must be on the range [-180.0, 180.0]. If specified, <code>latitude</code> must
                also be specified.
            marketId
                Determines which market is set for the account. Takes Market Key value from list of markets.
                must be a valid market id
            notificationUrl
            page
                The pages attribute is used to identify the set of pages for the microsite, as a list of page dictionaries. You must have at least 1 page, but you may not exceed 15 pages per microsite. When updating a microsite page, the slug attribute is used to identify the existing page. If a slug is used that is not present, a new page with that slug will be created. If a slug that previously existed is not in the pages list, the old page will be deleted and will return a 404 on subsequent requests. If the slug matches an existing page, that page will be updated with the new values. <br /> <strong>Page dictionary attributes (* means required):</strong> <table> <tr> <td>slug*</td> <td>The page slug is the string that identifies the page within a microsite. If a slug changes, the old page will be deleted (and requests for it will return a 404), and a new page will be created. Can only be letters, numbers, and dashes.</td> </tr> <tr> <td>template*</td> <td>One of: "Custom", "Images", "Contact", "Videos", "Coupons". Identifies the type of page content. Additional page dictionary attributes may be specified depending on the selected template - see below. This field is case-sensitive.</td> </tr> <tr> <td>tab_name*</td> <td>The name that appears on the navigation item.</td> </tr> <tr> <td>h1*</td> <td>The content that appears in the h1 tag for the page. Do not include the <h1></h1> tags themselves.</td> </tr> <tr> <td>title*</td> <td>The content that appears in the meta title tag for the page. Do not include the <title></title> tags themselves.</td> </tr> <tr> <td>meta_keywords</td> <td>The content for the meta keywords tag for the page. Do not include the <meta> tag itself.</td> </tr> <tr> <td>meta_description</td> <td>The content for the meta description tag for the page. Do not include the <meta> tag itself. </td> </tr> <tr> <td>top_content</td> <td>Free content that appears before any content determined by the page template. </td> </tr> <tr> <td>bottom_content</td> <td>Free content that appears after any content determined by the page template.</td> </tr> <tr> <td>email</td> <td>Only for Contact template. This is the email address that will receive contact requests from this page.</td> </tr> <tr> <td>images</td> <td>List of image urls. Only for Images template. The images that should be displayed on this page. We will import these images into our system and resize them as appropriate. On subsequent imports, we will check to see if the image has been updated and re-import if necessary.</td> </tr> <tr> <td>mobile_gallery_style</td> <td>Only for Images template. How the images will be displayed on a mobile device. E.g., "table", "photoswipe". Default is "table".</td> </tr> <tr> <td>desktop_gallery_style</td> <td>Only for Images template. How the images will be displayed on a desktop computer. E.g., "table", "lightbox". Default is "table".</td> </tr> <tr> <td>videos</td> <td>List of video urls or video dictionaries. Only for Videos template. The videos that should be displayed on this page.</td> </tr> <tr> <td>coupons</td> <td>List of coupon dictionaries. Only for Coupons template. The coupons that should be displayed on this page.</td> </tr> </table> <br /> <strong>Default Page</strong> <br /> If a page is not supplied, the default page is as follows: <pre> "page": [{ 'slug': 'home', 'template': 'Custom', 'tab_name': 'Home', 'title': 'Home', 'h1': 'Home', }] </pre> <br /> <strong>Images</strong> <br /> The images attribute is used to specify a list of images for an Images page. The list can either be a list of string urls, or a list of dictionaries depending on how much you wish to specify. You can specify up to 20 images per Images page. JPG, GIF, PNG, and WEBP images are supported. An example images specification is as follows: <pre> "images": [ "http://www.example.com/img1.png", "http://www.example.com/img2.jpg", "http://www.example.com/img3.gif" ] </pre> These images will be imported into our system and served from our servers. On subsequent batch imports, we will check to see if the images have been updated and re-import them if necessary. It is an important optimization for the server serving the original images to emit Etag or Last-Modified headers. These headers will be used to determine if an image has been updated or not. <br /> <strong>Image dictionary attributes (* means required):</strong> <table> <tr> <td>url*</td> <td>The url for the image content. The image will be imported into our system.</td> </tr> <tr> <td>caption</td> <td>The caption for the image.</td> </tr> <tr> <td>alt_text</td> <td>The alt text for the image.</td> </tr> <tr> <td>title</td> <td>The title text for the image.</td> </tr> </table> <br /> <strong>Videos</strong> <br /> The videos attribute specifies a list of videos to appear on the Videos page. The list can either be a list of string urls, or a list of dictionaries depending on how much you wish to specify. Note that video and thumbnail content are served from the urls provided; these items are not imported into our system. <br /> <strong>Video dictionary attributes (* means required):</strong> <table> <tr> <td>url*</td> <td> The url for the video content. The video will be served from this location and not imported into our system. For YouTube urls, we will automatically retrieve thumbnails, titles, etc. if they are not specified. </td> </tr> <tr> <td>phone_url</td> <td> A secondary URL for a video that is served on the mobile site, the "url" is embedded while the "phone_url" is directly linked. </td> </tr> <tr> <td>thumbnail_url</td> <td> The url for a thumbnail for the video. The thumbnail will be served from this location and not imported into our system. </td> </tr> <tr> <td>title</td> <td>The title for the video.</td> </tr> <tr> <td>caption</td> <td>A caption or description for the video.</td> </tr> <tr> <td>duration</td> <td>The duration of the video.</td> </tr> </table> Minimally, the videos page attribute can be a simple list of urls to videos, like the following: <pre> "videos": [ "http://www.youtube.com/watch?v=zzfQwXEqYaI", "http://www.youtube.com/watch?v=ntT7v47RIds" ]</pre> Alternatively, if you want to specify thumbnails, captions, duration, etc., you can provide a list of video dictionaries: <pre> "videos": [ { "url": "http://www.example.com/vid1.mp4", "thumbnail_url": "http://www.example.com/th1.jpg", "title": "Watch us in action!" }, { "url": "http://www.example.com/vid2.mp4", "title": "Last Thursday's Outing" }] </pre> <br /> <strong>Coupons</strong> <br /> The coupons attribute specifies a list of coupons to appear on the Coupons page. The list is a list of coupon dictionaries. Note that the coupon image is served from the url provided; this item is not imported into our system. <br /> <strong>Coupon dictionary attributes (* means required):</strong> <table> <tr> <td>title*</td> <td> The title of the coupon. This is the minimal amount of data required to display a coupon. </td> </tr> <tr> <td>detail</td> <td>The detail text for the coupon giving additional information.</td> </tr> <tr> <td>image_url</td> <td>A small image that is displayed at the top of the coupon, it is served from this url and not imported. Maximum size 150x100 pixels. </td> </tr> <tr> <td>category</td> <td> The Category of the coupon, any text string is possible, can be empty. </td> </tr> <tr> <td>start</td> <td> The start date at which the coupon becomes active formatted as YYYY-MM-DD eg. 2011-05-25. </td> </tr> <tr> <td>end</td> <td> The end date after which the coupon is no longer available formatted the same way the start date is. </td> </tr> <tr> <td>expiry</td> <td> The expiry date of the coupon, the day when the customer can no longer use the coupon. </td> </tr> </table> This is an example how the list of coupon dictionaries can look: <pre> "coupons": [ { "end": "2011-11-30", "title": "Save 20 %", "detail": "On any purchase", "category" : "Auto", "expiry": "2011-11-30", "start": "2011-08-15", "image_url": "http://example.com/image.png" }, { "title": "Buy one get one free every day after 5pm" }] </pre>
                Supports lists. Maximum instances: 15.
            pinterestUrl
                A link to a Pinterest Profile associated with this microsite.
            place
                A colloquial name that may be used in keywords, etc. E.g., "Big Apple" or "Twin Cities".
            rssUrl
                A link to a blob or an RSS feed associated with this microsite.
            service
                A comma separated list of likely search terms for the business. Used in meta-content for SEO. e.g. "plumbing, sewer service, commercial plumbing".
                This will validate the service arg accepts up to 3 items
                Supports lists.
            slug
                For a slug "this-is-the-slug", the url for the microsite becomes: "http://m.partner.com/this-is-the-slug/". One-to-one with a particular microsite. Can only be letters, numbers, and dashes. When the provided slug already exists for the host, a unique slug is generated. The desired slug is used in combination with either the sites city, zip or if all else fails a random identifier. E.g., if the slug "this-is-the-slug" exists, then a unique slug "this-is-the-slug-portland" is generated, and the url for the microsite becomes: "http://m.partner.com/this-is-the-slug-portland/
            spid
                A social profile identifier from core that the site is associated with.
            ssoToken
                An SSO token to grant authorization for this site. Maximum length of 200.
            state
                A state name or 2 character code. It is also validated if valid country is provided.
            tagline
                A short tagline that appears in the header beneath the name.
            taxId
                must be a valid tax id
                Supports lists. Maximum instances: 3.
            twitterUrl
                A link to a twitter profile associated with this microsite.
            validateOnlyFlag
                If provided, the site will not actually be committed, but the arguments will be validated to ensure the site creation would succeed.
            website
                The company website, if one exists other than the microsite. Must start with http:// or https://
                must be a valid url
            workNumber
                The list of work phone number for the site. Should be formatted for display. If a valid country code is given, phone number is formatted according to local phone number format. E.g., if 'CA' is provided as country, phone number is formatted as (*************
                Supports lists.
            youtubeUrl
                A link to a YouTube channel associated with this microsite.
            zip
                A zip or postal code
        """

        if pid is None or (isinstance(pid, str) and pid == '') or pid == BLANK_ARG:
            raise ValueError('"pid" is required.')
        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        if isinstance(instagramUrl, list):
            raise ValueError('"instagramUrl" may not be a list.')

        if isinstance(validateOnlyFlag, list):
            raise ValueError('"validateOnlyFlag" may not be a list.')

        if isinstance(color, list):
            raise ValueError('"color" may not be a list.')

        if isinstance(customDomain, list):
            raise ValueError('"customDomain" may not be a list.')

        if isinstance(customerIdentifier, list):
            raise ValueError('"customerIdentifier" may not be a list.')

        self.validate_max_list_length('taxId', taxId, 3)

        if isinstance(agid, list):
            raise ValueError('"agid" may not be a list.')

        if isinstance(googleplusUrl, list):
            raise ValueError('"googleplusUrl" may not be a list.')

        if isinstance(linkedinUrl, list):
            raise ValueError('"linkedinUrl" may not be a list.')


        if isinstance(marketId, list):
            raise ValueError('"marketId" may not be a list.')

        if isinstance(announcement, list):
            raise ValueError('"announcement" may not be a list.')


        if isinstance(layout, list):
            raise ValueError('"layout" may not be a list.')


        if isinstance(tagline, list):
            raise ValueError('"tagline" may not be a list.')

        if isinstance(companyName, list):
            raise ValueError('"companyName" may not be a list.')

        if isinstance(pinterestUrl, list):
            raise ValueError('"pinterestUrl" may not be a list.')

        if isinstance(filename, list):
            raise ValueError('"filename" may not be a list.')

        if isinstance(state, list):
            raise ValueError('"state" may not be a list.')

        if isinstance(notificationUrl, list):
            raise ValueError('"notificationUrl" may not be a list.')

        if isinstance(ssoToken, list):
            raise ValueError('"ssoToken" may not be a list.')

        if isinstance(foursquareUrl, list):
            raise ValueError('"foursquareUrl" may not be a list.')

        if isinstance(email, list):
            raise ValueError('"email" may not be a list.')

        if isinstance(website, list):
            raise ValueError('"website" may not be a list.')

        if isinstance(city, list):
            raise ValueError('"city" may not be a list.')

        if isinstance(twitterUrl, list):
            raise ValueError('"twitterUrl" may not be a list.')

        if isinstance(youtubeUrl, list):
            raise ValueError('"youtubeUrl" may not be a list.')

        if isinstance(hours, list):
            raise ValueError('"hours" may not be a list.')

        if isinstance(latitude, list):
            raise ValueError('"latitude" may not be a list.')
        if latitude is not None:
            for value in latitude if isinstance(latitude, list) else [latitude]:
                if value is not BLANK_ARG:
                    try:
                        float(value)
                    except ValueError:
                        raise ValueError('"latitude": %s is not an float' % value)

        if isinstance(host, list):
            raise ValueError('"host" may not be a list.')

        if isinstance(spid, list):
            raise ValueError('"spid" may not be a list.')

        if isinstance(address, list):
            raise ValueError('"address" may not be a list.')

        if isinstance(jobId, list):
            raise ValueError('"jobId" may not be a list.')

        if isinstance(slug, list):
            raise ValueError('"slug" may not be a list.')

        if isinstance(zip, list):
            raise ValueError('"zip" may not be a list.')

        if isinstance(country, list):
            raise ValueError('"country" may not be a list.')

        if isinstance(longitude, list):
            raise ValueError('"longitude" may not be a list.')
        if longitude is not None:
            for value in longitude if isinstance(longitude, list) else [longitude]:
                if value is not BLANK_ARG:
                    try:
                        float(value)
                    except ValueError:
                        raise ValueError('"longitude": %s is not an float' % value)

        if isinstance(facebookUrl, list):
            raise ValueError('"facebookUrl" may not be a list.')

        if isinstance(place, list):
            raise ValueError('"place" may not be a list.')

        if isinstance(logoUrl, list):
            raise ValueError('"logoUrl" may not be a list.')

        if isinstance(rssUrl, list):
            raise ValueError('"rssUrl" may not be a list.')

        if isinstance(identifier, list):
            raise ValueError('"identifier" may not be a list.')

        self.validate_max_list_length('page', page, 15)

        params = {}
        if pid is not None:
            params['pid'] = pid
        if instagramUrl is not None:
            params['instagramUrl'] = instagramUrl
        if validateOnlyFlag is not None:
            params['validateOnlyFlag'] = validateOnlyFlag
        if color is not None:
            params['color'] = color
        if customDomain is not None:
            params['customDomain'] = customDomain
        if customerIdentifier is not None:
            params['customerIdentifier'] = customerIdentifier
        if taxId is not None:
            params['taxId'] = taxId
        if agid is not None:
            params['agid'] = agid
        if googleplusUrl is not None:
            params['googleplusUrl'] = googleplusUrl
        if linkedinUrl is not None:
            params['linkedinUrl'] = linkedinUrl
        if callTrackingNumber is not None:
            params['callTrackingNumber'] = callTrackingNumber
        if marketId is not None:
            params['marketId'] = marketId
        if announcement is not None:
            params['announcement'] = announcement
        if workNumber is not None:
            params['workNumber'] = workNumber
        if layout is not None:
            params['layout'] = layout
        if service is not None:
            params['service'] = service
        if tagline is not None:
            params['tagline'] = tagline
        if companyName is not None:
            params['companyName'] = companyName
        if pinterestUrl is not None:
            params['pinterestUrl'] = pinterestUrl
        if filename is not None:
            params['filename'] = filename
        if state is not None:
            params['state'] = state
        if notificationUrl is not None:
            params['notificationUrl'] = notificationUrl
        if ssoToken is not None:
            params['ssoToken'] = ssoToken
        if foursquareUrl is not None:
            params['foursquareUrl'] = foursquareUrl
        if email is not None:
            params['email'] = email
        if website is not None:
            params['website'] = website
        if city is not None:
            params['city'] = city
        if twitterUrl is not None:
            params['twitterUrl'] = twitterUrl
        if youtubeUrl is not None:
            params['youtubeUrl'] = youtubeUrl
        if hours is not None:
            params['hours'] = hours
        if latitude is not None:
            params['latitude'] = latitude
        if host is not None:
            params['host'] = host
        if spid is not None:
            params['spid'] = spid
        if address is not None:
            params['address'] = address
        if jobId is not None:
            params['jobId'] = jobId
        if slug is not None:
            params['slug'] = slug
        if zip is not None:
            params['zip'] = zip
        if country is not None:
            params['country'] = country
        if longitude is not None:
            params['longitude'] = longitude
        if facebookUrl is not None:
            params['facebookUrl'] = facebookUrl
        if place is not None:
            params['place'] = place
        if logoUrl is not None:
            params['logoUrl'] = logoUrl
        if rssUrl is not None:
            params['rssUrl'] = rssUrl
        if identifier is not None:
            params['identifier'] = identifier
        if page is not None:
            params['page'] = page
        path = '/internalApi/v3/site/create/'
        cache_key = cache_time = None
        api_response = yield self.doPostAsync(path, params=params, requires_https=False,
                                              cache_key=cache_key, cache_timeout=cache_time,
                                              cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)

    def deleteSiteV3(self, msid=None, pid=None):
        """
        Delete a microsite fully.

        Kwargs:
            msid (required)
                A short system defined ID assigned to a microsite during creation process
                An internal identifier that is unique across the entire product.
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
        """

        return self.deleteSiteV3Async(msid=msid, pid=pid).get_result()

    @ndb.tasklet
    def deleteSiteV3Async(self, msid=None, pid=None):
        """
        Delete a microsite fully.

        Kwargs:
            msid (required)
                A short system defined ID assigned to a microsite during creation process
                An internal identifier that is unique across the entire product.
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
        """

        if pid is None or (isinstance(pid, str) and pid == '') or pid == BLANK_ARG:
            raise ValueError('"pid" is required.')
        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        if msid is None or (isinstance(msid, str) and msid == '') or msid == BLANK_ARG:
            raise ValueError('"msid" is required.')
        if isinstance(msid, list):
            raise ValueError('"msid" may not be a list.')

        params = {}
        if pid is not None:
            params['pid'] = pid
        if msid is not None:
            params['msid'] = msid
        path = '/internalApi/v3/site/delete/'
        cache_key = cache_time = None
        api_response = yield self.doPostAsync(path, params=params, requires_https=False,
                                              cache_key=cache_key, cache_timeout=cache_time,
                                              cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)

    def getReviewUrlV3(self, agid=None, msid=None, pid=None, reviewId=None):
        """
        Retrieve the review page URL for a microsite.

        Kwargs:
            agid (required)
                Must not be specified with [msid].
            msid (required)
                Must not be specified with [agid].
            pid (required)
                Validate partner argument
            reviewId (required)
                This parameter is unvalidated.
        """

        return self.getReviewUrlV3Async(agid=agid, msid=msid, pid=pid, reviewId=reviewId).get_result()

    @ndb.tasklet
    def getReviewUrlV3Async(self, agid=None, msid=None, pid=None, reviewId=None):
        """
        Retrieve the review page URL for a microsite.

        Kwargs:
            agid (required)
                Must not be specified with [msid].
            msid (required)
                Must not be specified with [agid].
            pid (required)
                Validate partner argument
            reviewId (required)
                This parameter is unvalidated.
        """

        if reviewId is None or (isinstance(reviewId, str) and reviewId == '') or reviewId == BLANK_ARG:
            raise ValueError('"reviewId" is required.')
        if isinstance(reviewId, list):
            raise ValueError('"reviewId" may not be a list.')

        if pid is None or (isinstance(pid, str) and pid == '') or pid == BLANK_ARG:
            raise ValueError('"pid" is required.')
        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        exclusive_iterable = (exclusive_arg for exclusive_arg in [msid, agid] if exclusive_arg is not None)
        if any(exclusive_iterable) and any(exclusive_iterable):
            raise ValueError("Only one of [msid, agid] may be specified.")
        if not any(exclusive_arg for exclusive_arg in [msid, agid] if exclusive_arg is not None and not (isinstance(exclusive_arg, str) and exclusive_arg == '') and exclusive_arg != BLANK_ARG):
            raise ValueError("One of [msid, agid] is required.")
        if isinstance(msid, list):
            raise ValueError('"msid" may not be a list.')
        if isinstance(agid, list):
            raise ValueError('"agid" may not be a list.')

        params = {}
        if reviewId is not None:
            params['reviewId'] = reviewId
        if pid is not None:
            params['pid'] = pid
        if msid is not None:
            params['msid'] = msid
        if agid is not None:
            params['agid'] = agid
        path = '/internalApi/v3/site/getReviewUrl/'
        cache_key = cache_time = None
        api_response = yield self.doGetAsync(path, params=params, requires_https=False,
                                             cache_key=cache_key, cache_timeout=cache_time,
                                             cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)

    def getSiteV3(self, pid=None, agid=None, msid=None):
        """
        Retrieves a microsite using msid or agid

        Kwargs:
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
            agid
                A unique identifier for the account group the site is linked to
            msid
                A short system defined ID assigned to a microsite during creation process
                An internal identifier that is unique across the entire product.
        """

        return self.getSiteV3Async(pid=pid, agid=agid, msid=msid).get_result()

    @ndb.tasklet
    def getSiteV3Async(self, pid=None, agid=None, msid=None):
        """
        Retrieves a microsite using msid or agid

        Kwargs:
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
            agid
                A unique identifier for the account group the site is linked to
            msid
                A short system defined ID assigned to a microsite during creation process
                An internal identifier that is unique across the entire product.
        """

        if pid is None or (isinstance(pid, str) and pid == '') or pid == BLANK_ARG:
            raise ValueError('"pid" is required.')
        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        if isinstance(agid, list):
            raise ValueError('"agid" may not be a list.')

        if isinstance(msid, list):
            raise ValueError('"msid" may not be a list.')

        params = {}
        if pid is not None:
            params['pid'] = pid
        if agid is not None:
            params['agid'] = agid
        if msid is not None:
            params['msid'] = msid
        path = '/internalApi/v3/site/get/'
        cache_key = cache_time = None
        api_response = yield self.doGetAsync(path, params=params, requires_https=False,
                                             cache_key=cache_key, cache_timeout=cache_time,
                                             cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)

    def searchSiteV3(self, pid=None, query=None, pageSize=None):
        """
        Search for microsites using a custom query. Not including the query
        parameter will result in all the microsites under the specified partner
        being returned. The search actually returns search documents, which do
        not necessarily encapsulate all data about a microsite. However, the
        data encapsulated should be more than enough for most purposes.

        Kwargs:
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
            query
                Query used to search sites.
        """

        return self.searchSiteV3Async(pid=pid, query=query, pageSize=pageSize).get_result()

    @ndb.tasklet
    def searchSiteV3Async(self, pid=None, query=None, pageSize=None):
        """
        Search for microsites using a custom query. Not including the query
        parameter will result in all the microsites under the specified partner
        being returned. The search actually returns search documents, which do
        not necessarily encapsulate all data about a microsite. However, the
        data encapsulated should be more than enough for most purposes.

        Kwargs:
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
            query
                Query used to search sites.
        """

        if pid is None or (isinstance(pid, str) and pid == '') or pid == BLANK_ARG:
            raise ValueError('"pid" is required.')
        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        if isinstance(query, list):
            raise ValueError('"query" may not be a list.')

        params = {}
        if pid is not None:
            params['pid'] = pid
        if query is not None:
            params['query'] = query
        if pageSize is not None:
            params['pageSize'] = pageSize
        path = '/internalApi/v3/site/search/'
        cache_key = cache_time = None
        api_response = yield self.doGetAsync(path, params=params, requires_https=False,
                                             cache_key=cache_key, cache_timeout=cache_time,
                                             cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response, api_response.nextUrl, api_response.totalResults)

    def setBillingCodeV3(self, billingCode=None, msid=None, pid=None):
        """
        Set billing code to one of the product configuration string for a microsite

        Kwargs:
            billingCode (required)
                A billing code which should be one of the product configurations. Allowed billing codes: standard, social-posting, landing-page-only',
                Validate billing code argument
            msid (required)
                A short system defined ID assigned to a microsite during creation process
                An internal identifier that is unique across the entire product.
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
        """

        return self.setBillingCodeV3Async(billingCode=billingCode, msid=msid, pid=pid).get_result()

    @ndb.tasklet
    def setBillingCodeV3Async(self, billingCode=None, msid=None, pid=None):
        """
        Set billing code to one of the product configuration string for a microsite

        Kwargs:
            billingCode (required)
                A billing code which should be one of the product configurations. Allowed billing codes: standard, social-posting, landing-page-only',
                Validate billing code argument
            msid (required)
                A short system defined ID assigned to a microsite during creation process
                An internal identifier that is unique across the entire product.
            pid (required)
                A unique ID identifying a partner the API call is for
                Validate partner argument
        """

        if msid is None or (isinstance(msid, str) and msid == '') or msid == BLANK_ARG:
            raise ValueError('"msid" is required.')
        if isinstance(msid, list):
            raise ValueError('"msid" may not be a list.')

        if pid is None or (isinstance(pid, str) and pid == '') or pid == BLANK_ARG:
            raise ValueError('"pid" is required.')
        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        if billingCode is None or (isinstance(billingCode, str) and billingCode == '') or billingCode == BLANK_ARG:
            raise ValueError('"billingCode" is required.')
        if isinstance(billingCode, list):
            raise ValueError('"billingCode" may not be a list.')

        params = {}
        if msid is not None:
            params['msid'] = msid
        if pid is not None:
            params['pid'] = pid
        if billingCode is not None:
            params['billingCode'] = billingCode
        path = '/internalApi/v3/site/setBillingCode/'
        cache_key = cache_time = None
        api_response = yield self.doGetAsync(path, params=params, requires_https=False,
                                             cache_key=cache_key, cache_timeout=cache_time,
                                             cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)

    def updateSiteV3(self, msid=None, page=None, slug=None, address=None, callTrackingNumber=None, city=None, color=None, companyName=None, country=None, customDomain=None, customerIdentifier=None, email=None, facebookUrl=None, filename=None, foursquareUrl=None, googleplusUrl=None, host=None, hours=None, identifier=None, instagramUrl=None, jobId=None, latitude=None, layout=None, linkedinUrl=None, logoUrl=None, longitude=None, marketId=None, notificationUrl=None, pid=None, pinterestUrl=None, place=None, rssUrl=None, service=None, spid=None, ssoToken=None, state=None, tagline=None, taxId=None, twitterUrl=None, website=None, workNumber=None, youtubeUrl=None, zip=None):
        """
        A single microsite is specified using an msid and a JSON dictionary encoding. The dictionary for a single microsite
        contains all the details necessary to update the microsite; including titles, headings, images, videos, pages, etc.

        Kwargs:
            msid (required)
                A short system defined ID assigned to a microsite during creation process
                An internal identifier that is unique across the entire product.
            page (required)
                The pages attribute is used to identify the set of pages for the microsite, as a list of page dictionaries. You must have at least 1 page, but you may not exceed 15 pages per microsite. When updating a microsite page, the slug attribute is used to identify the existing page. If a slug is used that is not present, a new page with that slug will be created. If a slug that previously existed is not in the pages list, the old page will be deleted and will return a 404 on subsequent requests. If the slug matches an existing page, that page will be updated with the new values. <br /> <strong>Page dictionary attributes (* means required):</strong> <table> <tr> <td>slug*</td> <td>The page slug is the string that identifies the page within a microsite. If a slug changes, the old page will be deleted (and requests for it will return a 404), and a new page will be created. Can only be letters, numbers, and dashes.</td> </tr> <tr> <td>template*</td> <td>One of: "Custom", "Images", "Contact", "Videos", "Coupons". Identifies the type of page content. Additional page dictionary attributes may be specified depending on the selected template - see below. This field is case-sensitive.</td> </tr> <tr> <td>tab_name*</td> <td>The name that appears on the navigation item.</td> </tr> <tr> <td>h1*</td> <td>The content that appears in the h1 tag for the page. Do not include the <h1></h1> tags themselves.</td> </tr> <tr> <td>title*</td> <td>The content that appears in the meta title tag for the page. Do not include the <title></title> tags themselves.</td> </tr> <tr> <td>meta_keywords</td> <td>The content for the meta keywords tag for the page. Do not include the <meta> tag itself.</td> </tr> <tr> <td>meta_description</td> <td>The content for the meta description tag for the page. Do not include the <meta> tag itself. </td> </tr> <tr> <td>top_content</td> <td>Free content that appears before any content determined by the page template. </td> </tr> <tr> <td>bottom_content</td> <td>Free content that appears after any content determined by the page template.</td> </tr> <tr> <td>email</td> <td>Only for Contact template. This is the email address that will receive contact requests from this page.</td> </tr> <tr> <td>images</td> <td>List of image urls. Only for Images template. The images that should be displayed on this page. We will import these images into our system and resize them as appropriate. On subsequent imports, we will check to see if the image has been updated and re-import if necessary.</td> </tr> <tr> <td>mobile_gallery_style</td> <td>Only for Images template. How the images will be displayed on a mobile device. E.g., "table", "photoswipe". Default is "table".</td> </tr> <tr> <td>desktop_gallery_style</td> <td>Only for Images template. How the images will be displayed on a desktop computer. E.g., "table", "lightbox". Default is "table".</td> </tr> <tr> <td>videos</td> <td>List of video urls or video dictionaries. Only for Videos template. The videos that should be displayed on this page.</td> </tr> <tr> <td>coupons</td> <td>List of coupon dictionaries. Only for Coupons template. The coupons that should be displayed on this page.</td> </tr> </table> <br /> <strong>Default Page</strong> <br /> If a page is not supplied, the default page is as follows: <pre> "page": [{ 'slug': 'home', 'template': 'Custom', 'tab_name': 'Home', 'title': 'Home', 'h1': 'Home', }] </pre> <br /> <strong>Images</strong> <br /> The images attribute is used to specify a list of images for an Images page. The list can either be a list of string urls, or a list of dictionaries depending on how much you wish to specify. You can specify up to 20 images per Images page. JPG, GIF, PNG, and WEBP images are supported. An example images specification is as follows: <pre> "images": [ "http://www.example.com/img1.png", "http://www.example.com/img2.jpg", "http://www.example.com/img3.gif" ] </pre> These images will be imported into our system and served from our servers. On subsequent batch imports, we will check to see if the images have been updated and re-import them if necessary. It is an important optimization for the server serving the original images to emit Etag or Last-Modified headers. These headers will be used to determine if an image has been updated or not. <br /> <strong>Image dictionary attributes (* means required):</strong> <table> <tr> <td>url*</td> <td>The url for the image content. The image will be imported into our system.</td> </tr> <tr> <td>caption</td> <td>The caption for the image.</td> </tr> <tr> <td>alt_text</td> <td>The alt text for the image.</td> </tr> <tr> <td>title</td> <td>The title text for the image.</td> </tr> </table> <br /> <strong>Videos</strong> <br /> The videos attribute specifies a list of videos to appear on the Videos page. The list can either be a list of string urls, or a list of dictionaries depending on how much you wish to specify. Note that video and thumbnail content are served from the urls provided; these items are not imported into our system. <br /> <strong>Video dictionary attributes (* means required):</strong> <table> <tr> <td>url*</td> <td> The url for the video content. The video will be served from this location and not imported into our system. For YouTube urls, we will automatically retrieve thumbnails, titles, etc. if they are not specified. </td> </tr> <tr> <td>phone_url</td> <td> A secondary URL for a video that is served on the mobile site, the "url" is embedded while the "phone_url" is directly linked. </td> </tr> <tr> <td>thumbnail_url</td> <td> The url for a thumbnail for the video. The thumbnail will be served from this location and not imported into our system. </td> </tr> <tr> <td>title</td> <td>The title for the video.</td> </tr> <tr> <td>caption</td> <td>A caption or description for the video.</td> </tr> <tr> <td>duration</td> <td>The duration of the video.</td> </tr> </table> Minimally, the videos page attribute can be a simple list of urls to videos, like the following: <pre> "videos": [ "http://www.youtube.com/watch?v=zzfQwXEqYaI", "http://www.youtube.com/watch?v=ntT7v47RIds" ]</pre> Alternatively, if you want to specify thumbnails, captions, duration, etc., you can provide a list of video dictionaries: <pre> "videos": [ { "url": "http://www.example.com/vid1.mp4", "thumbnail_url": "http://www.example.com/th1.jpg", "title": "Watch us in action!" }, { "url": "http://www.example.com/vid2.mp4", "title": "Last Thursday's Outing" }] </pre> <br /> <strong>Coupons</strong> <br /> The coupons attribute specifies a list of coupons to appear on the Coupons page. The list is a list of coupon dictionaries. Note that the coupon image is served from the url provided; this item is not imported into our system. <br /> <strong>Coupon dictionary attributes (* means required):</strong> <table> <tr> <td>title*</td> <td> The title of the coupon. This is the minimal amount of data required to display a coupon. </td> </tr> <tr> <td>detail</td> <td>The detail text for the coupon giving additional information.</td> </tr> <tr> <td>image_url</td> <td>A small image that is displayed at the top of the coupon, it is served from this url and not imported. Maximum size 150x100 pixels. </td> </tr> <tr> <td>category</td> <td> The Category of the coupon, any text string is possible, can be empty. </td> </tr> <tr> <td>start</td> <td> The start date at which the coupon becomes active formatted as YYYY-MM-DD eg. 2011-05-25. </td> </tr> <tr> <td>end</td> <td> The end date after which the coupon is no longer available formatted the same way the start date is. </td> </tr> <tr> <td>expiry</td> <td> The expiry date of the coupon, the day when the customer can no longer use the coupon. </td> </tr> </table> This is an example how the list of coupon dictionaries can look: <pre> "coupons": [ { "end": "2011-11-30", "title": "Save 20 %", "detail": "On any purchase", "category" : "Auto", "expiry": "2011-11-30", "start": "2011-08-15", "image_url": "http://example.com/image.png" }, { "title": "Buy one get one free every day after 5pm" }] </pre>
                Supports lists.
            slug (required)
                For a slug "this-is-the-slug", the url for the microsite becomes: "http://m.partner.com/this-is-the-slug/". One-to-one with a particular microsite. Can only be letters, numbers, and dashes. When the provided slug already exists for the host, a unique slug is generated. The desired slug is used in combination with either the sites city, zip or if all else fails a random identifier. E.g., if the slug "this-is-the-slug" exists, then a unique slug "this-is-the-slug-portland" is generated, and the url for the microsite becomes: "http://m.partner.com/this-is-the-slug-portland/
            address
                A street address
            callTrackingNumber
                The list of call tracking numbers. Should be formatted for display. If a valid country code is given, phone number is formatted according to local phone number format. E.g., if 'CA' is provided as country, phone number is formatted as (*************
                Supports lists.
            city
                The name of a city
            color
                The primary color for the microsite. For hex values, you must include the "#" leading character. E.g., "#99CC00".
            companyName
                The name of the microsite appears in the header of each page.
            country
                A country name or 2 character code. If valid country name is provided, it is converted to corresponding country code.
            customDomain
                A custom domain e.g. "customDomain" : "m.abc.com". Appropriate setup on the customer side needs to be performed to make this active.
            customerIdentifier
                A partner's microsite identifier.
            email
                An e-mail address
            facebookUrl
                A link to a Facebook Page associated with this microsite.
            filename
            foursquareUrl
                A link to a Foursquare Venue associated with this microsite.
            googleplusUrl
                A link to a YouTube channel associated with this microsite.
            host
                Host for the slug to be appended to. Must be one of the valid partner's hosts that uses slugs. If not provided, the first host (default) will be used.
                Validates the host is one of the partner's configured hosts
            hours
                The hours attribute is used to specify the Hours of Operation section, if applicable. The attribute is a nested JSON dictionary, yielding something like the following: <pre> "hours": { "monday": {"open": "9:00 AM", "close": "5:00 AM"}, ...} </pre> Each day of the week can have an open and a close string, plus an open2 and a close2 string for split hours (e.g., "9:00AM - 12:00PM, 1:00PM - 5:00PM"). Additionally, each day of the week can support an arbitrary note. There are also some attributes specified globally: open24, holidays, and other. open24 is a boolean flag, and needs to be encoded like the following: <pre> "open24": true (or) "open24": false </pre> <strong>Hours dictionary attributes:</strong> <table> <tr> <td> monday, tuesday, wednesday, thursday, friday, saturday, sunday </td> <td> day of week dictionary (see below) </td> </tr> <tr> <td>open24</td> <td>boolean, either true or false</td> </tr> <tr> <td>holidays</td><td>string</td> </tr> <tr> <td>other</td><td>string</td> </tr> </table> <strong>Day of week dictionary attributes:</strong> <table> <tr> <td>open</td><td>Should be a locale-appropriate time. E.g., "9:00 AM", "1:30pm", "17:15", etc.</td></tr> <tr><td>close</td><td>As above</td><tr> <tr><td>open2</td><td>As above</td><tr> <tr><td>close2</td><td>As above</td><tr> <tr><td>note</td><td>An arbitrary note that can be used in addition to, or in place of, the above attributes.</td><tr> </table> In an hours dictionary specification, all fields are optional, which provides for a very flexible interface. Below is an example, though your approach may be very different. <pre> "hours": { "monday": { "open": "9:00 am", "close": "5:00 pm", "note": "closed for lunch" }, "tuesday": { "open": "9:00 am", "close": "5:00 pm" }, "friday": { "open": "9:00 am", "close": "5:00 pm" }, "wednesday": { "open": "9:00 am", "close": "5:00 pm" }, "thursday": { "open": "9:00 am", "close": "5:00 pm", "open2": "6:00pm", "close2": "9:00pm" }, "holiday": "Closed for stat holidays", "open24": false } </pre> Note the above example of split hours on Thursday.
            identifier
                A short ID for the microsite. Must be unique across the entire partner. One-to-one with a particular microsite.
                Validate that we can get an msid using the pmsid and pid/apiuser passed in.
            instagramUrl
                A link to a Instagram Profile associated with this microsite.
            jobId
                Custom identifier used to track requests through the pipeline to ease debugging.
            latitude
                E.g., "45.234887".
            layout
                The layout to use for the website. Choices are: 'short', 'tall', 'responsive', and 'location'.
            linkedinUrl
                A link to a LinkedIn Profile associated with this microsite.
            logoUrl
                E.g., "http://www.example.com/foo.png". The logo will be read and imported into our system. Each batch import will provoke a check to see if the image has been updated and requires re-import.
            longitude
                E.g., "-58.245234".
            marketId
                Determines which market is set for the account. Takes Market Key value from list of markets.
                Checks to make sure that the marketId being passed in exists for the partner
            notificationUrl
                Validates the notification URL
            pid
                A unique ID identifying a partner the API call is for
                Validate partner argument
            pinterestUrl
                A link to a Pinterest Profile associated with this microsite.
            place
                A colloquial name that may be used in keywords, etc. E.g., "Big Apple" or "Twin Cities".
            rssUrl
                A link to a blob or an RSS feed associated with this microsite.
            service
                A comma separated list of likely search terms for the business. Used in meta-content for SEO. e.g. "plumbing, sewer service, commercial plumbing".
                This will validate the service arg accepts up to 3 items
                Supports lists.
            spid
                A social profile identifier from core that the site is associated with.
            ssoToken
                An SSO token to grant authorization for this site. Maximum length of 200.
            state
                A state name or 2 character code. It is also validated if valid country is provided.
            tagline
                A short tagline that appears in the header beneath the name.
            taxId
                Ensure the number of tax id values provided are not greater than the maximum allowed.
                Supports lists. Maximum instances: 3.
            twitterUrl
                A link to a twitter profile associated with this microsite.
            website
                The company website, if one exists other than the microsite. Must start with http:// or https://
            workNumber
                The list of work phone number for the site. Should be formatted for display. If a valid country code is given, phone number is formatted according to local phone number format. E.g., if 'CA' is provided as country, phone number is formatted as (*************
                Supports lists.
            youtubeUrl
                A link to a YouTube channel associated with this microsite.
            zip
                A zip or postal code
        """

        return self.updateSiteV3Async(msid=msid, page=page, slug=slug, address=address, callTrackingNumber=callTrackingNumber, city=city, color=color, companyName=companyName, country=country, customDomain=customDomain, customerIdentifier=customerIdentifier, email=email, facebookUrl=facebookUrl, filename=filename, foursquareUrl=foursquareUrl, googleplusUrl=googleplusUrl, host=host, hours=hours, identifier=identifier, instagramUrl=instagramUrl, jobId=jobId, latitude=latitude, layout=layout, linkedinUrl=linkedinUrl, logoUrl=logoUrl, longitude=longitude, marketId=marketId, notificationUrl=notificationUrl, pid=pid, pinterestUrl=pinterestUrl, place=place, rssUrl=rssUrl, service=service, spid=spid, ssoToken=ssoToken, state=state, tagline=tagline, taxId=taxId, twitterUrl=twitterUrl, website=website, workNumber=workNumber, youtubeUrl=youtubeUrl, zip=zip).get_result()

    @ndb.tasklet
    def updateSiteV3Async(self, msid=None, page=None, slug=None, address=None, callTrackingNumber=None, city=None, color=None, companyName=None, country=None, customDomain=None, customerIdentifier=None, email=None, facebookUrl=None, filename=None, foursquareUrl=None, googleplusUrl=None, host=None, hours=None, identifier=None, instagramUrl=None, jobId=None, latitude=None, layout=None, linkedinUrl=None, logoUrl=None, longitude=None, marketId=None, notificationUrl=None, pid=None, pinterestUrl=None, place=None, rssUrl=None, service=None, spid=None, ssoToken=None, state=None, tagline=None, taxId=None, twitterUrl=None, website=None, workNumber=None, youtubeUrl=None, zip=None):
        """
        A single microsite is specified using an msid and a JSON dictionary encoding. The dictionary for a single microsite
        contains all the details necessary to update the microsite; including titles, headings, images, videos, pages, etc.

        Kwargs:
            msid (required)
                A short system defined ID assigned to a microsite during creation process
                An internal identifier that is unique across the entire product.
            page (required)
                The pages attribute is used to identify the set of pages for the microsite, as a list of page dictionaries. You must have at least 1 page, but you may not exceed 15 pages per microsite. When updating a microsite page, the slug attribute is used to identify the existing page. If a slug is used that is not present, a new page with that slug will be created. If a slug that previously existed is not in the pages list, the old page will be deleted and will return a 404 on subsequent requests. If the slug matches an existing page, that page will be updated with the new values. <br /> <strong>Page dictionary attributes (* means required):</strong> <table> <tr> <td>slug*</td> <td>The page slug is the string that identifies the page within a microsite. If a slug changes, the old page will be deleted (and requests for it will return a 404), and a new page will be created. Can only be letters, numbers, and dashes.</td> </tr> <tr> <td>template*</td> <td>One of: "Custom", "Images", "Contact", "Videos", "Coupons". Identifies the type of page content. Additional page dictionary attributes may be specified depending on the selected template - see below. This field is case-sensitive.</td> </tr> <tr> <td>tab_name*</td> <td>The name that appears on the navigation item.</td> </tr> <tr> <td>h1*</td> <td>The content that appears in the h1 tag for the page. Do not include the <h1></h1> tags themselves.</td> </tr> <tr> <td>title*</td> <td>The content that appears in the meta title tag for the page. Do not include the <title></title> tags themselves.</td> </tr> <tr> <td>meta_keywords</td> <td>The content for the meta keywords tag for the page. Do not include the <meta> tag itself.</td> </tr> <tr> <td>meta_description</td> <td>The content for the meta description tag for the page. Do not include the <meta> tag itself. </td> </tr> <tr> <td>top_content</td> <td>Free content that appears before any content determined by the page template. </td> </tr> <tr> <td>bottom_content</td> <td>Free content that appears after any content determined by the page template.</td> </tr> <tr> <td>email</td> <td>Only for Contact template. This is the email address that will receive contact requests from this page.</td> </tr> <tr> <td>images</td> <td>List of image urls. Only for Images template. The images that should be displayed on this page. We will import these images into our system and resize them as appropriate. On subsequent imports, we will check to see if the image has been updated and re-import if necessary.</td> </tr> <tr> <td>mobile_gallery_style</td> <td>Only for Images template. How the images will be displayed on a mobile device. E.g., "table", "photoswipe". Default is "table".</td> </tr> <tr> <td>desktop_gallery_style</td> <td>Only for Images template. How the images will be displayed on a desktop computer. E.g., "table", "lightbox". Default is "table".</td> </tr> <tr> <td>videos</td> <td>List of video urls or video dictionaries. Only for Videos template. The videos that should be displayed on this page.</td> </tr> <tr> <td>coupons</td> <td>List of coupon dictionaries. Only for Coupons template. The coupons that should be displayed on this page.</td> </tr> </table> <br /> <strong>Default Page</strong> <br /> If a page is not supplied, the default page is as follows: <pre> "page": [{ 'slug': 'home', 'template': 'Custom', 'tab_name': 'Home', 'title': 'Home', 'h1': 'Home', }] </pre> <br /> <strong>Images</strong> <br /> The images attribute is used to specify a list of images for an Images page. The list can either be a list of string urls, or a list of dictionaries depending on how much you wish to specify. You can specify up to 20 images per Images page. JPG, GIF, PNG, and WEBP images are supported. An example images specification is as follows: <pre> "images": [ "http://www.example.com/img1.png", "http://www.example.com/img2.jpg", "http://www.example.com/img3.gif" ] </pre> These images will be imported into our system and served from our servers. On subsequent batch imports, we will check to see if the images have been updated and re-import them if necessary. It is an important optimization for the server serving the original images to emit Etag or Last-Modified headers. These headers will be used to determine if an image has been updated or not. <br /> <strong>Image dictionary attributes (* means required):</strong> <table> <tr> <td>url*</td> <td>The url for the image content. The image will be imported into our system.</td> </tr> <tr> <td>caption</td> <td>The caption for the image.</td> </tr> <tr> <td>alt_text</td> <td>The alt text for the image.</td> </tr> <tr> <td>title</td> <td>The title text for the image.</td> </tr> </table> <br /> <strong>Videos</strong> <br /> The videos attribute specifies a list of videos to appear on the Videos page. The list can either be a list of string urls, or a list of dictionaries depending on how much you wish to specify. Note that video and thumbnail content are served from the urls provided; these items are not imported into our system. <br /> <strong>Video dictionary attributes (* means required):</strong> <table> <tr> <td>url*</td> <td> The url for the video content. The video will be served from this location and not imported into our system. For YouTube urls, we will automatically retrieve thumbnails, titles, etc. if they are not specified. </td> </tr> <tr> <td>phone_url</td> <td> A secondary URL for a video that is served on the mobile site, the "url" is embedded while the "phone_url" is directly linked. </td> </tr> <tr> <td>thumbnail_url</td> <td> The url for a thumbnail for the video. The thumbnail will be served from this location and not imported into our system. </td> </tr> <tr> <td>title</td> <td>The title for the video.</td> </tr> <tr> <td>caption</td> <td>A caption or description for the video.</td> </tr> <tr> <td>duration</td> <td>The duration of the video.</td> </tr> </table> Minimally, the videos page attribute can be a simple list of urls to videos, like the following: <pre> "videos": [ "http://www.youtube.com/watch?v=zzfQwXEqYaI", "http://www.youtube.com/watch?v=ntT7v47RIds" ]</pre> Alternatively, if you want to specify thumbnails, captions, duration, etc., you can provide a list of video dictionaries: <pre> "videos": [ { "url": "http://www.example.com/vid1.mp4", "thumbnail_url": "http://www.example.com/th1.jpg", "title": "Watch us in action!" }, { "url": "http://www.example.com/vid2.mp4", "title": "Last Thursday's Outing" }] </pre> <br /> <strong>Coupons</strong> <br /> The coupons attribute specifies a list of coupons to appear on the Coupons page. The list is a list of coupon dictionaries. Note that the coupon image is served from the url provided; this item is not imported into our system. <br /> <strong>Coupon dictionary attributes (* means required):</strong> <table> <tr> <td>title*</td> <td> The title of the coupon. This is the minimal amount of data required to display a coupon. </td> </tr> <tr> <td>detail</td> <td>The detail text for the coupon giving additional information.</td> </tr> <tr> <td>image_url</td> <td>A small image that is displayed at the top of the coupon, it is served from this url and not imported. Maximum size 150x100 pixels. </td> </tr> <tr> <td>category</td> <td> The Category of the coupon, any text string is possible, can be empty. </td> </tr> <tr> <td>start</td> <td> The start date at which the coupon becomes active formatted as YYYY-MM-DD eg. 2011-05-25. </td> </tr> <tr> <td>end</td> <td> The end date after which the coupon is no longer available formatted the same way the start date is. </td> </tr> <tr> <td>expiry</td> <td> The expiry date of the coupon, the day when the customer can no longer use the coupon. </td> </tr> </table> This is an example how the list of coupon dictionaries can look: <pre> "coupons": [ { "end": "2011-11-30", "title": "Save 20 %", "detail": "On any purchase", "category" : "Auto", "expiry": "2011-11-30", "start": "2011-08-15", "image_url": "http://example.com/image.png" }, { "title": "Buy one get one free every day after 5pm" }] </pre>
                Supports lists.
            slug (required)
                For a slug "this-is-the-slug", the url for the microsite becomes: "http://m.partner.com/this-is-the-slug/". One-to-one with a particular microsite. Can only be letters, numbers, and dashes. When the provided slug already exists for the host, a unique slug is generated. The desired slug is used in combination with either the sites city, zip or if all else fails a random identifier. E.g., if the slug "this-is-the-slug" exists, then a unique slug "this-is-the-slug-portland" is generated, and the url for the microsite becomes: "http://m.partner.com/this-is-the-slug-portland/
            address
                A street address
            callTrackingNumber
                The list of call tracking numbers. Should be formatted for display. If a valid country code is given, phone number is formatted according to local phone number format. E.g., if 'CA' is provided as country, phone number is formatted as (*************
                Supports lists.
            city
                The name of a city
            color
                The primary color for the microsite. For hex values, you must include the "#" leading character. E.g., "#99CC00".
            companyName
                The name of the microsite appears in the header of each page.
            country
                A country name or 2 character code. If valid country name is provided, it is converted to corresponding country code.
            customDomain
                A custom domain e.g. "customDomain" : "m.abc.com". Appropriate setup on the customer side needs to be performed to make this active.
            customerIdentifier
                A partner's microsite identifier.
            email
                An e-mail address
            facebookUrl
                A link to a Facebook Page associated with this microsite.
            filename
            foursquareUrl
                A link to a Foursquare Venue associated with this microsite.
            googleplusUrl
                A link to a YouTube channel associated with this microsite.
            host
                Host for the slug to be appended to. Must be one of the valid partner's hosts that uses slugs. If not provided, the first host (default) will be used.
                Validates the host is one of the partner's configured hosts
            hours
                The hours attribute is used to specify the Hours of Operation section, if applicable. The attribute is a nested JSON dictionary, yielding something like the following: <pre> "hours": { "monday": {"open": "9:00 AM", "close": "5:00 AM"}, ...} </pre> Each day of the week can have an open and a close string, plus an open2 and a close2 string for split hours (e.g., "9:00AM - 12:00PM, 1:00PM - 5:00PM"). Additionally, each day of the week can support an arbitrary note. There are also some attributes specified globally: open24, holidays, and other. open24 is a boolean flag, and needs to be encoded like the following: <pre> "open24": true (or) "open24": false </pre> <strong>Hours dictionary attributes:</strong> <table> <tr> <td> monday, tuesday, wednesday, thursday, friday, saturday, sunday </td> <td> day of week dictionary (see below) </td> </tr> <tr> <td>open24</td> <td>boolean, either true or false</td> </tr> <tr> <td>holidays</td><td>string</td> </tr> <tr> <td>other</td><td>string</td> </tr> </table> <strong>Day of week dictionary attributes:</strong> <table> <tr> <td>open</td><td>Should be a locale-appropriate time. E.g., "9:00 AM", "1:30pm", "17:15", etc.</td></tr> <tr><td>close</td><td>As above</td><tr> <tr><td>open2</td><td>As above</td><tr> <tr><td>close2</td><td>As above</td><tr> <tr><td>note</td><td>An arbitrary note that can be used in addition to, or in place of, the above attributes.</td><tr> </table> In an hours dictionary specification, all fields are optional, which provides for a very flexible interface. Below is an example, though your approach may be very different. <pre> "hours": { "monday": { "open": "9:00 am", "close": "5:00 pm", "note": "closed for lunch" }, "tuesday": { "open": "9:00 am", "close": "5:00 pm" }, "friday": { "open": "9:00 am", "close": "5:00 pm" }, "wednesday": { "open": "9:00 am", "close": "5:00 pm" }, "thursday": { "open": "9:00 am", "close": "5:00 pm", "open2": "6:00pm", "close2": "9:00pm" }, "holiday": "Closed for stat holidays", "open24": false } </pre> Note the above example of split hours on Thursday.
            identifier
                A short ID for the microsite. Must be unique across the entire partner. One-to-one with a particular microsite.
                Validate that we can get an msid using the pmsid and pid/apiuser passed in.
            instagramUrl
                A link to a Instagram Profile associated with this microsite.
            jobId
                Custom identifier used to track requests through the pipeline to ease debugging.
            latitude
                E.g., "45.234887".
            layout
                The layout to use for the website. Choices are: 'short', 'tall', 'responsive', and 'location'.
            linkedinUrl
                A link to a LinkedIn Profile associated with this microsite.
            logoUrl
                E.g., "http://www.example.com/foo.png". The logo will be read and imported into our system. Each batch import will provoke a check to see if the image has been updated and requires re-import.
            longitude
                E.g., "-58.245234".
            marketId
                Determines which market is set for the account. Takes Market Key value from list of markets.
                Checks to make sure that the marketId being passed in exists for the partner
            notificationUrl
                Validates the notification URL
            pid
                A unique ID identifying a partner the API call is for
                Validate partner argument
            pinterestUrl
                A link to a Pinterest Profile associated with this microsite.
            place
                A colloquial name that may be used in keywords, etc. E.g., "Big Apple" or "Twin Cities".
            rssUrl
                A link to a blob or an RSS feed associated with this microsite.
            service
                A comma separated list of likely search terms for the business. Used in meta-content for SEO. e.g. "plumbing, sewer service, commercial plumbing".
                This will validate the service arg accepts up to 3 items
                Supports lists.
            spid
                A social profile identifier from core that the site is associated with.
            ssoToken
                An SSO token to grant authorization for this site. Maximum length of 200.
            state
                A state name or 2 character code. It is also validated if valid country is provided.
            tagline
                A short tagline that appears in the header beneath the name.
            taxId
                Ensure the number of tax id values provided are not greater than the maximum allowed.
                Supports lists. Maximum instances: 3.
            twitterUrl
                A link to a twitter profile associated with this microsite.
            website
                The company website, if one exists other than the microsite. Must start with http:// or https://
            workNumber
                The list of work phone number for the site. Should be formatted for display. If a valid country code is given, phone number is formatted according to local phone number format. E.g., if 'CA' is provided as country, phone number is formatted as (*************
                Supports lists.
            youtubeUrl
                A link to a YouTube channel associated with this microsite.
            zip
                A zip or postal code
        """

        if msid is None or (isinstance(msid, str) and msid == '') or msid == BLANK_ARG:
            raise ValueError('"msid" is required.')
        if isinstance(msid, list):
            raise ValueError('"msid" may not be a list.')

        if slug is None or (isinstance(slug, str) and slug == '') or slug == BLANK_ARG:
            raise ValueError('"slug" is required.')
        if isinstance(slug, list):
            raise ValueError('"slug" may not be a list.')

        if page is None or (isinstance(page, str) and page == '') or page == BLANK_ARG:
            raise ValueError('"page" is required.')

        if isinstance(instagramUrl, list):
            raise ValueError('"instagramUrl" may not be a list.')

        if isinstance(color, list):
            raise ValueError('"color" may not be a list.')

        if isinstance(customDomain, list):
            raise ValueError('"customDomain" may not be a list.')

        if isinstance(customerIdentifier, list):
            raise ValueError('"customerIdentifier" may not be a list.')

        if isinstance(pid, list):
            raise ValueError('"pid" may not be a list.')

        self.validate_max_list_length('taxId', taxId, 3)

        if isinstance(jobId, list):
            raise ValueError('"jobId" may not be a list.')

        if isinstance(googleplusUrl, list):
            raise ValueError('"googleplusUrl" may not be a list.')

        if isinstance(linkedinUrl, list):
            raise ValueError('"linkedinUrl" may not be a list.')


        if isinstance(marketId, list):
            raise ValueError('"marketId" may not be a list.')

        if isinstance(city, list):
            raise ValueError('"city" may not be a list.')


        if isinstance(layout, list):
            raise ValueError('"layout" may not be a list.')

        if isinstance(zip, list):
            raise ValueError('"zip" may not be a list.')

        if isinstance(tagline, list):
            raise ValueError('"tagline" may not be a list.')

        if isinstance(pinterestUrl, list):
            raise ValueError('"pinterestUrl" may not be a list.')

        if isinstance(filename, list):
            raise ValueError('"filename" may not be a list.')

        if isinstance(state, list):
            raise ValueError('"state" may not be a list.')

        if isinstance(notificationUrl, list):
            raise ValueError('"notificationUrl" may not be a list.')

        if isinstance(ssoToken, list):
            raise ValueError('"ssoToken" may not be a list.')

        if isinstance(foursquareUrl, list):
            raise ValueError('"foursquareUrl" may not be a list.')

        if isinstance(email, list):
            raise ValueError('"email" may not be a list.')

        if isinstance(website, list):
            raise ValueError('"website" may not be a list.')

        if isinstance(companyName, list):
            raise ValueError('"companyName" may not be a list.')

        if isinstance(twitterUrl, list):
            raise ValueError('"twitterUrl" may not be a list.')

        if isinstance(youtubeUrl, list):
            raise ValueError('"youtubeUrl" may not be a list.')

        if isinstance(hours, list):
            raise ValueError('"hours" may not be a list.')

        if isinstance(latitude, list):
            raise ValueError('"latitude" may not be a list.')
        if latitude is not None:
            for value in latitude if isinstance(latitude, list) else [latitude]:
                if value is not BLANK_ARG:
                    try:
                        float(value)
                    except ValueError:
                        raise ValueError('"latitude": %s is not an float' % value)

        if isinstance(host, list):
            raise ValueError('"host" may not be a list.')

        if isinstance(spid, list):
            raise ValueError('"spid" may not be a list.')

        if isinstance(address, list):
            raise ValueError('"address" may not be a list.')


        if isinstance(country, list):
            raise ValueError('"country" may not be a list.')

        if isinstance(longitude, list):
            raise ValueError('"longitude" may not be a list.')
        if longitude is not None:
            for value in longitude if isinstance(longitude, list) else [longitude]:
                if value is not BLANK_ARG:
                    try:
                        float(value)
                    except ValueError:
                        raise ValueError('"longitude": %s is not an float' % value)

        if isinstance(facebookUrl, list):
            raise ValueError('"facebookUrl" may not be a list.')

        if isinstance(place, list):
            raise ValueError('"place" may not be a list.')

        if isinstance(logoUrl, list):
            raise ValueError('"logoUrl" may not be a list.')

        if isinstance(rssUrl, list):
            raise ValueError('"rssUrl" may not be a list.')

        if isinstance(identifier, list):
            raise ValueError('"identifier" may not be a list.')

        params = {}
        if msid is not None:
            params['msid'] = msid
        if slug is not None:
            params['slug'] = slug
        if page is not None:
            params['page'] = page
        if instagramUrl is not None:
            params['instagramUrl'] = instagramUrl
        if color is not None:
            params['color'] = color
        if customDomain is not None:
            params['customDomain'] = customDomain
        if customerIdentifier is not None:
            params['customerIdentifier'] = customerIdentifier
        if pid is not None:
            params['pid'] = pid
        if taxId is not None:
            params['taxId'] = taxId
        if jobId is not None:
            params['jobId'] = jobId
        if googleplusUrl is not None:
            params['googleplusUrl'] = googleplusUrl
        if linkedinUrl is not None:
            params['linkedinUrl'] = linkedinUrl
        if callTrackingNumber is not None:
            params['callTrackingNumber'] = callTrackingNumber
        if marketId is not None:
            params['marketId'] = marketId
        if city is not None:
            params['city'] = city
        if workNumber is not None:
            params['workNumber'] = workNumber
        if layout is not None:
            params['layout'] = layout
        if zip is not None:
            params['zip'] = zip
        if tagline is not None:
            params['tagline'] = tagline
        if pinterestUrl is not None:
            params['pinterestUrl'] = pinterestUrl
        if filename is not None:
            params['filename'] = filename
        if state is not None:
            params['state'] = state
        if notificationUrl is not None:
            params['notificationUrl'] = notificationUrl
        if ssoToken is not None:
            params['ssoToken'] = ssoToken
        if foursquareUrl is not None:
            params['foursquareUrl'] = foursquareUrl
        if email is not None:
            params['email'] = email
        if website is not None:
            params['website'] = website
        if companyName is not None:
            params['companyName'] = companyName
        if twitterUrl is not None:
            params['twitterUrl'] = twitterUrl
        if youtubeUrl is not None:
            params['youtubeUrl'] = youtubeUrl
        if hours is not None:
            params['hours'] = hours
        if latitude is not None:
            params['latitude'] = latitude
        if host is not None:
            params['host'] = host
        if spid is not None:
            params['spid'] = spid
        if address is not None:
            params['address'] = address
        if service is not None:
            params['service'] = service
        if country is not None:
            params['country'] = country
        if longitude is not None:
            params['longitude'] = longitude
        if facebookUrl is not None:
            params['facebookUrl'] = facebookUrl
        if place is not None:
            params['place'] = place
        if logoUrl is not None:
            params['logoUrl'] = logoUrl
        if rssUrl is not None:
            params['rssUrl'] = rssUrl
        if identifier is not None:
            params['identifier'] = identifier
        path = '/internalApi/v3/site/update/'
        cache_key = cache_time = None
        api_response = yield self.doPostAsync(path, params=params, requires_https=False,
                                              cache_key=cache_key, cache_timeout=cache_time,
                                              cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)


class SocialClient(AbstractClient):
    """
    These end-points are used for managing Social integration with Sites.
    """

    def addSiteToFBPageV2(self, facebookPageId=None, facebookUrl=None, facebookUserId=None, identifier=None, msid=None):
        """
        Adding an existing site to Facebook Page as More Info tab

        Kwargs:
            facebookPageId
                A Facebook page identifier.
                To use the current API, either facebookPageId or facebookUrl must be specified but not both.
            facebookUrl
                A Facebook page url
                To use the current API, either facebookPageId or facebookUrl must be specified but not both.
            facebookUserId
                A Facebook user identifier. Optional.
                Only needed when current partner has no Facebook master user defined and Facebook page ID is present.
            identifier
                A short customer defined ID for the microsite which identifies the microsite that is requested to
                be added to given Facebook page.
                To use the current API, either msid or identifier must be specified but not both.
            msid
                A short system defined ID assigned to a microsite during creation process.
                To use current API, either msid or identifier must be specified but not both.
        """

        return self.addSiteToFBPageV2Async(facebookPageId=facebookPageId, facebookUrl=facebookUrl, facebookUserId=facebookUserId, identifier=identifier, msid=msid).get_result()

    @ndb.tasklet
    def addSiteToFBPageV2Async(self, facebookPageId=None, facebookUrl=None, facebookUserId=None, identifier=None, msid=None):
        """
        Adding an existing site to Facebook Page as More Info tab

        Kwargs:
            facebookPageId
                A Facebook page identifier.
                To use the current API, either facebookPageId or facebookUrl must be specified but not both.
            facebookUrl
                A Facebook page url
                To use the current API, either facebookPageId or facebookUrl must be specified but not both.
            facebookUserId
                A Facebook user identifier. Optional.
                Only needed when current partner has no Facebook master user defined and Facebook page ID is present.
            identifier
                A short customer defined ID for the microsite which identifies the microsite that is requested to
                be added to given Facebook page.
                To use the current API, either msid or identifier must be specified but not both.
            msid
                A short system defined ID assigned to a microsite during creation process.
                To use current API, either msid or identifier must be specified but not both.
        """

        if isinstance(facebookPageId, list):
            raise ValueError('"facebookPageId" may not be a list.')

        if isinstance(facebookUserId, list):
            raise ValueError('"facebookUserId" may not be a list.')

        if isinstance(identifier, list):
            raise ValueError('"identifier" may not be a list.')

        if isinstance(facebookUrl, list):
            raise ValueError('"facebookUrl" may not be a list.')

        if isinstance(msid, list):
            raise ValueError('"msid" may not be a list.')

        params = {}
        if facebookPageId is not None:
            params['facebookPageId'] = facebookPageId
        if facebookUserId is not None:
            params['facebookUserId'] = facebookUserId
        if identifier is not None:
            params['identifier'] = identifier
        if facebookUrl is not None:
            params['facebookUrl'] = facebookUrl
        if msid is not None:
            params['msid'] = msid
        path = '/api/v2/facebookTab/add/'
        cache_key = cache_time = None
        api_response = yield self.doPostAsync(path, params=params, requires_https=True,
                                              cache_key=cache_key, cache_timeout=cache_time,
                                              cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)

    def removeSiteFromFacebookV2(self, facebookPageId=None, identifier=None, msid=None):
        """
        Removes the specified Site from the specified Facebook page. The More Info tab
        will be removed from the Facebook page.

        Kwargs:
            facebookPageId (required)
                A short ID for the Facebook page. Assigned by Facebook to each unique page.
            identifier
                A short ID for the microsite, must be unique
                across the entire import and must match previous imports for
                updates to work correctly.  One-to-one with a particular microsite.
            msid
                A short ID for the microsite. Assigned by VendAsta to each unique microsite.
        """

        return self.removeSiteFromFacebookV2Async(facebookPageId=facebookPageId, identifier=identifier, msid=msid).get_result()

    @ndb.tasklet
    def removeSiteFromFacebookV2Async(self, facebookPageId=None, identifier=None, msid=None):
        """
        Removes the specified Site from the specified Facebook page. The More Info tab
        will be removed from the Facebook page.

        Kwargs:
            facebookPageId (required)
                A short ID for the Facebook page. Assigned by Facebook to each unique page.
            identifier
                A short ID for the microsite, must be unique
                across the entire import and must match previous imports for
                updates to work correctly.  One-to-one with a particular microsite.
            msid
                A short ID for the microsite. Assigned by VendAsta to each unique microsite.
        """

        if facebookPageId is None or (isinstance(facebookPageId, str) and facebookPageId == '') or facebookPageId == BLANK_ARG:
            raise ValueError('"facebookPageId" is required.')
        if isinstance(facebookPageId, list):
            raise ValueError('"facebookPageId" may not be a list.')

        if isinstance(identifier, list):
            raise ValueError('"identifier" may not be a list.')

        if isinstance(msid, list):
            raise ValueError('"msid" may not be a list.')

        params = {}
        if facebookPageId is not None:
            params['facebookPageId'] = facebookPageId
        if identifier is not None:
            params['identifier'] = identifier
        if msid is not None:
            params['msid'] = msid
        path = '/api/v2/facebookTab/remove/'
        cache_key = cache_time = None
        api_response = yield self.doPostAsync(path, params=params, requires_https=True,
                                              cache_key=cache_key, cache_timeout=cache_time,
                                              cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)


class UnifiedBillingClient(AbstractClient):
    """
    V1 API End-points
    """

    def getBillableAccountsQueriesV3(self, reportMonth=None):
        """
        Returns the queries to get the billable accounts for unified billing.

        Kwargs:
            reportMonth
                The month up to which accounts created will be queried for billing. Eg. "2013-08" will return all accounts created prior to August 1, 2013
        """

        return self.getBillableAccountsQueriesV3Async(reportMonth=reportMonth).get_result()

    @ndb.tasklet
    def getBillableAccountsQueriesV3Async(self, reportMonth=None):
        """
        Returns the queries to get the billable accounts for unified billing.

        Kwargs:
            reportMonth
                The month up to which accounts created will be queried for billing. Eg. "2013-08" will return all accounts created prior to August 1, 2013
        """

        if isinstance(reportMonth, list):
            raise ValueError('"reportMonth" may not be a list.')

        params = {}
        if reportMonth is not None:
            params['reportMonth'] = reportMonth
        path = '/internalApi/v3/billing/getBillableAccountsQueries/'
        cache_key = cache_time = None
        api_response = yield self.doGetAsync(path, params=params, requires_https=False,
                                             cache_key=cache_key, cache_timeout=cache_time,
                                             cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)

    def getCurrentAccountsQueriesV3(self):
        """
        Return a query to fetch current accounts.
        Non-billable accounts should be included but deleted accounts should not.
        """

        return self.getCurrentAccountsQueriesV3Async().get_result()

    @ndb.tasklet
    def getCurrentAccountsQueriesV3Async(self):
        """
        Return a query to fetch current accounts.
        Non-billable accounts should be included but deleted accounts should not.
        """

        params = {}
        path = '/internalApi/v3/billing/getCurrentAccountsQueries/'
        cache_key = cache_time = None
        api_response = yield self.doGetAsync(path, params=params, requires_https=False,
                                             cache_key=cache_key, cache_timeout=cache_time,
                                             cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)

    def getProductConfigurationsV3(self):
        """
        Returns the product configurations for unified billing.
        """

        return self.getProductConfigurationsV3Async().get_result()

    @ndb.tasklet
    def getProductConfigurationsV3Async(self):
        """
        Returns the product configurations for unified billing.
        """

        params = {}
        path = '/internalApi/v3/billing/getProductConfigurations/'
        cache_key = cache_time = None
        api_response = yield self.doGetAsync(path, params=params, requires_https=False,
                                             cache_key=cache_key, cache_timeout=cache_time,
                                             cache_use_datastore=False, cache_use_ndb_memcache=False)
        response_class = None
        response_vobject = None
        formatted_response = self.format_response(api_response.data, response_class=response_class, response_vobject=response_vobject, response_dict_key="")
        raise ndb.Return(formatted_response)
