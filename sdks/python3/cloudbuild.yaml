steps:
- name: gcr.io/repcore-prod/build-python-appengine
  dir: /workspace/sdks/python3
  script: |
    #!/usr/bin/env bash
    pip install --prefer-binary -r requirements.txt --user --no-warn-script-location
    python3 -m build . --sdist --wheel --outdir dist/
    python3 -m twine upload --repository-url https://us-central1-python.pkg.dev/repcore-prod/python/ dist/*

options:
  logging: CLOUD_LOGGING_ONLY 