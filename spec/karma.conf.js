// Karma configuration
// Generated on Thu May 29 2014 11:08:50 GMT-0600 (CST)

module.exports = function(config) {
  config.set({

    // base path, that will be used to resolve files and exclude
    basePath: '',


    // frameworks to use
    frameworks: ['jasmine'],

    // list of files / patterns to load in the browser
    files: [
      'https://www.cdnstyles.com/static/js/jquery-1.9.1.min.js',
      'https://www.cdnstyles.com/static/js/jquery-ui-1.10.3.min.js',
      'https://www.cdnstyles.com/static/js/inheritance.js',
      'https://www.cdnstyles.com/static/js/knockout-3.2.0.js',
      'https://www.cdnstyles.com/static/js/knockout-postbox.min.js',
      'https://www.cdnstyles.com/static/js/tooltip.js',
      'https://www.cdnstyles.com/static/js/json2.js',
      'https://www.cdnstyles.com/static/js/vendor/jquery.gritter.min.js',
      'https://www.cdnstyles.com/static/js/vendasta.gritter.js',
      'https://www.cdnstyles.com/static/js/moment.js',
      'https://www.cdnstyles.com/static/js/highcharts-4.0.1.min.js',
      'https://www.cdnstyles.com/static/js/vendor/zero-clipboard/ZeroClipboard.min.js',
      '../src/static/js/MS.Dashboard.js',
      '../src/static/js/MS.AreaChart.js',
      '../src/static/js/MS.ListingStats.js',
      '../src/static/js/MS.Listings.js',
      '../src/static/js/MS.AddReview.js',
      '../src/static/js/MS.GoogleMyBusiness.js',
      '../src/static/js/google-insights/authed.js',
      '../src/static/js/MS.SocialService.js',
      '../src/static/js/MS.ListingSyncPro.js',
      '../src/static/js/ga-utils.js',
      '../src/static/js/utils.js',
      '../src/static/js/admin/website.js',
      '../src/static/js/cloneable.js',
      '../src/static/js/admin/image-uploader.js',
      '../src/static/js/components/gmb-column-chart.js',
      '../src/static/js/components/stacked-area-chart.js',
      '../src/lib/vform/js/forms.js',
      'lib/jasmine-jquery.js',
      '**/*Spec.js'
    ],


    // list of files to exclude
    exclude: [

    ],


    // test results reporter to use
    // possible values: 'dots', 'progress', 'junit', 'growl', 'coverage'
    reporters: ['progress'],
    junitReporter: {
      outputFile: 'jasmine-results.xml',
      outputDir: '../artifacts',
      useBrowserName: false
    },


    // web server port
    port: 9876,


    // enable / disable colors in the output (reporters and logs)
    colors: true,


    // level of logging
    // possible values: config.LOG_DISABLE || config.LOG_ERROR || config.LOG_WARN || config.LOG_INFO || config.LOG_DEBUG
    logLevel: config.LOG_INFO,


    // enable / disable watching file and executing tests whenever any file changes
    autoWatch: true,


    // Start these browsers, currently available:
    // - Chrome
    // - ChromeCanary
    // - Firefox
    // - Opera
    // - Safari (only Mac)
    // - PhantomJS
    // - IE (only Windows)
    browsers: ['Chrome'],


    // If browser does not capture in given timeout [ms], kill it
    captureTimeout: 60000,


    // Continuous Integration mode
    // if true, it capture browsers, run tests and exit
    singleRun: false
  });
};
