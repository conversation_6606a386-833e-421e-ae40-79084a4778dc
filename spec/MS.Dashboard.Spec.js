describe('MS.Dashboard', function() {

    describe('MS.Dashboard.DashboardItem', function() {

        beforeEach(function() {
            self.data = {
                'num_complete': 45,
                'num_total': 100,
                'type': 'xofy'
            };
            spyOn($.fn, 'highcharts');
        });

        describe('init', function () {
            it('should not call highcharts when chart related information not given', function () {
                var item = new DashboardItem(self.data);
                expect($.fn.highcharts).not.toHaveBeenCalled();
            });

            it('should call highcharts when chart related information are given', function () {
                self.data.chart_data = [2,3,4];
                self.data.chart_id = 'chart_id';
                var item = new DashboardChartItem(self.data);
                expect($.fn.highcharts).toHaveBeenCalled();
            });
        });

        describe('color', function() {
            it('should be red if less than or equal to 50 percent', function() {
                var item = new DashboardItem(self.data);
                expect(item.color()).toBe('#C1334E');
                self.data.percent = 50;
                var item = new DashboardItem(self.data);
                expect(item.color()).toBe('#C1334E');
            });

            it('should be yellow if percentage between 50 and 85', function() {
                self.data.num_complete = 51;
                var item = new DashboardItem(self.data);
                expect(item.color()).toBe('#F7BC08');
                self.data.num_complete = 85;
                var item = new DashboardItem(self.data);
                expect(item.color()).toBe('#F7BC08');
            });

            it('should be green if percentage between 85 and 100', function() {
                self.data.num_complete = 86;
                var item = new DashboardItem(self.data);
                expect(item.color()).toBe('#42C05C');
                self.data.num_complete = 100;
                var item = new DashboardItem(self.data);
                expect(item.color()).toBe('#42C05C');
            });

            describe('using SOCIAL_SYNC_PROGRESS_COLOR_BANDS as progress_color_bands', function() {

                beforeEach(function() {
                    self.progoress_bands = {progress_color_bands: SOCIAL_SYNC_PROGRESS_COLOR_BANDS};
                });

                it('should be green if it is 100 only', function() {
                    self.data.num_complete = 100;
                    var item = new DashboardItem(self.data, self.progoress_bands);
                    expect(item.color()).toBe('#42C05C');
                });

                it('should be red if it is less than 100', function() {
                    self.data.num_complete = 99;
                    var item = new DashboardItem(self.data, self.progoress_bands);
                    expect(item.color()).toBe('#C1334E');
                    self.data.num_complete = 50;
                    var item = new DashboardItem(self.data, self.progoress_bands);
                    expect(item.color()).toBe('#C1334E');
                });
            });
        });

    });
});