describe('image-uploader', function() {
    var self = this;
    beforeEach(function() {
        spyOn(MS.ImageUploader.prototype, 'init');
        $.gritter.error = jasmine.createSpy('error');
        self.imageUploader = new MS.ImageUploader();
    });

    describe('checkImageUploadAllowance', function () {
        beforeEach(function() {
            spyOn(self.imageUploader, 'showImageLimitReached');
            spyOn(self.imageUploader, 'showAddImageButton');
        });

        it('should call showImageLimitReached when clone count is greater than max image count', function () {
            spyOn(self.imageUploader, 'cloneCount').and.returnValue(21);
            self.imageUploader.maxImageCount = 20;
            self.imageUploader.checkImageUploadAllowance();
            expect(self.imageUploader.showImageLimitReached).toHaveBeenCalled();
        });

        it('should call showAddImageButton when clone count is less than max image count', function () {
            spyOn(self.imageUploader, 'cloneCount').and.returnValue(2);
            self.imageUploader.maxImageCount = 20;
            self.imageUploader.checkImageUploadAllowance();
            expect(self.imageUploader.showAddImageButton).toHaveBeenCalled();
        });

        it('should call showAddImageButton when clone count is equal to max image count', function () {
            spyOn(self.imageUploader, 'cloneCount').and.returnValue(20);
            self.imageUploader.maxImageCount = 20;
            self.imageUploader.checkImageUploadAllowance();
            expect(self.imageUploader.showAddImageButton).toHaveBeenCalled();
        });
    });

    describe('validateImageFile', function() {
        beforeEach(function() {
           self.file =  {'name': 'hello.jpg', 'type': 'jpg', 'size': 1000000};
        });
        it('should show gritter error if file size greater than 5MB', function() {
            self.file.size = 5100000;
            self.imageUploader.validateImageFile(self.file);
            expect($.gritter.error).toHaveBeenCalledWith('File is too big. Size must be less than 5MB');
        });
        it('should show gritter error if file type is invalid', function() {
            self.file.type = 'invalid-type';
            self.file.name = 'hello.invalid-type';
            self.imageUploader.validateImageFile(self.file);
            expect($.gritter.error).toHaveBeenCalledWith('Filetype is not allowed. Please upload an gif, jpeg, or png image.');
        });
    });

    describe('addFileUpload', function() {
        beforeEach(function() {
            spyOn(self.imageUploader, 'validateImageFile').and.returnValue(true);
            spyOn(self.imageUploader, 'showUploadProgress');
            spyOn(self.imageUploader, 'uploadFileData');
            spyOn($, 'ajax');
        });
        it('should show gritter error if data is invalid', function() {
            var data = undefined;
            self.imageUploader.addFileUpload(data);
            expect($.gritter.error).toHaveBeenCalled();
        });
        it('should show gritter error if data does not contain file', function() {
            var data = {};
            self.imageUploader.addFileUpload(data);
            expect($.gritter.error).toHaveBeenCalled();
        });
        it('should call validateImageFile for valid data', function() {
            var data = {'files': ['123']};
            self.imageUploader.addFileUpload(data);
            expect(self.imageUploader.validateImageFile).toHaveBeenCalled();
        });
        it('should call showUploadProgress for valid data', function() {
            var data = {'files': ['123']};
            self.imageUploader.addFileUpload(data);
            expect(self.imageUploader.showUploadProgress).toHaveBeenCalled();
        });
        it('should make ajax request to get upload url', function() {
            $.ajax.and.callFake(function (e) {
                e.success();
            });
            var data = {'files': ['123']};
            self.imageUploader.generateUploadUrl = 'fake_generate_upload_url';
            self.imageUploader.addFileUpload(data);
            expect($.ajax.calls.count()).toBe(1);
            expect($.ajax.calls.argsFor(0)[0].type).toBe('GET');
            expect($.ajax.calls.argsFor(0)[0].url).toBe('fake_generate_upload_url');
            expect(self.imageUploader.uploadFileData).toHaveBeenCalled();
        });
        it('should show gritter error if ajax request get error', function() {
            $.ajax.and.callFake(function (e) {
                e.error();
            });
            var data = {'files': ['123']};
            self.imageUploader.generateUploadUrl = 'fake_generate_upload_url';
            self.imageUploader.addFileUpload(data);
            expect($.gritter.error).toHaveBeenCalled();
        });
    });

    describe('failFileUpload', function() {
        beforeEach(function() {
            spyOn(self.imageUploader, 'validateImageFile').and.returnValue(true);
            spyOn(self.imageUploader, 'showUploadProgress');
            spyOn(self.imageUploader, 'uploadFileData');
            spyOn($, 'ajax');
        });
        it('should show gritter error', function() {
            var data = {};
            self.imageUploader.failFileUpload(data);
            expect($.gritter.error).toHaveBeenCalled();
        });
    });
});