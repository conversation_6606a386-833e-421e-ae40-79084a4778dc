describe('website', function() {
    var self = this;
    describe('ThemePreviewDialog', function() {
        beforeEach(function() {
            self.dialog = new ThemePreviewDialog();
        });

        describe('setTitle', function() {
            it('should set the title', function() {
                self.dialog.setTitle('This is my title');
                expect(self.dialog.modal.dialog('option', 'title')).toBe('This is my title');
            });
            it('should be able to set title multiple times', function() {
                self.dialog.setTitle('This is my title');
                self.dialog.setTitle('This is my next title');
                expect(self.dialog.modal.dialog('option', 'title')).toBe('This is my next title');
            });
        });

        describe('setPreviewImageCss', function() {
            it('should set the div with image css', function() {
                self.dialog.setPreviewImageCss('location-css');
                expect(self.dialog.modal.html()).toBe('<div class="location-css"></div>');
            });
            it('should set the div with image css multiple times', function() {
                self.dialog.setPreviewImageCss('location-css');
                self.dialog.setPreviewImageCss('short-css');
                expect(self.dialog.modal.html()).toBe('<div class="short-css"></div>');
            });
        });
    });

    describe('ThemeViewModel', function() {
        beforeEach(function() {
            spyOn($, 'ajax');
            $.gritter.success = jasmine.createSpy('success');
            $.gritter.error = jasmine.createSpy('error');

            self.themes = {
                'layout_responsive': {'desktop_theme': 'responsive_theme', 'name': 'Responsive', 'api_key': 'responsive', 'description': "Description of responsive layout", 'phone_theme': 'responsive_theme'},
                'layout_location': {'desktop_theme': 'location_theme', 'name': 'My Listing', 'api_key': 'location', 'description': "Decription of location layout", 'phone_theme': 'location_theme'},
                'layout_1': {'desktop_theme': 'desktop_theme1', 'name': 'Short Mobile', 'api_key': 'short', 'description': 'Description of short mobile layout', 'phone_theme': 'phone_theme1'},
                'layout_2': {'desktop_theme': 'desktop_theme1', 'name': 'Tall Mobile', 'api_key': 'tall', 'description': 'Description of tall mobile layout', 'phone_theme': 'phone_theme2'}};
            self.responsive_theme = new Theme('layout_responsive', self.themes['layout_responsive']);
            self.location_theme = new Theme('layout_location', self.themes['layout_location']);
            self.short_theme = new Theme('layout_1', self.themes['layout_1']);
            self.tall_theme = new Theme('layout_2', self.themes['layout_2']);
            self.i18n = '{"LAYOUT_UPDATED_SUCCESSFULLY": "Layout updated successfully.","LAYOUT_UPDATE_FAILED": "Layout update failed.","PREVIEW_OF_THEME": "Preview of {theme} Theme"}';

            self.checkThemeObj = function(theme1, theme2) {
                expect(theme1.id).toBe(theme2.id);
                expect(theme1.name).toBe(theme2.name);
                expect(theme1.description).toBe(theme2.description);
                expect(theme1.thumbnail_css).toBe(theme2.thumbnail_css);
                expect(theme1.preview_image_css).toBe(theme2.preview_image_css);
                expect(theme1.processing()).toBe(theme2.processing());
            };

            self.viewModel = new ThemeViewModel(self.themes, 'location_theme', 'fake_theme_update_url', self.i18n);
        });

        describe('selectedTheme', function() {
            it('should get the selected theme', function() {
                self.checkThemeObj(self.viewModel.selectedTheme(), self.location_theme);
            });

            it('should change the selected theme if we select another theme', function() {
                self.viewModel.selectedPhoneTheme('phone_theme2');
                self.checkThemeObj(self.viewModel.selectedTheme(), self.tall_theme);
            });
        });

        describe('otherThemes', function() {
            it('should have other themes', function() {
                expect(self.viewModel.otherThemes().length).toEqual(3);
            });
        });

        describe('chooseTheme', function() {
            it('should send a request to change the theme', function() {
                $.ajax.and.callFake(function (e) {
                    e.success();
                });
                self.viewModel.chooseTheme(self.responsive_theme);
                expect($.ajax).toHaveBeenCalled();
                expect($.ajax.calls.argsFor(0)[0].type).toBe('POST');
                expect($.ajax.calls.argsFor(0)[0].url).toBe('fake_theme_update_url');
            });
            it('should render success gritter message', function () {
                $.ajax.and.callFake(function (e) {
                    e.success();
                });
                self.viewModel.chooseTheme(self.responsive_theme);
                expect($.gritter.success).toHaveBeenCalledWith("Layout updated successfully.");
            });
            it('should render error gritter message', function () {
                $.ajax.and.callFake(function (e) {
                    e.error();
                });
                self.viewModel.chooseTheme(self.responsive_theme);
                expect($.gritter.error).toHaveBeenCalledWith("Layout update failed.");
            });
        });

        describe('openPreviewImage', function() {
            it('should open up the dialog', function() {
                spyOn(self.viewModel.previewDialog.modal, 'dialog');
                self.viewModel.openPreviewImage(self.location_theme);
                expect(self.viewModel.previewDialog.modal.dialog).toHaveBeenCalledWith("open");
            });
        });
    });
});
