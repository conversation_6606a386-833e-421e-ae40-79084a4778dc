describe('MS.AddReview', function() {
    beforeEach(function() {
        spyOn(window, 'vForm');
        addReviewViewModel = new ReviewAddViewModel('url', 'MS-123', 'ABC', 'AG-123', 'Bob', '<EMAIL>', 'CON-123', 'swfUrl', false, true, true, true, null, null, null, '{"SUBMIT": "Submit"}');
    });

    describe('init', function() {
        it('sets the initial value of stage to start', function() {
            expect(ko.unwrap(addReviewViewModel.stage)).toBe('start')
        });

        it('initializes the vform', function() {
            expect(window.vForm).toHaveBeenCalledWith('url', {
                submitText: 'Submit',
                context: '?msid=MS-123&pid=ABC&disableActions=false&name=Bob&email=<EMAIL>&id=CON-123',
                postSubmitCallback: addReviewViewModel.postSubmitCallback
            })
        });
    });

    describe('postSubmitCallback', function() {
        beforeEach(function() {
            spyOn(window, 'ZeroClipboard').and.callThrough();
            ZeroClipboard.config = jasmine.createSpy('config');
            self.review = {
                'originalStars': 0
            }
        });

        it('sets stage to negative if rating is 1', function() {
            self.review['originalStars'] = 1;
            addReviewViewModel.postSubmitCallback(200, self.review);
            expect(ko.unwrap(addReviewViewModel.stage)).toBe('negative');
        });

        it('sets stage to negative if rating is 2', function() {
            self.review['originalStars'] = 2;
            addReviewViewModel.postSubmitCallback(200, self.review);
            expect(ko.unwrap(addReviewViewModel.stage)).toBe('negative');
        });

        it('sets stage to negative if rating is 3', function() {
            self.review['originalStars'] = 3;
            addReviewViewModel.postSubmitCallback(200, self.review);
            expect(ko.unwrap(addReviewViewModel.stage)).toBe('negative');
        });

        it('sets stage to positive if rating is 4', function() {
            self.review['originalStars'] = 4;
            addReviewViewModel.postSubmitCallback(200, self.review);
            expect(ko.unwrap(addReviewViewModel.stage)).toBe('positive');
        });

        it('sets stage to positive if rating is 5', function() {
            self.review['originalStars'] = 5;
            addReviewViewModel.postSubmitCallback(200, self.review);
            expect(ko.unwrap(addReviewViewModel.stage)).toBe('positive');
        });

        it('scrolls to top of window', function() {
            spyOn($.fn, 'scrollTop');
            self.review['originalStars'] = 1;
            addReviewViewModel.postSubmitCallback(200, self.review);
            expect($.fn.scrollTop).toHaveBeenCalledWith(0);
        });

        it('should configure ZeroClipboard if positive', function() {
            self.review['originalStars'] = 4;
            addReviewViewModel.postSubmitCallback(200, self.review);
            expect(ZeroClipboard.config).toHaveBeenCalledWith({swfPath: 'swfUrl'});
        });

        it('should create a ZeroClipboard instance if positive', function() {
            var reviewUrlFixture = $('<input type="button" id="copy-to-clipboard"/>');
            appendSetFixtures(reviewUrlFixture);
            self.review['originalStars'] = 4;
            addReviewViewModel.postSubmitCallback(200, self.review);
            expect(window.ZeroClipboard.calls.argsFor(0)[0].is($('#copy-to-clipboard'))).toBe(true);
        });

        it('sets hasReviewContent to true for positive review if comments textarea has value', function() {
            var commentsFixture = $('<input type="text" value="foo" id="comments"/>');
            appendSetFixtures(commentsFixture);
            self.review['originalStars'] = 4;
            addReviewViewModel.postSubmitCallback(200, self.review);
            expect(addReviewViewModel.hasReviewContent()).toBe(true);
        });

        it('does not set hasReviewContent to true for positive review if comments textarea has no value', function() {
            var commentsFixture = $('<input type="text" value="" id="comments"/>');
            appendSetFixtures(commentsFixture);
            self.review['originalStars'] = 4;
            addReviewViewModel.postSubmitCallback(200, self.review);
            expect(addReviewViewModel.hasReviewContent()).toBe(false);
        });

        it('does not set hasReviewContent to true for negative review', function() {
            self.review['originalStars'] = 2;
            addReviewViewModel.postSubmitCallback(200, self.review);
            expect(addReviewViewModel.hasReviewContent()).toBe(false);
        });
    });

    describe('shareReview', function() {
        beforeEach(function() {
            spyOn(addReviewViewModel, 'goToReviewSharePage');
            spyOn(addReviewViewModel, 'trackShare');
            listing = {name: 'Dummy Source'};
        });

        it('should go to review page if actions are enabled', function() {
            addReviewViewModel.disabled = false;
            addReviewViewModel.shareReview(listing);
            expect(addReviewViewModel.goToReviewSharePage).toHaveBeenCalledWith(listing);
        });

        it('should not go to review page if actions are disabled', function() {
            addReviewViewModel.disabled = true;
            addReviewViewModel.shareReview(listing);
            expect(addReviewViewModel.goToReviewSharePage).not.toHaveBeenCalled();
        });

        it('should call trackShare with correct listing name', function() {
            addReviewViewModel.disabled = false;
            addReviewViewModel.shareReview(listing);
            expect(addReviewViewModel.trackShare).toHaveBeenCalledWith('dummy_source');
        });
    });

    describe('goToReviewSharePage', function() {
        beforeEach(function() {
            spyOn(window, 'open');
            spyOn(window, 'scrollTo');
            spyOn(document, 'getElementsByClassName').and.returnValue([{offsetTop: 13}]);
            listing = {url: 'www.url.com'};
        });

        it('should open a model dialog to guide the users if the listing source is Google Maps and the review URL is unavailable', function() {
            spyOn(addReviewViewModel.googleMapsReviewTip, 'show');
            listing.sourceId = addReviewViewModel.GOOGLE_MAPS_SOURCE;
            addReviewViewModel.goToReviewSharePage(listing);
            expect(addReviewViewModel.googleMapsReviewTip.show).toHaveBeenCalledWith(listing.url);
            expect(window.open).not.toHaveBeenCalled();
        });
        it('should open a new tab directly if the listing source is Google Maps and the review URL is available', function() {
            spyOn(addReviewViewModel.googleMapsReviewTip, 'show');
            listing.sourceId = addReviewViewModel.GOOGLE_MAPS_SOURCE;
            listing.reviewUrl = 'www.reviewUrl.com';
            addReviewViewModel.goToReviewSharePage(listing);
            expect(window.open).toHaveBeenCalledWith(listing.reviewUrl, '_blank');
            expect(addReviewViewModel.googleMapsReviewTip.show).not.toHaveBeenCalled();
        });
        it('should open a new tab directly if the listing source is not Google Maps', function() {
            spyOn(addReviewViewModel.googleMapsReviewTip, 'show');
            listing.sourceId = addReviewViewModel.GOOGLE_MAPS_SOURCE + 1;
            addReviewViewModel.goToReviewSharePage(listing);
            expect(window.open).toHaveBeenCalledWith(listing.url, '_blank');
            expect(addReviewViewModel.googleMapsReviewTip.show).not.toHaveBeenCalled();
        });
        it('should scroll to the Google Maps tooltip if on a mobile device', function() {
            listing.sourceId = addReviewViewModel.GOOGLE_MAPS_SOURCE;
            window.innerWidth = 760;
            addReviewViewModel.goToReviewSharePage(listing);
            expect(window.scrollTo).toHaveBeenCalledWith(0, 13);
        });
    });

});
