describe('utils', function() {
    describe('toTitleCase', function () {
        var expectedString = "I'm A Little Tea Pot.";

        it('title cases string', function () {
            expect(toTitleCase("I'm a little tea pot.")).toBe(expectedString);
        });

        it('ignores arg if falsey', function () {
            expect(toTitleCase(null)).toBeNull();
        });

        it('does leave non title characters uppercased', function () {
            expect(toTitleCase("I'M A LITTLE TEA POT.")).toBe(expectedString);
        });
    })
});
