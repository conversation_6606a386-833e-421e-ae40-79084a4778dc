describe('SubscriptionStatusViewModel', function(){
    var self = this;
    beforeEach(function () {
        self.subscription = {
            thruDate: '2014-01-01',
            expired: true,
            expiring: true
        };
        self.createFutureDate = function (numDays) {
            var tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + numDays);
            return tomorrow;
        };
        self.createPastDate = function (numDays) {
            var yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - numDays);
            return yesterday;
        };
        self.makeExpiring = function(subscription) {
            subscription.thruDate = self.createFutureDate(89).toDateString();
            return subscription;
        };
        self.makeExpired = function(subscription) {
            subscription.thruDate = self.createPastDate(1).toDateString();
            return subscription;
        };
        self.makeNotExpiring = function(subscription) {
            subscription.thruDate = self.createFutureDate(91).toDateString();
            return subscription;
        }
    });


    describe('isActive', function(){
        it('should be true if the thruDate is in the future', function(){
            self.subscription.thruDate = self.createFutureDate(1).toDateString();
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, true);
            expect(statusViewModel.isActive).toBeTruthy();
        });

        it('should be false if the thruDate is in the past', function(){
            self.subscription.thruDate = self.createPastDate(1).toDateString();
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, true);
            expect(statusViewModel.isActive).toBeFalsy();
        });

        it('should be false if thruDate is missing', function() {
            self.subscription.thruDate = undefined;
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, true);
            expect(statusViewModel.isActive).toBeFalsy();
        })
    });

    describe('isExpiring', function(){
        it('should be true if the thruDate is less than 90 days into the future', function(){
            self.subscription.thruDate = self.createFutureDate(89).toDateString();
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, true);
            expect(statusViewModel.isExpiring()).toBeTruthy();
        });

        it('should be true if the thruDate is 90 days into the future', function(){
            self.subscription.thruDate = self.createFutureDate(90).toDateString();
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, true);
            expect(statusViewModel.isExpiring()).toBeTruthy();
        });

        it('should be false if the thruDate is more than 90 days into the future', function(){
            self.subscription.thruDate = self.createFutureDate(91).toDateString();
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, true);
            expect(statusViewModel.isExpiring()).toBeFalsy();
        });

        it('should be false if order is set to auto-renew', function() {
            self.subscription.autoRenew = true;
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, true);
            expect(statusViewModel.isExpiring()).toBeFalsy();
        });
    });

    describe('isExpired', function(){
        it('should be true if thruDate is in past', function(){
            self.subscription.thruDate = self.createPastDate(1).toDateString();
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, true);
            expect(statusViewModel.isExpired()).toBeTruthy();
        });

        it('should be false if thruDate is in future', function(){
            self.subscription.thruDate = self.createFutureDate(1).toDateString();
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, true);
            expect(statusViewModel.isExpired()).toBeFalsy();
        });

        it('should be false if thruDate is today', function(){
            var today = moment();
            self.subscription.thruDate = today.format('YYYY-MM-DD');
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, true);
            expect(statusViewModel.isExpired()).toBeFalsy();
        });

        it('should be false if order is set to auto renew', function(){
            self.subscription.autoRenew = true;
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, true);
            expect(statusViewModel.isExpired()).toBeFalsy();
        });
    });

    describe('isAutoRenew', function() {
        it('should be true if autoRenew is true', function() {
            self.subscription.autoRenew = true;
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, true);
            expect(statusViewModel.isAutoRenew()).toBeTruthy();
        });
        it('should be false if autoRenew is false', function() {
            self.subscription.autoRenew = false;
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, true);
            expect(statusViewModel.isAutoRenew()).toBeFalsy();
        });
    });

    describe('showExpiry', function(){
        it('should be true when showExpiry is true', function(){
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, true);
            expect(statusViewModel.showExpiry()).toBeTruthy();
        });

        it('should be false when showExpiry is false', function(){
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, false);
            expect(statusViewModel.showExpiry()).toBeFalsy();
        });
    });

    describe('statusText', function (){
        it('should be Active when not expiring nor expired', function(){
            self.subscription = self.makeNotExpiring(self.subscription);
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, false);
            expect(statusViewModel.statusText()).toBe('Active');
        });

        it('should be Active when not expired but expiring', function(){
            self.subscription = self.makeExpiring(self.subscription);
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, false);
            expect(statusViewModel.statusText()).toBe('Active');
        });

        it('should be Expired when expired', function(){
            self.subscription = self.makeExpired(self.subscription);
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, false);
            expect(statusViewModel.statusText()).toBe('Expired');
        });
    });

    describe('statusTitle', function (){
        it('should be Expires: when not expiring nor expired', function(){
            self.subscription = self.makeNotExpiring(self.subscription);
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, false);
            expect(statusViewModel.statusTitle()).toBe('Expires:');
        });

        it('should be Expires: when not expired but expiring', function(){
            self.subscription = self.makeExpiring(self.subscription);
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, false);
            expect(statusViewModel.statusTitle()).toBe('Expires:');
        });

        it('should be Expired: when expired', function(){
            self.subscription = self.makeExpired(self.subscription);
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, false);
            expect(statusViewModel.statusTitle()).toBe('Expired:');
        });
        it('should have correct value when auto-renew is on', function() {
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, false);
            statusViewModel.isAutoRenew(true);
            expect(statusViewModel.statusTitle()).toBe('Renews:');
        });
    });

    describe('statusClass', function(){
        it('should be active-subscription when not expiring nor expired', function(){
            self.subscription = self.makeNotExpiring(self.subscription);
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, false);
            expect(statusViewModel.statusClass()).toBe('active-subscription');
        });

        it('should be expiring-subscription when not expired but expiring', function(){
            self.subscription = self.makeExpiring(self.subscription);
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, false);
            expect(statusViewModel.statusClass()).toBe('expiring-subscription');
        });

        it('should be expired-subscription when expired', function(){
            self.subscription = self.makeExpired(self.subscription);
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, false);
            expect(statusViewModel.statusClass()).toBe('expired-subscription');
        });

        it('should be active-subscription when near expiry and auto-renew is on', function() {
            self.subscription = self.makeExpiring(self.subscription);
            var statusViewModel = new SubscriptionStatusViewModel(self.subscription, false);
            statusViewModel.isAutoRenew(true);
            expect(statusViewModel.statusClass()).toBe('active-subscription');
        });
    });

    describe('thruDateFormatted', function() {

        it('should format the date correctly', function() {
            self.subscription['thruDate'] = '2099-04-05';
            var model = new SubscriptionStatusViewModel(self.subscription);
            expect(model.thruDateFormatted).toEqual('Apr 5, 2099');
        });
    });

    describe('autoRenewText', function() {
        it('should have correct value when auto renew is off', function() {
            self.subscription['autoRenew'] = false;
            var model = new SubscriptionStatusViewModel(self.subscription);
            expect(model.autoRenewText()).toEqual('Off');
        });
        it('should have correct value when auto renew is on', function() {
            self.subscription['autoRenew'] = true;
            var model = new SubscriptionStatusViewModel(self.subscription);
            expect(model.autoRenewText()).toEqual('On');
        });
    });
});

describe('SourceData', function() {
    var self = this;
    beforeEach(function () {
        self.data = {};
        self.latestSyndicationOrder = {};
        self.dialog = {};
        self.baseData = {};
    });

    describe('getStatusLabel', function() {
        beforeEach(function () {
            self.baseData = new BaseData('data', 'edit-link');
            self.sourceDataModel = new SourceData(self.data, 'FACTUAL', self.latestSyndicationOrder, self.dialog, self.baseData);
        });

        it('should return missing if no data for that field/category', function() {
            expect(self.sourceDataModel.getStatusLabel('category-with-no-data')).toBe('missing')
        });

        it('should return null if match_flag is true', function() {
            spyOn(self.baseData, 'getValue').and.returnValue('something');
            spyOn(self.sourceDataModel, 'getMatchFlag').and.returnValue(true);
            expect(self.sourceDataModel.getStatusLabel('blah')).toBe(null)
        });

        it('should return rejected if syndication order is rejected', function() {
            spyOn(self.baseData, 'getValue').and.returnValue('something');
            spyOn(self.sourceDataModel, 'getMatchFlag').and.returnValue(false);
            self.sourceDataModel.syndicationStatus = ORDER_REJECTED;
            expect(self.sourceDataModel.getStatusLabel('blah')).toBe('rejected');
        });
    });

    describe('getCellText', function() {
        beforeEach(function () {
            self.baseData = new BaseData('data', 'edit-link');
            self.sourceDataModel = new SourceData(self.data, 'FACTUAL', self.latestSyndicationOrder, self.dialog, self.baseData);
        });

        it('should return Not Applicable if field/category is not accepted by that source', function() {
            expect(self.sourceDataModel.getCellText('call_tracking_number')).toBe('Not Applicable')
        });
    });

    describe('getValueCss', function() {
        beforeEach(function () {
            self.sourceDataModel = new SourceData(self.data, 'FACTUAL', self.latestSyndicationOrder, self.dialog, self.baseData);
            self.baseCss = 'details-view';
        });

        it('should return no-value if match_flag is undefined', function() {
            spyOn(self.sourceDataModel, 'getMatchFlag').and.returnValue(undefined);
            expect(self.sourceDataModel.getValueCss('blah')).toBe(self.baseCss + ' no-value')
        });

        it('should return base css if match_flag is defined', function() {
            spyOn(self.sourceDataModel, 'getMatchFlag').and.returnValue('whatever');
            expect(self.sourceDataModel.getValueCss('blah')).toBe(self.baseCss)
        });
    });

    describe('hasDataForCategory', function() {
        beforeEach(function () {
            self.sourceDataModel = new SourceData(
                self.data,
                'FACTUAL',
                self.latestSyndicationOrder,
                self.dialog,
                self.baseData,
                false,
                {description: 'A great description', toll_free_number: '18005551234'},
                null
            );
        });

        it('should return false if no value for  rich data field and healthcare data is null', function() {
            expect(self.sourceDataModel.hasDataForCategory('payment_methods')).toBe(false);
        });

        it('should return true if value for rich data field', function() {
            expect(self.sourceDataModel.hasDataForCategory('description')).toBe(true);
        });

        it('should return true if value for healthcare data', function() {
            self.sourceDataModel.healthCareData = {first_name: 'Kyle', office: 'My Office'};
            expect(self.sourceDataModel.hasDataForCategory('office')).toBe(true);
        });

        it('should return false for empty repeated fields', function() {
            self.sourceDataModel.healthCareData = {professional_credential: []};
            expect(self.sourceDataModel.hasDataForCategory('professional_credential')).toBe(false);
        });
    });

    describe('getStatus', function() {

        it('should return undefined if ld not active', function(){
            var sourceDataModel = new SourceData(self.data, 'FACTUAL', self.latestSyndicationOrder, self.dialog, self.baseData);
            sourceDataModel.dialog['isLDActive'] = false;
            expect(sourceDataModel.getStatus('blah')).toBe(undefined);
        });

        it('should return empty string if hasHoursOfOperation is false and category is hours of operation', function(){
            var sourceDataModel = new SourceData(self.data, 'FACTUAL', self.latestSyndicationOrder, self.dialog, self.baseData, false);
            sourceDataModel.dialog['isLDActive'] = true;
            expect(sourceDataModel.getStatus('hoursOfOperation')).toBe('');
        });

        it('should return Rejected if order rejected', function(){
            var sourceDataModel = new SourceData(self.data, 'LOCALEZE', self.latestSyndicationOrder, self.dialog, self.baseData);
            sourceDataModel.dialog['isLDActive'] = true;
            sourceDataModel.syndicationStatus = 'orderRejected';
            expect(sourceDataModel.getStatus('city')).toBe('Rejected');
        });

        describe('getMatchFlag(category) is true', function() {

            it('should return undefined if category is country and source name is not factual', function(){
                var sourceDataModel = new SourceData(self.data, 'LOCALEZE', self.latestSyndicationOrder, self.dialog, self.baseData);
                sourceDataModel.dialog['isLDActive'] = true;
                // sourceDataModel.syndicationStatus = 'orderRejected';
                spyOn(sourceDataModel, 'getMatchFlag').and.returnValue(true);
                spyOn(sourceDataModel, 'getHistory');
                expect(sourceDataModel.getStatus('country')).toBe(undefined);
            });

            it('should return fixed if and category is city and source name is not factual and history match flag is false', function(){
                var sourceDataModel = new SourceData(self.data, 'LOCALEZE', self.latestSyndicationOrder, self.dialog, self.baseData);
                sourceDataModel.dialog['isLDActive'] = true;
                sourceDataModel.historyItems = {
                    city: [
                        {},
                        {matchFlag: false}
                    ]
                };
                // sourceDataModel.syndicationStatus = 'orderRejected';
                spyOn(sourceDataModel, 'getMatchFlag').and.returnValue(true);
                spyOn(sourceDataModel, 'getHistory');
                expect(sourceDataModel.getStatus('city')).toBe('Fixed');
            });

            it('should return fixed if category is country and source name is factual and history match flag is false', function(){
                var sourceDataModel = new SourceData(self.data, 'FACTUAL', self.latestSyndicationOrder, self.dialog, self.baseData);
                sourceDataModel.dialog['isLDActive'] = true;
                sourceDataModel.historyItems = {
                    country: [
                        {},
                        {matchFlag: false}
                    ]
                };
                // sourceDataModel.syndicationStatus = 'orderRejected';
                spyOn(sourceDataModel, 'getMatchFlag').and.returnValue(true);
                spyOn(sourceDataModel, 'getHistory');
                expect(sourceDataModel.getStatus('country')).toBe('Fixed');
            });

            it('should return undefined if category is country and source name is factual and history match flag is true', function(){
                var sourceDataModel = new SourceData(self.data, 'FACTUAL', self.latestSyndicationOrder, self.dialog, self.baseData);
                sourceDataModel.dialog['isLDActive'] = true;
                sourceDataModel.historyItems = {
                    country: [
                        {},
                        {matchFlag: true}
                    ]
                };
                // sourceDataModel.syndicationStatus = 'orderRejected';
                spyOn(sourceDataModel, 'getMatchFlag').and.returnValue(true);
                spyOn(sourceDataModel, 'getHistory');
                expect(sourceDataModel.getStatus('country')).toBe(undefined);
            });

        });

        it('should return sent if match_flag is false and syndicationStatus is undefined', function(){
            var sourceDataModel = new SourceData(self.data, 'FACTUAL', self.latestSyndicationOrder, self.dialog, self.baseData);
            sourceDataModel.dialog['isLDActive'] = true;
            sourceDataModel.syndicationStatus = undefined;
            spyOn(sourceDataModel, 'getMatchFlag').and.returnValue(false);
            expect(sourceDataModel.getStatus('country')).toBe('Sent');
        });

        it('should return sent if match_flag is false and syndicationStatus is processing', function(){
            var sourceDataModel = new SourceData(self.data, 'FACTUAL', self.latestSyndicationOrder, self.dialog, self.baseData);
            sourceDataModel.dialog['isLDActive'] = true;
            sourceDataModel.syndicationStatus = 'orderProcessing';
            spyOn(sourceDataModel, 'getMatchFlag').and.returnValue(false);
            expect(sourceDataModel.getStatus('country')).toBe('Sent');
        });

        it('should return accepted if match_flag is false and syndicationStatus is accepted', function(){
            var sourceDataModel = new SourceData(self.data, 'FACTUAL', self.latestSyndicationOrder, self.dialog, self.baseData);
            sourceDataModel.dialog['isLDActive'] = true;
            sourceDataModel.syndicationStatus = 'orderAccepted';
            spyOn(sourceDataModel, 'getMatchFlag').and.returnValue(false);
            expect(sourceDataModel.getStatus('country')).toBe('Accepted');
        });

    });

    describe('getStatusCss', function() {
        beforeEach(function() {
            self.sourceDataModel = new SourceData(self.data, 'FACTUAL', self.latestSyndicationOrder, self.dialog, self.baseData);
            self.baseCss = 'show status ';
        });

        it('should return badge-green status is fixed', function(){
            spyOn(self.sourceDataModel, 'getStatus').and.returnValue('Fixed');
            expect(self.sourceDataModel.getStatusCss('city')).toBe(self.baseCss + 'badge-green');
        });

        it('should return badge-green status is accepted', function(){
            spyOn(self.sourceDataModel, 'getStatus').and.returnValue('Accepted');
            expect(self.sourceDataModel.getStatusCss('city')).toBe(self.baseCss + 'badge-green');
        });

        it('should return badge-orange if status is sent', function(){
            spyOn(self.sourceDataModel, 'getStatus').and.returnValue('Sent');
            expect(self.sourceDataModel.getStatusCss('city')).toBe(self.baseCss + 'badge-orange');
        });

        it('should return badge-red if status is rejected', function(){
            spyOn(self.sourceDataModel, 'getStatus').and.returnValue('Rejected');
            expect(self.sourceDataModel.getStatusCss('city')).toBe(self.baseCss + 'badge-red');
        });

        it('should return undefined otherwise', function(){
            spyOn(self.sourceDataModel, 'getStatus').and.returnValue('blah');
            expect(self.sourceDataModel.getStatusCss('city')).toBe(undefined);
        });
    });

});

describe('ListingsViewModel', function() {
    var self = this;
    beforeEach(function () {
        self.data = {};
        self.synOrders = {};
        self.subscription = {active: true};
    });

    describe('isHealthCareSectionVisible', function() {
        beforeEach(function() {
            self.listingsViewModel = new ListingsViewModel(self.data, self.synOrders, self.subscription);
        });

        it('should return true if model has HC data', function() {
            self.listingsViewModel.healthCareDataCategories = ['cat1', 'cat2'];
            expect(self.listingsViewModel.isHealthCareSectionVisible()).toBe(true);
        });
    });

    describe('getFormattedField', function() {
        it('should handle empty fields', function() {
            self.richData = {};
            self.healthCareData = {};
            self.listingsViewModel = new ListingsViewModel(self.data, self.synOrders, self.subscription, null, null, null, self.richData, self.healthCareData);
            expect(self.listingsViewModel.getFormattedField('is-taking-patients')).toBe('');
        });
        it('should handle booleans', function() {
            self.richData = {};
            self.healthCareData = {'is-taking-patients': true};
            self.listingsViewModel = new ListingsViewModel(self.data, self.synOrders, self.subscription, null, null, null, self.richData, self.healthCareData);
            expect(self.listingsViewModel.getFormattedField('is-taking-patients')).toBe('Yes');
        });
        it('should handle strings', function() {
            self.richData = {'services': 'shoe-cleaning'};
            self.healthCareData = {};
            self.listingsViewModel = new ListingsViewModel(self.data, self.synOrders, self.subscription, null, null, null, self.richData, self.healthCareData);
            expect(self.listingsViewModel.getFormattedField('services')).toBe('shoe-cleaning');
        });
        it('should handle lists of strings', function() {
            self.richData = {'services': ['shoe-cleaning', 'plumbing']};
            self.healthCareData = {};
            self.listingsViewModel = new ListingsViewModel(self.data, self.synOrders, self.subscription, null, null, null, self.richData, self.healthCareData);
            expect(self.listingsViewModel.getFormattedField('services')).toBe('shoe-cleaning, plumbing');
        });
    });
});
