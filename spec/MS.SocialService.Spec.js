describe('MS.SocialServiceSync', function() {
    var self = this;
    self.serviceType = 'type';
    self.serviceUserId = 'id';
    self.msid = 'MS-ASDADS';
    self.social_profile_id = 'SCP-asdasdas';
    self.fetchServiceUrl = 'testfetchurl';
    self.addServiceUrl = 'testaddurl';
    self.search = '';
    self.availableServices  = [
        JSON.parse('{"page_id": "id-1", "name": "name-1"}'),
        JSON.parse('{"page_id": "id-2", "name": "name-2"}')];

    beforeEach(function(){
        self.ssSync = new SocialServiceSync(self.msid, self.serviceType, self.social_profile_id, self.serviceUserId, self.fetchServiceUrl, self.addServiceUrl, self.search);
        self.ssSync.services([new Service(self.availableServices[0]), new Service(self.availableServices[1])]);
    });

    describe('init', function(){
        it('should set services properly', function(){
            expect(self.ssSync.services()[0].id).toBe(self.availableServices[0].page_id);
            expect(self.ssSync.services()[1].id).toBe(self.availableServices[1].page_id);
            expect(self.ssSync.services()[0].name).toBe(self.availableServices[0].name);
            expect(self.ssSync.services()[1].name).toBe(self.availableServices[1].name);
        });
    });

    describe('selectService', function(){
        beforeEach(function() {
            spyOn($, "ajax");
        });

        it('should try to select the specified service', function(){
            self.ssSync.isSelectingService(false);
            self.ssSync.selectService(self.availableServices[1]);

            expect($.ajax).toHaveBeenCalled();
        });

        it('should not try to select the specified service', function(){
            self.ssSync.isSelectingService(true);
            self.ssSync.selectService(self.availableServices[1]);

            expect($.ajax).not.toHaveBeenCalled();
        });


    });
});


