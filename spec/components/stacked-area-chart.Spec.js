describe('stacked-area-chart.js', function(){

    describe('formatChartSeriesData', function(){
        it('should return a list of one properly formatted dictionary if one series is passed in ', function () {
            var testObject =  [ko.observable({chartData: [{date: "2017-05-17T06:00:00Z", value: 0}]})];
            var expectedList = [ko.observable({chartData: [["2017-05-17T06:00:00Z", 0]]})];
            var actualList = formatChartSeriesData(testObject);
            expect(actualList[0]()).toEqual(expectedList[0]());
        });

        it('should return a list of two properly formatted dictionary if two series are passed in ', function () {
            var testObject =  [ko.observable({chartData: [{date: "2017-05-17T06:00:00Z", value: 0}]}),
                               ko.observable({chartData: [{date: "2017-06-18T06:00:00Z", value: 4}]})];
            var expectedList = [ko.observable({chartData: [["2017-05-17T06:00:00Z", 0]]}),
                                ko.observable({chartData: [["2017-06-18T06:00:00Z", 4]]})];
            var actualList = formatChartSeriesData(testObject);
            expect(actualList[0]()).toEqual(expectedList[0]());
            expect(actualList[1]()).toEqual(expectedList[1]());
        });
    });

    describe('getChartSeriesDataFromParams', function() {
        it('should return a list of one item if only series1 is passed in', function () {
            expect(getChartSeriesDataFromParams({series1: ['blah']}).length).toEqual(1);
        });

        it('should return a list of two items if series1 and series 2 are passed in', function () {
            expect(getChartSeriesDataFromParams({series1: ['blah'], series2: ['blah2']}).length).toEqual(2);
        });

        it('should return a list of three items if series1, series2, and series3 are passed in', function () {
            params = {series1: ['blah'], series2: ['blah2'], series3: ['blah3']};
            expect(getChartSeriesDataFromParams(params).length).toEqual(3);
        });
    });

});
