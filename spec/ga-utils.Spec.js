describe('ga-utils', function() {

    describe('GATrackEvent', function() {

        it('does nothing if there _gaq was not defined', function() {
            GATrackEvent('MS-123', 'Phone', '************');
            expect(true).toBeTruthy('If there was no JS error everything is good');
        });

        it('does nothing if there is no google analytics queue', function() {
            _gaq = undefined;
            delete _gaq;
            GATrackEvent('MS-123', 'Phone', '************');
            expect(true).toBeTruthy('If there was no JS error everything is good');
        });

        it('adds the event to the queue if there is a google analytics queue', function() {
            _gaq = [];
            GATrackEvent('MS-123', 'Phone', '************');
            expect(_gaq.length).toEqual(1);
            expect(_gaq[0]).toEqual(['_trackEvent', 'MS-123', 'Phone', '************']);
        });

    });

    describe('GATrackPageView', function() {

        it('does nothing if there is no google analytics queue', function() {
            _gaq = undefined;
            delete _gaq;
            GATrackPageView('MS-123', 'Phone', '************');
            expect(true).toBeTruthy('If there was no JS error everything is good');
        });

        it('adds the page view to the queue if there is a google analytics queue', function() {
            _gaq = [];
            GATrackPageView('/path/to/nowhere');
            expect(_gaq.length).toEqual(1);
            expect(_gaq[0]).toEqual(['_trackPageView', '/path/to/nowhere']);
        });

    });

});
