describe('ga-utils', function() {

    describe('GATrackEvent', function() {

        beforeEach(function() {
            // Clean up global variables before each test
            if (typeof ga !== 'undefined') {
                delete window.ga;
            }
            if (typeof _gaq !== 'undefined') {
                delete window._gaq;
            }
        });

        it('does nothing if neither ga nor _gaq are defined', function() {
            GATrackEvent('MS-123', 'Phone', '************');
            expect(true).toBeTruthy('If there was no JS error everything is good');
        });

        it('uses Universal Analytics (ga) when available', function() {
            var gaCalls = [];
            window.ga = function() {
                gaCalls.push(Array.prototype.slice.call(arguments));
            };

            GATrackEvent('MS-123', 'Phone', '************');
            expect(gaCalls.length).toEqual(1);
            expect(gaCalls[0]).toEqual(['send', 'event', 'MS-123', 'Phone', '************']);
        });

        it('falls back to Classic Analytics (_gaq) when ga is not available', function() {
            window._gaq = [];
            GATrackEvent('MS-123', 'Phone', '************');
            expect(_gaq.length).toEqual(1);
            expect(_gaq[0]).toEqual(['_trackEvent', 'MS-123', 'Phone', '************']);
        });

        it('prefers Universal Analytics over Classic Analytics when both are available', function() {
            var gaCalls = [];
            window.ga = function() {
                gaCalls.push(Array.prototype.slice.call(arguments));
            };
            window._gaq = [];

            GATrackEvent('MS-123', 'Phone', '************');
            expect(gaCalls.length).toEqual(1);
            expect(_gaq.length).toEqual(0);
        });

    });

    describe('GATrackPageView', function() {

        beforeEach(function() {
            // Clean up global variables before each test
            if (typeof ga !== 'undefined') {
                delete window.ga;
            }
            if (typeof _gaq !== 'undefined') {
                delete window._gaq;
            }
        });

        it('does nothing if neither ga nor _gaq are defined', function() {
            GATrackPageView('/path/to/nowhere');
            expect(true).toBeTruthy('If there was no JS error everything is good');
        });

        it('uses Universal Analytics (ga) when available', function() {
            var gaCalls = [];
            window.ga = function() {
                gaCalls.push(Array.prototype.slice.call(arguments));
            };

            GATrackPageView('/path/to/nowhere');
            expect(gaCalls.length).toEqual(1);
            expect(gaCalls[0]).toEqual(['send', 'pageview', '/path/to/nowhere']);
        });

        it('falls back to Classic Analytics (_gaq) when ga is not available', function() {
            window._gaq = [];
            GATrackPageView('/path/to/nowhere');
            expect(_gaq.length).toEqual(1);
            expect(_gaq[0]).toEqual(['_trackPageView', '/path/to/nowhere']);
        });

        it('prefers Universal Analytics over Classic Analytics when both are available', function() {
            var gaCalls = [];
            window.ga = function() {
                gaCalls.push(Array.prototype.slice.call(arguments));
            };
            window._gaq = [];

            GATrackPageView('/path/to/nowhere');
            expect(gaCalls.length).toEqual(1);
            expect(_gaq.length).toEqual(0);
        });

    });

});
