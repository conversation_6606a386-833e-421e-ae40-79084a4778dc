describe('MS.ListingStats', function() {
    describe('MS.ListingStats.ListingStatsViewModel', function() {
        var listingStatsVM;

        beforeEach(function() {
            listingStatsVM = new ListingStatsViewModel();
            listingStatsVM.graphData = [{
                name: 'Listing Citations',
                data: []
            }];
            spyOn($.fn, 'highcharts');
            spyOn(MS, 'AreaChart');
        });

        describe('_setPeriodOptions', function() {
            it('should not set period if no stats given', function() {
                listingStatsVM._setPeriodOptions();
                expect(listingStatsVM.loadingCitationStats()).toBe(false);
                expect(listingStatsVM.period()).toBe(undefined);
            });

            it('should set period to seven days', function() {
                listingStatsVM.allStats = new Array(7);
                listingStatsVM._setPeriodOptions();
                expect(listingStatsVM.loadingCitationStats()).toBe(true);
                expect(listingStatsVM.periodOptions()).toEqual([PERIOD_TYPE.SEVEN_DAYS]);
                expect(listingStatsVM.period()).toBe(PERIOD_TYPE.SEVEN_DAYS);
            });

            it('should set period to thirty days', function() {
                listingStatsVM.allStats = new Array(30);
                listingStatsVM._setPeriodOptions();
                expect(listingStatsVM.loadingCitationStats()).toBe(true);
                expect(listingStatsVM.periodOptions()).toEqual([PERIOD_TYPE.SEVEN_DAYS, PERIOD_TYPE.THIRTY_DAYS]);
                expect(listingStatsVM.period()).toBe(PERIOD_TYPE.THIRTY_DAYS);
            });

            it('should set period to six months', function() {
                listingStatsVM.allStats = new Array(52);
                listingStatsVM._setPeriodOptions();
                expect(listingStatsVM.loadingCitationStats()).toBe(true);
                expect(listingStatsVM.periodOptions()).toEqual([PERIOD_TYPE.SEVEN_DAYS, PERIOD_TYPE.THIRTY_DAYS,
                    PERIOD_TYPE.SIX_MONTHS]);
                expect(listingStatsVM.period()).toBe(PERIOD_TYPE.SIX_MONTHS);
            });

            it('should set period to one year', function() {
                listingStatsVM.allStats = new Array(78);
                listingStatsVM._setPeriodOptions();
                expect(listingStatsVM.loadingCitationStats()).toBe(true);
                expect(listingStatsVM.periodOptions()).toEqual([PERIOD_TYPE.SEVEN_DAYS, PERIOD_TYPE.THIRTY_DAYS,
                    PERIOD_TYPE.SIX_MONTHS, PERIOD_TYPE.YEAR]);
                expect(listingStatsVM.period()).toBe(PERIOD_TYPE.YEAR);
            });
        });

        describe('_getPeriodStats', function() {

            beforeEach(function() {
                listingStatsVM.allStats = new Array(78);
            });

            it('return stats for seven days', function() {
                listingStatsVM.period(PERIOD_TYPE.SEVEN_DAYS);
                var stats = listingStatsVM._getPeriodStats();
                expect(stats.length).toBe(7);
            });

            it('return stats for thirty days', function() {
                listingStatsVM.period(PERIOD_TYPE.THIRTY_DAYS);
                var stats = listingStatsVM._getPeriodStats();
                expect(stats.length).toBe(30);
            });

            it('return stats for six months', function() {
                listingStatsVM.period(PERIOD_TYPE.SIX_MONTHS);
                var stats = listingStatsVM._getPeriodStats();
                expect(stats.length).toBe(27);
            });

            it('return stats for one year', function() {
                listingStatsVM.period(PERIOD_TYPE.YEAR);
                var stats = listingStatsVM._getPeriodStats();
                expect(stats.length).toBe(53);
            });
        });
    });
});
