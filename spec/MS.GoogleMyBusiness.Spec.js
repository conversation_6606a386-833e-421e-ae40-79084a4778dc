describe('MS.GoogleMyBusiness', function() {
    describe('MS.GoogleMyBusiness.GoogleMyBusinessAccount', function() {
        var gmbAccount;

        beforeEach(function() {
            var account = {
                accountPathName: '/account/123',
                displayName: 'My Company',
                locations: []
            };
           gmbAccount = new GoogleMyBusinessAccount(account);
        });

        describe('endOfLocationList', function () {
            it('should return true when location cursor is null', function() {
                gmbAccount.locationCursor(null);
                expect(gmbAccount.endOfLocationList()).toBe(true);
            });

            it('should return false when location cursor is undefined', function() {
                gmbAccount.locationCursor(undefined);
                expect(gmbAccount.endOfLocationList()).toBe(false);
            });

            it('should return false when account cursor is anything except null', function() {
                gmbAccount.locationCursor('cursor');
                expect(gmbAccount.endOfLocationList()).toBe(false);
            });
        });

        describe('loadMoreLocations', function () {
            beforeEach(function() {
                spyOn($, "ajax");
            });

            it('should try to load more locations', function() {
                gmbAccount.locationCursor('cursor');
                gmbAccount.isLoadingLocations(false);
                gmbAccount.loadMoreLocations('id');

                expect($.ajax).toHaveBeenCalled();
            });

            it('should not try to load more locations if the end of location lists has reached', function() {
                gmbAccount.locationCursor(null);
                gmbAccount.isLoadingLocations(false);
                gmbAccount.loadMoreLocations('id');

                expect($.ajax).not.toHaveBeenCalled();
            });

            it('should not try to load more locations if it is already loading', function() {
                gmbAccount.locationCursor('cursor');
                gmbAccount.isLoadingLocations(true);
                gmbAccount.loadMoreLocations('id');

                expect($.ajax).not.toHaveBeenCalled();
            });

        });

        describe('noLocationFound', function () {
            it('should return false if the number of locations is bigger than zero', function() {
                gmbAccount.locations([1, 2]);
                expect(gmbAccount.noLocationFound()).toBe(false);
            });

            it('should return true if there is no location and the location cursor is null', function() {
                gmbAccount.locations([]);
                gmbAccount.locationCursor(null);
                gmbAccount.isLoadingLocations(false);
                expect(gmbAccount.noLocationFound()).toBe(true);
            });

            it('should return false when it is loading', function() {
                gmbAccount.locations([]);
                gmbAccount.locationCursor(null);
                gmbAccount.isLoadingLocations(true);
                expect(gmbAccount.noLocationFound()).toBeFalsy();
            });
        });
    });


    describe('MS.GoogleMyBusiness.GoogleMyBusinessSync', function() {
        var gmbSync;

        beforeEach(function() {
            gmbSync = new GoogleMyBusinessSync('id');
            for (var i = 0; i < 3; i++) {
                var account = {};
                account.accountPathName = '/account/' + i;
                account.displayName = 'My Company ' + i;
                account.locationCursor = 'cursor';
                account.locations = [];

                gmbSync.accounts.push(new GoogleMyBusinessAccount(account));
            }
        });

        describe('endOfAccountList', function () {
            it('should return true when account cursor is null', function() {
                gmbSync.accountCursor(null);
                expect(gmbSync.endOfAccountList()).toBe(true);
            });

            it('should return false when account cursor is undefined', function() {
                gmbSync.accountCursor(undefined);
                expect(gmbSync.endOfAccountList()).toBe(false);
            });

            it('should return false when account cursor is anything except null', function() {
                gmbSync.accountCursor('cursor');
                expect(gmbSync.endOfAccountList()).toBe(false);
            });

        });

        describe('loadMoreAccounts', function () {
            beforeEach(function() {
                spyOn($, "ajax");
            });

            it('should try to load more accounts', function() {
                gmbSync.accountCursor('cursor');
                gmbSync.isLoadingAccounts(false);

                gmbSync.loadMoreAccounts();

                expect($.ajax).toHaveBeenCalled();
            });

            it('should not try to load more accounts if the end of list has been reached', function() {
                gmbSync.accountCursor(null);
                gmbSync.isLoadingAccounts(false);

                gmbSync.loadMoreAccounts();

                expect($.ajax).not.toHaveBeenCalled();
            });

            it('should not try to load more accounts if it is already being loading', function() {
                gmbSync.accountCursor('cursor');
                gmbSync.isLoadingAccounts(true);

                gmbSync.loadMoreAccounts();

                expect($.ajax).not.toHaveBeenCalled();
            });

        });

        describe('selectLocation', function () {
            var location;

            beforeEach(function() {
                location = {
                    'address': 'address',
                    'displayName': 'name',
                    'isVerified': true,
                    'locationPathName': 'path'
                };
                location = new GoogleMyBusinessLocation(location);

                spyOn($, "ajax");
            });

            it('should try to select the specified location', function() {
                gmbSync.isSelectingLocation(false);
                gmbSync.selectLocation('id', location);

                expect(location.isSelected()).toBe(true);
                expect($.ajax).toHaveBeenCalled();
            });

            it('should not try to select the specified location', function() {
                gmbSync.isSelectingLocation(true);
                gmbSync.selectLocation('id', location);

                expect(location.isSelected()).toBe(false);
                expect($.ajax).not.toHaveBeenCalled();
            });
        });

        describe('loadMoreAccountsIntoView', function () {
            it('should do nothing if all accounts have already been retrieved', function() {
                spyOn(gmbSync, 'accounts');
                gmbSync.accountCursor(null);
                gmbSync.loadMoreAccountsIntoView();

                expect(gmbSync.accounts).not.toHaveBeenCalled();
            });

            it('should try to load more accounts if no account has been loaded', function() {
                spyOn(gmbSync, 'accounts').and.returnValue([]);
                spyOn(gmbSync, 'loadMoreAccounts');
                gmbSync.accountCursor('cursor');
                gmbSync.loadMoreAccountsIntoView();

                expect(gmbSync.loadMoreAccounts).toHaveBeenCalled();
            });

            it('should not check if the last account is in the viewport if it has been displayed', function() {
                spyOn(gmbSync, 'isElementVerticallyInView');
                gmbSync.accountCursor('cursor');
                gmbSync.accounts()[gmbSync.accounts().length - 1].hasBeenDisplayed = true;
                gmbSync.loadMoreAccountsIntoView();

                expect(gmbSync.isElementVerticallyInView).not.toHaveBeenCalled();
            });

            it('should check if the last account is in the viewport if it has not been displayed', function() {
                spyOn(gmbSync, 'isElementVerticallyInView').and.returnValue(false);
                spyOn(gmbSync, 'loadMoreAccounts');
                gmbSync.accountCursor('cursor');
                gmbSync.accounts()[gmbSync.accounts().length - 1].hasBeenDisplayed = false;
                gmbSync.loadMoreAccountsIntoView();

                expect(gmbSync.isElementVerticallyInView).toHaveBeenCalled();
                expect(gmbSync.loadMoreAccounts).not.toHaveBeenCalled();
            });

            it('should try to load more accounts if the current last account is fully displayed', function() {
                spyOn(gmbSync, 'isElementVerticallyInView').and.returnValue(true);
                spyOn(gmbSync, 'loadMoreAccounts');
                gmbSync.accountCursor('cursor');
                gmbSync.accounts()[gmbSync.accounts().length - 1].hasBeenDisplayed = false;
                gmbSync.loadMoreAccountsIntoView();

                expect(gmbSync.loadMoreAccounts).toHaveBeenCalled();
                expect(gmbSync.accounts()[gmbSync.accounts().length - 1].hasBeenDisplayed).toBe(true);
            });
        });

    });
});