describe("MS.ListingSyncPro", function() {

    describe("ListingAccuracyStatus", function() {

        var assertDetails = function(expected_result, result) {
            for (key in expected_result) {
                expect(result[key]).toBe(expected_result[key]);
            }
        };

        it('should have details from CLAIM_STATUS_DETAILS for CLAIMED_BY_OTHERS', function() {
            var status = new ListingAccuracyStatus('IN_SYNC', 'CLAIMED_BY_OTHERS');
            assertDetails(CLAIM_STATUS_DETAILS['CLAIMED_BY_OTHERS'], status);
        });

        it('should have details from SYNC_STATUS_DETAILS for NOT_IN_SYNC', function() {
            var status = new ListingAccuracyStatus('NOT_IN_SYNC', 'CLAIMED_BY_US');
            assertDetails(SYNC_STATUS_DETAILS['NOT_IN_SYNC'], status);
        });

        it('should have details from SYNC_STATUS_DETAILS for IN_SYNC', function() {
            var status = new ListingAccuracyStatus('IN_SYNC', 'CLAIMED_BY_US');
            assertDetails(SYNC_STATUS_DETAILS['IN_SYNC'], status);
        });

        it('should have details from CLAIM_STATUS_DETAILS for NOT_CLAIMABLE if type is FACEBOOK', function() {
            var status = new ListingAccuracyStatus('NOT_IN_SYNC', 'NOT_CLAIMABLE', 'FACEBOOK');
            assertDetails(CLAIM_STATUS_DETAILS['NOT_CLAIMABLE'], status);
        });

        it('should have details from CLAIM_STATUS_DETAILS for NOT_CLAIMABLE if type is GOOGLE_MAPS', function() {
            var status = new ListingAccuracyStatus('NOT_IN_SYNC', 'NOT_CLAIMABLE', 'GOOGLE_MAPS');
            assertDetails(CLAIM_STATUS_DETAILS['NOT_CLAIMABLE'], status);
        });

        it('should have details from SYNC_STATUS_DETAILS for IN_PROGRESS if type is CLAIMABLE and NOT_IN_SYNC', function() {
            var status = new ListingAccuracyStatus('NOT_IN_SYNC', 'CLAIMABLE', 'BMW');
            assertDetails(SYNC_STATUS_DETAILS['IN_PROGRESS'], status);
        });

        it('should not use details from CLAIM_STATUS_DETAILS for NOT_CLAIMABLE if type is not FACEBOOK OR GOOGLE_MAPS', function() {
            var status = new ListingAccuracyStatus('NO_ONLINE_LISTING', 'NOT_CLAIMABLE', 'BMW');
            assertDetails(SYNC_STATUS_DETAILS['NO_ONLINE_LISTING'], status);
        })
    });

    describe("ListingSyncProDirectory", function() {
        it('should infer icon name from source name', function() {
            var directoryType = 'TEST_SOURCE_NAME';
            var lspd = new ListingSyncProDirectory(directoryType, null, {});
            expect(lspd.iconName()).toBe('TestSourceName');
        });

        it('should set icon name from source info if proivded', function() {
            var directoryType = 'HOTFROG';
            var sourceInfo = SERVICE_CONFIG.UBERALL.DIRECTORY_TYPE_TO_SOURCE_MAP[directoryType];
            var lspd = new ListingSyncProDirectory(directoryType, sourceInfo, {});
            expect(lspd.iconName()).toBe('HotFrog');
        })
    });

    describe("InquiryModalDialog", function() {
        describe("doSendMessage", function() {
            var self = this;
            beforeEach(function() {
                self.accountGroupId = 'AG-ANYTHING';
                self.currentUserId = 'ANY-UID';
                self.model = new InquiryModalDialog('arbitraryTitle', 'arbitraryTextForTitle', self.accountGroupId,
                    { 'some': 'salesperson_data' }, self.currentUserId);
            });
            it('should call ajax endpoint with correct params', function() {
                var message = 'arbitrary message for popup';
                var ajaxSpy = spyOn($, 'ajax');
                ajaxSpy.and.callFake(function(options) {
                    expect(options.data.account_group_id).toEqual(self.accountGroupId);
                    expect(options.data.current_user_id).toEqual(self.currentUserId);
                    expect(options.data.message).toEqual(message);
                });
                self.model.doSendMessage(message, false);
                expect(ajaxSpy).toHaveBeenCalled();  // This is a sanity check - not part of the test assertion
            });
            it('should show success message if ajax is success', function() {
                spyOn($, 'ajax').and.callFake(function(ajax) {
                    ajax.success();
                });
                var successSpy = spyOn(self.model, 'showSuccess');
                self.model.doSendMessage('some message', true);
                expect(successSpy).toHaveBeenCalled();
            });
            it('should show error message if ajax is error', function() {
                spyOn($, 'ajax').and.callFake(function(ajax) {
                    ajax.error();
                });
                var errorSpy = spyOn(self.model, 'showError');
                self.model.doSendMessage('some message', true);
                expect(errorSpy).toHaveBeenCalled();
            });
        });
    });

    describe('ListingSyncProService', function(){
        describe('fetchDirectories', function(){
            var self = this;
            beforeEach(function() {
                self.service = new ListingSyncProService('', '', '', '', '', '');
                spyOn(self.service, 'fetchUberallDirectories');
                spyOn(self.service, 'fetchYextDirectories');
            });
            it('should fetch uberall directories when service type is uberall', function() {
                self.service.serviceType = 'Uberall';
                self.service.fetchDirectories();
                expect(self.service.fetchUberallDirectories).toHaveBeenCalled();
            });
            it('should fetch yext directories when service type is yext', function() {
                self.service.serviceType = 'Yext';
                self.service.fetchDirectories();
                expect(self.service.fetchYextDirectories).toHaveBeenCalled();
            });
        });
        describe('fetchYextDirectories', function() {
            beforeEach(function() {
                self.service = new ListingSyncProService('', '', '', '', '', '');
            });
            it('should have all the yext directories set to not activated when isActive is false', function(){
                self.service.isActive = false;
                self.service.fetchYextDirectories();

                expect(self.service.directories().length).toBe(62);

                for(var index in self.service.directories()){
                    expect(self.service.directories()[index].syncStatus).toBe(SYNC_STATUS.NOT_ACTIVATED);
                }
            });
            it('should include PR if feature flag is true and isActive false', function(){
                self.service.isActive = false;
                self.service.showPublicReputationFeatureFlag = true;
                self.service.fetchYextDirectories();

                expect(self.service.directories().length).toBe(63);

                for(var index in self.service.directories()){
                    expect(self.service.directories()[index].syncStatus).toBe(SYNC_STATUS.NOT_ACTIVATED);
                }
            });
            it('should have all the yext directories set to in sync when isActive is true', function(){
                response = {
                    data: {
                        'BROWNBOOKNET': {
                            'listingUrl': 'http://brownbook.net/business/40271006/hunter-rv-center',
                            'listingType': 'BROWNBOOKNET'
                        },
                        'OPENDI': {
                            'listingUrl': 'https://www.opendi.us/sapulpa-ok/7484578.html',
                            'listingType': 'OPENDI'
                        }
                    }
                };
                spyOn($, "getJSON").and.callFake(function(url, callback) {
                    callback(response);
                    return {
                        fail: function() {}
                    };
                });
                self.service.isActive = true;
                self.service.fetchYextDirectories();

                expect(self.service.directories().length).toBe(62);

                for(var index in self.service.directories()){
                    expect(self.service.directories()[index].syncStatus).toBe(SYNC_STATUS.IN_SYNC);
                }
            });
            it('should have public reputation when isActive is true and feature flag', function(){
                response = {
                    data: {
                        'PUBLICREPUTATION': {
                            'listingUrl': 'https://www.publicreputation.com/taqueria-tres-magueyes-temple-texas-2',
                            'listingType': 'PUBLICREPUTATION'
                        },
                        'OPENDI': {
                            'listingUrl': 'https://www.opendi.us/sapulpa-ok/7484578.html',
                            'listingType': 'OPENDI'
                        }
                    }
                };
                spyOn($, "getJSON").and.callFake(function(url, callback) {
                    callback(response);
                    return {
                        fail: function() {}
                    };
                });
                self.service.isActive = true;
                self.service.showPublicReputationFeatureFlag = true;
                self.service.fetchYextDirectories();

                expect(self.service.directories().length).toBe(63);

                for(var index in self.service.directories()){
                    expect(self.service.directories()[index].syncStatus).toBe(SYNC_STATUS.IN_SYNC);
                }
            });
            it('should have listing urls for brownbook and opendi', function(){
                response = {
                    data: {
                        'BROWNBOOKNET': {
                            'listingUrl': 'http://brownbook.net/business/40271006/hunter-rv-center',
                            'listingType': 'BROWNBOOKNET'
                        },
                        'OPENDI': {
                            'listingUrl': 'https://www.opendi.us/sapulpa-ok/7484578.html',
                            'listingType': 'OPENDI'
                        }
                    }
                };
                spyOn($, "getJSON").and.callFake(function(url, callback) {
                    callback(response);
                    return {
                        fail: function() {}
                    };
                });
                self.service.isActive = true;
                self.service.fetchYextDirectories();

                for(var index in self.service.directories()){
                    if (self.service.directories()[index].name === 'Brownbook.net') {
                        expect(self.service.directories()[index].listingUrl).toBe('http://brownbook.net/business/40271006/hunter-rv-center');
                    }
                    if (self.service.directories()[index].name === 'Opendi') {
                        expect(self.service.directories()[index].listingUrl).toBe('https://www.opendi.us/sapulpa-ok/7484578.html');
                    }
                }

            });
        });
    });
});
