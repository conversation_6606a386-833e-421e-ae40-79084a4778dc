var CHART_DATA = [
    {
        date: '',
        value: 10
    },
    {
        date: '',
        value: 15
    }
];

describe('MS.GoogleMyBusinessInsights', function () {
    describe('getValueFromChartData', function () {
        it('should sum up the values in the chartData', function () {
            expect(getValueFromChartData(CHART_DATA)).toEqual(25);
        });

        it('should return 0 if chartData is falsy', function () {
            expect(getValueFromChartData()).toEqual(0);
            expect(getValueFromChartData(null)).toEqual(0);
            expect(getValueFromChartData([])).toEqual(0);
        });
    });

    describe('addOptions', function () {
        beforeEach(function(){
            var today = moment('2017-06-15').toDate();
            jasmine.clock().mockDate(today);
        });

        it('should contain the list: ["1 week"]', function() {
            var gmbViewModel = new GoogleMyBusinessInsightsViewModel('SPC-123', '2017-05-30');
            var expectedArray = ['1 week'];
            expect(gmbViewModel.availableRanges()).toEqual(expectedArray);
        });
        it('should contain the list: ["1 week", "1 month"]', function() {
            var gmbViewModel = new GoogleMyBusinessInsightsViewModel('SPC-123', '2017-04-30');
            var expectedArray = ['1 week', '1 month'];
            expect(gmbViewModel.availableRanges()).toEqual(expectedArray);
        });
        it('should contain the list: ["1 week", "1 month", "3 months"]', function() {
            var gmbViewModel = new GoogleMyBusinessInsightsViewModel('SPC-123', '2017-02-30');
            var expectedArray = ['1 week', '1 month', '3 months'];
            expect(gmbViewModel.availableRanges()).toEqual(expectedArray);
        });
        it('should contain the list: ["1 week", "1 month", "3 months", "6 months"]', function() {
            var gmbViewModel = new GoogleMyBusinessInsightsViewModel('SPC-123', '2016-08-30');
            var expectedArray = ['1 week', '1 month', '3 months', '6 months'];
            expect(gmbViewModel.availableRanges()).toEqual(expectedArray);
        });
        it('should contain the list: ["1 week", "1 month", "3 months", "6 months", "1 year"]', function() {
            var gmbViewModel = new GoogleMyBusinessInsightsViewModel('SPC-123', '2016-01-30');
            var expectedArray = ['1 week', '1 month', '3 months', '6 months', '1 year'];
            expect(gmbViewModel.availableRanges()).toEqual(expectedArray);
        });
    });
});
