{"name": "listing-directory", "version": "1.0.0", "description": "Listing Directory", "license": "ISC", "author": "<PERSON><PERSON><PERSON><PERSON>", "engines": {"node": ">=5.10", "npm": ">3.0"}, "scripts": {"build": "cross-env NODE_ENV=development node_modules/.bin/webpack --display-chunks --progress", "build:watch": "cross-env NODE_ENV=development node_modules/.bin/webpack --display-chunks --progress --watch", "build:prod": "cross-env NODE_ENV=production node_modules/.bin/webpack --display-chunks --progress", "clean": "del-cli ../src/static/js/app/ --force", "lint": "npm-run-all lint:js lint:ts", "lint:js": "node_modules/.bin/eslint -c node_modules/@vendasta/lint-config/.eslintrc.yml *.js ./src", "lint:ts": "node_modules/.bin/tslint --config node_modules/@vendasta/lint-config/tslint.json --project ./tsconfig.json", "postinstall": "cp node_modules/@vendasta/lint-config/.eslintignore .", "prebuild": "npm-run-all clean lint", "server:dev": "cross-env NODE_ENV=development node_modules/.bin/webpack-dev-server --progress", "server:prod": "cross-env NODE_ENV=production node_modules/.bin/webpack-dev-server --progress", "start": "npm run server:dev", "test": "cross-env NODE_ENV=test node_modules/.bin/karma start --single-run", "test:watch": "cross-env NODE_ENV=test node_modules/.bin/karma start", "ngc": "node_modules/.bin/ngc -p tsconfig.aot.json"}, "dependencies": {"@angular/common": "2.1.1", "@angular/compiler": "2.1.1", "@angular/compiler-cli": "2.1.1", "@angular/core": "2.1.1", "@angular/forms": "2.1.1", "@angular/http": "2.1.1", "@angular/material": "2.0.0-alpha.9-3", "@angular/platform-browser": "2.1.1", "@angular/platform-browser-dynamic": "2.1.1", "@angular/platform-server": "2.1.1", "@angular/router": "3.1.1", "@types/googlemaps": "^3.26.1", "core-js": "^2.4.1", "hammerjs": "^2.0.8", "rxjs": "5.0.0-rc.1", "zone.js": "^0.6.25"}, "devDependencies": {"@types/core-js": "^0.9.30", "@types/googlemaps": "^3.26.1", "@types/jasmine": "^2.5.41", "@types/node": "^6.0.34", "@vendasta/lint-config": "^2.0.0", "autoprefixer": "~6.5.1", "awesome-typescript-loader": "^3.0.0-beta.9", "copy-webpack-plugin": "~3.0.1", "cross-env": "~3.1.3", "css-loader": "~0.25.0", "del-cli": "~0.2.0", "eslint": "~3.8.1", "extract-text-webpack-plugin": "^2.0.0-beta", "html-webpack-plugin": "~2.22.0", "jasmine-core": "~2.5.2", "karma": "~1.3.0", "karma-chrome-launcher": "~2.0.0", "karma-jasmine": "~1.0.2", "karma-phantomjs-launcher": "1.0.1", "karma-sourcemap-loader": "~0.3.7", "karma-teamcity-reporter": "1.0.0", "karma-webpack": "~1.8.0", "node-sass": "~3.10.1", "npm-run-all": "~3.1.1", "postcss-loader": "~1.0.0", "raw-loader": "~0.5.1", "sass-loader": "~4.0.0", "style-loader": "~0.13.1", "tslint": "^4.2.0", "typescript": "2.1.6", "webpack": "2.1.0-beta.27", "webpack-bundle-analyzer": "^1.3.0", "webpack-dev-server": "~1.16.2", "webpack-md5-hash": "~0.0.5"}}