# Angular 2 Seed

Getting Started
---------------

#### Prerequisites
- `node >=5.12`
- `yarn` must be installed globally

#### Quick Start

1. Download a zip of the project from [here](https://github.com/vendasta/angular2-seed/archive/master.zip)
1. Unzip the project, saving it where you want
1. Run the following within in the project folder:

```shell
$ git init
$ yarn install
$ yarn start
```

This will start up a webpack dev server on `localhost:3000`. The dev server will listen for changes you make and automatically refresh the browser window.

For usage in Vendasta projects simply specify `yarn install` as a command to be run by `inv setup`.

Usage
-----

|Script|Description|
|---|---|
|`yarn start`|Start webpack development server @ `localhost:3000`|
|`yarn run build`|Format, lint, test, and build the application to `./target`|
|`yarn run build:prod`|Format, lint, test, and build the production version of the application to `./target`. This means AoT compilation, and compressed JS.|
|`yarn run lint`|Lint `.ts` and `.js` files|
|`yarn run format` |Format `.ts` files with clang-format|
|`yarn test`|Run unit tests with Karma and Jasmine|
|`yarn run test:watch`|Run unit tests with Karma and Jasmine; watch for changes to re-run tests|
|`yarn run ngc`|Runs the Angular AoT compiler, useful for finding template errors|

Features
--------
- Fully bootstrapped Angular 2
  - AoT compilation for the production build
- Linting and formatting using @vendasta/lint-config configuration
  - TSLint, ESLint, and clang-format
- Comes pre-installed with Angular Material
- Jasmine and Karma for testing
- Webpack to create the bundles and provide the dev server
