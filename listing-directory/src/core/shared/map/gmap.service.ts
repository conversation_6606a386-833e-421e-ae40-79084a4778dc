import { Injectable } from '@angular/core';

declare var window: {google: any};

@Injectable()
export class GoogleMapsService {
    public apiKey: string;

    loadGoogleMaps(callback: any): void {
        if (window.google) {
            callback();
        } else {
            const mapsScript = document.getElementById('placesApi');
            if (mapsScript) {
                callback();
                return;
            }
            const script = document.createElement('script');
            script.id = 'mapsApi';
            script.type = 'text/javascript';
            script.onload = callback;
            document.head.appendChild(script);
            script.src = `https://maps.googleapis.com/maps/api/js?v=3.exp&key=${this.apiKey}`;
        }
    }
}
