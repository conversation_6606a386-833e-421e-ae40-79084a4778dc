import { Component, Input, OnInit } from '@angular/core';
import { Listing } from '../../../views/search';
import {GoogleMapsService} from './gmap.service';

// Removes the 'google is not found on window' Typescript error for whatever reason.
// Per: http://stackoverflow.com/questions/18083389/ignore-typescript-errors-property-does-not-exist-on-value-of-type
declare var window: any;

// Google maps marker constants
const MARKER_SIZE = {x: 44, y: 54};
const MARKER_SCALE_FACTOR = 1.8;
const MARKER_SCALED_SIZE = {
    x: MARKER_SIZE.x / MARKER_SCALE_FACTOR,
    y: MARKER_SIZE.y / MARKER_SCALE_FACTOR
};
const DEFAULT_MAP_STYLE = { width: '100%', height: '500px' };
export const DEFAULT_MAP_CENTER = {lat: 38.4530126, lng: -99.9059719};
export const DEFAULT_MAP_ZOOM = 4;
export const DEFAULT_MAP_MAX_ZOOM = 16;

@Component({
    selector: 'map',
    template: `<div id='map' [ngStyle]='_style'></div>`
})
export class MapComponent implements OnInit {
    map: google.maps.Map;
    markers: google.maps.Marker[];
    labelIndex: number = 0;
    infoWindow: google.maps.InfoWindow;


    private mapCenterInput: google.maps.LatLng;
    private zoomLevelInput: number;

    private _listings: Listing[];
    private _mapCenter: google.maps.LatLng;
    private _zoomLevel: number;
    private _style: Object;

    @Input() googleMapsApiKey: string;
    @Input() style: Object;
    @Input() mapType: string;
    @Input() disableMapControls: boolean = false;
    @Input() disableLabels: boolean = false;

    @Input()
    set listings(listings: Listing[]) {
        this._listings = listings;
        if (this.map) {
            this.updateMarkers(listings);
        }
    }
    @Input()
    set mapCenter(center: any) {
        this.mapCenterInput = center;
        if (this.map) {
            this.updateMap();
        }
    }

    @Input()
    set zoomLevel(zoomLevel: number) {
        this.zoomLevelInput = zoomLevel;
        if (this.map) {
            this.updateMap();
        }
    }

    constructor(private gmapsService: GoogleMapsService) { }

    ngOnInit(): void {
        this._style = this.style || DEFAULT_MAP_STYLE;

        this.gmapsService.apiKey = this.googleMapsApiKey;
        this.gmapsService.loadGoogleMaps(this.initMap.bind(this));
        this.markers = [];
    }

    initMap(): void {
        this._mapCenter = new window.google.maps.LatLng(this.mapCenterInput || DEFAULT_MAP_CENTER);
        this._zoomLevel = this.zoomLevelInput || DEFAULT_MAP_ZOOM;
        this.map = new window.google.maps.Map(document.getElementById('map'), {
            center: this._mapCenter,
            zoom: this._zoomLevel,
            mapTypeId: this.mapType || google.maps.MapTypeId.ROADMAP,
            disableDefaultUI: this.disableMapControls,
            maxZoom: DEFAULT_MAP_MAX_ZOOM,
            scrollwheel: !this.disableLabels,
            styles: [
                {
                    featureType: 'all',
                    elementType: 'labels',
                    stylers: [
                        { visibility: this.disableLabels ? 'off' : 'on' }
                    ]
                }, {
                    featureType: 'poi',
                    elementType: 'labels',
                    stylers: [
                        { visibility: 'off' }
                    ]
                }
            ]
        });
    }

    updateMap(): void {
        // When we route back to a page with a Map on it, it will just show grey squares until we trigger a resize...
        google.maps.event.trigger(this.map, 'resize');
        this._mapCenter = new window.google.maps.LatLng(this.mapCenterInput || DEFAULT_MAP_CENTER);
        this._zoomLevel = this.zoomLevelInput || DEFAULT_MAP_ZOOM;
        this.map.setCenter(this._mapCenter);
        this.map.setZoom(this._zoomLevel);
    }

    removeMarkers(): void {
        this.markers.forEach((marker: google.maps.Marker) => {
            marker.setMap(null);
        });
    }

    updateMarkers(points: Listing[]): void {
        // When we route back to a page with a Map on it, it will just show grey squares until we trigger a resize...
        google.maps.event.trigger(this.map, 'resize');
        if (this.markers && this.markers.length > 0) {
            this.labelIndex = 0;
            this.removeMarkers();
            this.markers = [];
        }
        if (points.length === 0) {
            this.map.setCenter(this._mapCenter);
            this.map.setZoom(this._zoomLevel);
            return;
        }

        // Set map bounds - to zoom in the appropriate amount on the map markers
        let bounds = new window.google.maps.LatLngBounds();
        points.forEach((point: Listing) => {
            if (point.latitude != 0 && point.longitude != 0) {
                bounds.extend(new window.google.maps.LatLng({
                    lat: point.latitude,
                    lng: point.longitude
                }));
            }
        });
        this.map.fitBounds(bounds);

        this.infoWindow = new window.google.maps.InfoWindow({
            // offset the info window half the width of the marker to the left
            pixelOffset: new window.google.maps.Size(MARKER_SCALED_SIZE.x / -2, 0)
        });

        points.forEach((point: Listing, index: number) => {
            if (point.latitude && point.longitude) {
                this.addMarkerToMapFromPoint(point, index);
            }
        });
    }

    addMarkerToMapFromPoint(point: Listing, index: number): void {
        const timeoutMethod = () => {
            const marker = new window.google.maps.Marker({
                position: {lat: point.latitude, lng: point.longitude},
                icon: {
                    url: `/static/images/pins/pin${++this.labelIndex}.png`,
                    anchor: new google.maps.Point(MARKER_SCALED_SIZE.x / 2, MARKER_SCALED_SIZE.y),
                    size: new google.maps.Size(MARKER_SIZE.x, MARKER_SIZE.y),
                    scaledSize: new window.google.maps.Size(MARKER_SCALED_SIZE.x, MARKER_SCALED_SIZE.y)
                },
                title: point.name,
                map: this.map,
                animation: google.maps.Animation.DROP
            });
            this.markers.push(marker);
            this.registerInfoWindowClickEvent(marker, point);
        };
        // stagger the pins being added to have them drop down one-by-one
        window.setTimeout(timeoutMethod, index * 50);
   }

    registerInfoWindowClickEvent(marker: google.maps.Marker, listingInfo: Listing): void {
        marker.addListener('click', () => {
            this.infoWindow.close();
            let listingUrl = '#';
            if (listingInfo.listingUrl && !listingInfo.listingUrl.startsWith('http')) {
                listingUrl = 'http://' + listingInfo.listingUrl;
            }
            let infoWindowContent: string = `Info not found`;
            if (listingInfo) {
                infoWindowContent = MapComponent.getInfoWindowContent(listingInfo, listingUrl);
            }
            this.infoWindow.setContent(infoWindowContent);
            this.infoWindow.open(this.map, marker);
        });
    }

    static getInfoWindowContent(listingInfo: Listing, listingUrl: string): string {
        return `
            <div class="item-content" style="padding-left: 0; width:240px">
                <div class="item-desc">
                    <a target="_blank" href="${listingUrl}">
                        <h3 class="name" style="margin: 0">${listingInfo.name}</h3>
                    </a>
                    <span>${listingInfo.address}</span>
                </div>
            </div>
            <div class="flex-container">
                <a href="tel:${listingInfo.phoneNumber}" class="flex-item" style="border-right: 1px solid rgba(5,126,193,0.4);"><span class="icon icon-phone"></span><br/> Call</a> <br />
                <a href="${listingInfo.website}" target="_blank" class="flex-item" style="border-right: 1px solid rgba(5,126,193,0.4);"><span class="icon icon-location"></span><br/> Website</a> <br />
                <a href="https://www.google.ca/maps/?q=${MapComponent.getListingNameWithoutSpaces(listingInfo)}&daddr=${listingInfo.latitude},${listingInfo.longitude}" target="_blank" class="flex-item"><span class="icon icon-directions"></span><br/> Directions</a>
            </div>
        `;
    }

    static getListingNameWithoutSpaces(point: Listing): string {
        return point.name.replace(' ', '+');
    }
}
