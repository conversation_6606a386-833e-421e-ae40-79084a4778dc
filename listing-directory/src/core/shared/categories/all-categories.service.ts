import { Injectable, Inject } from '@angular/core';
import { Http, URLSearchParams } from '@angular/http';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class AllCategoriesService {
    private url = '/ajax/all-categories/';

    constructor(private http: Http) { }

    getCategories(orderBy: string = 'name'): Observable<any> {
        let params: URLSearchParams = new URLSearchParams();
        params.set('orderBy', orderBy);
        return this.http.get(this.url, { search: params })
            .map(res => res.json());
    }
}
