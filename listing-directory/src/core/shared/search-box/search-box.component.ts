import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

import { Subscription } from 'rxjs/Subscription';

@Component({
  selector: 'search-box',
  template: require('./search-box.component.html')
})
export class SearchBoxComponent implements OnInit, OnDestroy {
    currentSearchValue: string;
    currentCategory: string;
    currentCategoryName: string;
    queryParamsSubscription: Subscription;

    taxIds: Taxonomy[] = [
        {key: '', value: 'All Categories'},
        {key: 'active', value: 'Active Life'},
        {key: 'arts', value: 'Arts & Entertainment'},
        {key: 'auto', value: 'Automotive'},
        {key: 'beautysvc', value: 'Beauty & Spas'},
        {key: 'education', value: 'Education'},
        {key: 'eventservices', value: 'Event Planning & Services'},
        {key: 'financialservices', value: 'Financial Services'},
        {key: 'food', value: 'Food'},
        {key: 'health', value: 'Health & Medical'},
        {key: 'homeservices', value: 'Home Services'},
        {key: 'hotelstravel', value: 'Hotels & Travel'},
        {key: 'industgoodsmanu', value: 'Industrial Goods and Manufacturing'},
        {key: 'localservices', value: 'Local Services'},
        {key: 'massmedia', value: 'Mass Media'},
        {key: 'mineag', value: 'Mining & Agriculture'},
        {key: 'nightlife', value: 'Nightlife'},
        {key: 'pets', value: 'Pets'},
        {key: 'professional', value: 'Professional Services'},
        {key: 'publicservicesgovt', value: 'Public Services & Government'},
        {key: 'realestate', value: 'Real Estate'},
        {key: 'religiousorgs', value: 'Religious Organizations'},
        {key: 'restaurants', value: 'Restaurants'},
        {key: 'shopping', value: 'Shopping'},
        {key: 'transportation', value: 'Transportation'},
        {key: 'other', value: 'Other'}
    ];

    constructor(private router: Router, private route: ActivatedRoute) {
    }

    ngOnInit(): void {
        this.queryParamsSubscription = this.route.queryParams
            .map(({query, category}) => { return { query: query || '', category: category || '' }; })
            .subscribe(({query, category}) => {
                this.currentSearchValue = query;
                this.currentCategory = category;
                if (this.currentCategory === '') {
                    this.currentCategoryName = this.taxIds[0].value;
                }
                else {
                    this.taxIds.forEach((taxId) => {
                        if (taxId.key === this.currentCategory) {
                            this.currentCategoryName = taxId.value;
                        }
                    });
                }
            });
    }

    private categoryChanged(event: any, selectedCategory: string): void {
        this.search(this.currentSearchValue, 1, selectedCategory);
    }

    private searchChanged(value: string): void {
        this.currentSearchValue = value;
        this.search(value, 1, this.currentCategory);
    }

    private search(query: string, page: number, category: string): void {
        let params: QueryParams = {};
        if (query !== '') {
            params.query = query;
        }
        if (page > 1) {
            params.page = page;
        }
        if (category !== '') {
            params.category = category;
        }
        this.router.navigate(['/search'], {queryParams: params});
    }

    ngOnDestroy(): void {
        this.queryParamsSubscription.unsubscribe();
    }
}

export interface QueryParams {
    query?: string;
    page?: number;
    category?: string;
}

interface Taxonomy {
    key: string;
    value: string;
}
