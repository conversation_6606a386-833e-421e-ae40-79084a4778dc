<div class="control-group">
    <div class="multi-input search-box-wrapper">
        <span class="control-input-wrapper static-styled-select category-dropdown">
            <select
                    #category (change)="categoryChanged($event, category.value)"
                    [(value)]="currentCategory"
            >
                <option
                    *ngFor="let taxId of taxIds"
                        [value]="taxId.key">
                    {{ taxId.value }}
                </option>

            </select>
            <span>{{ currentCategoryName }}</span>
        </span>
        <input-debounce
                class="control-input"
                id="name"
                placeholder="Search"
                [initialValue]="currentSearchValue"
                (value)="searchChanged($event)"
        >
        </input-debounce>
    </div>
</div>
