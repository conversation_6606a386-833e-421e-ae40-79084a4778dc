import { Component, Input } from '@angular/core';

const DEFAULT_ARRAY = [0,0,0,0,0];

@Component({
    selector: 'rating-stars',
    template: `
     <div>
        <span class="star-rating " *ngFor="let i of _ratingStarArray">
            <span [class.colored-star]="i" class="icon icon-star"></span>
        </span>
     </div>
     `
})
export class RatingStarComponent {
    private _ratingStarArray: number[];
    @Input() ratingStarArray: number[] = DEFAULT_ARRAY;

    ngOnInit(): void {
        this._ratingStarArray = this.ratingStarArray || DEFAULT_ARRAY;
    }
}
