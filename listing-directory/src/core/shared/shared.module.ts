import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {RouterModule} from '@angular/router';

import {SearchBoxComponent} from './search-box';
import {RatingStarComponent} from './rating-star';
import {PrependHttpPipe} from './prepend-http';
import {MapComponent, GoogleMapsService} from './map';
import {InputDebounceComponent} from './input-debounce';
import {CategoryCardComponent, MoreCategoriesCardComponent} from './category-card';
import {CategoryFeedComponent} from './category-feed';

import {AllCategoriesService, CategoryService} from './categories';
import {GeoService} from './geo';

@NgModule({
  imports: [CommonModule, FormsModule, RouterModule],
  declarations: [SearchBoxComponent, RatingStarComponent, PrependHttpPipe, MapComponent, InputDebounceComponent,
                 CategoryCardComponent, MoreCategoriesCardComponent, CategoryFeedComponent],
  exports: [SearchBoxComponent, RatingStarComponent, PrependHttpPipe, MapComponent, InputDebounceComponent,
            CategoryCardComponent, MoreCategoriesCardComponent, CategoryFeedComponent],
  providers: [GeoService, AllCategoriesService, CategoryService, GoogleMapsService]
})
export class SharedModule {
}
