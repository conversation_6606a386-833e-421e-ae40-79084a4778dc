import { Injectable } from '@angular/core';
import { Http } from '@angular/http';
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/operator/map';
import 'rxjs/add/operator/pluck';
import { Geo } from './geo';

@Injectable()
export class GeoService {
    private geoUrl = '/ajax/geo-center/';

    constructor(private http: Http) {
    }

    getGeoInformation(): Observable<Geo> {
        return this.http.get(this.geoUrl)
            .map(res => res.json())
            .pluck('data');
    }
}
