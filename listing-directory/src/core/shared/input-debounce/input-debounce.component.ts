// Component from https://manuel-rauber.com/2015/12/31/debouncing-angular-2-input-component/

import {Component, Input, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit, OnDestroy} from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { Subscription } from 'rxjs/Subscription';
import 'rxjs/add/observable/fromEvent';
import 'rxjs/add/operator/debounceTime';
import 'rxjs/add/operator/distinctUntilChanged';

@Component({
    selector: 'input-debounce',
    template: `<input 
        #search 
        class="ld-search-box" 
        type="text" 
        [value]="initialValue" 
        [placeholder]="placeholder" 
        [(ngModel)]="inputValue">`
})
export class InputDebounceComponent implements AfterViewInit, OnDestroy {
    @Input() placeholder: string;
    @Input() delay: number = 300;
    @Output() value: EventEmitter<string> = new EventEmitter<string>();
    @Input()
    set initialValue(value: string) {
        this.inputValue = value;
    }
    @ViewChild('search') element: ElementRef;

    private inputValue: string;
    private keySubscription: Subscription;

    ngAfterViewInit(): void {
        this.keySubscription = Observable.fromEvent(this.element.nativeElement, 'keyup')
            .map(() => this.inputValue)
            .debounceTime(this.delay)
            .distinctUntilChanged()
            .subscribe(input => {
                this.value.emit(input);
            });
    }

    ngOnDestroy(): void {
        this.keySubscription.unsubscribe();
    }
}
