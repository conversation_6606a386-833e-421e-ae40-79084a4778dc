import {DirectoryComponent} from "./directory.component";


describe ('SearchComponent', () => {
    describe ('getListings', () => {
        beforeEach(() => {
            this.subSpy = jasmine.createSpyObj('subscription', ['unsubscribe']);
            this.dc = new DirectoryComponent({
                getListings: (url, searchText, cursor) => {
                    return {subscribe: (success, fail, complete) => {
                        return this.subSpy;
                } }; }
            }, {subscribe: (params) => { }}, {subscribe: (params) => {}}); // Add two dummy observables for now.
        });

        it("calls listings subscription unsubscribes if we're already subscribed", () => {
            this.dc.subscription = this.subSpy;
            this.dc.searchListings('one', 'two', 3);
            expect(this.dc.subscription.unsubscribe).toHaveBeenCalled();
        })
    })
});
