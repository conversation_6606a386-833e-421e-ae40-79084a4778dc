import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {
    DEFAULT_MAP_CENTER,
    DEFAULT_MAP_ZOOM,
    Geo,
    GeoService,
    Category,
    AllCategoriesService
} from '../../core/shared';


@Component({
    selector: 'listing-directory',
    template: require('./directory.component.html')
})
export class DirectoryComponent implements OnInit {
    placeName: string;
    geo: Geo;
    categories: Category[];
    other: string[];
    center: Object;
    zoom: number;

    googleMapsApiKey: string = 'AIzaSyB90ebQ9ZBzAKdMGcQtQFxkc3KRw5ggKhE';
    style: Object = {width: '100%', height: '100%'};

    constructor(private geoService: GeoService,
                private allCategoriesService: AllCategoriesService,
                private router: Router) {
    }

    ngOnInit(): void {
        this.geoService.getGeoInformation().subscribe(
            response => {
                this.geo = response;

                if (this.geo.name === 'DEFAULT') {
                    this.center = {lat: DEFAULT_MAP_CENTER.lat, lng: DEFAULT_MAP_CENTER.lng};
                    this.zoom = DEFAULT_MAP_ZOOM;
                    this.placeName = '';
                } else {
                    this.center = {lat: this.geo.lat, lng: this.geo.long};
                    this.zoom = this.geo.zoom;
                    this.placeName = this.geo.name;
                }
            },
            error => console.log(error)
        );

        this.allCategoriesService.getCategories('popularity').subscribe(
            response => {
                this.categories = response.data.slice(0, 5);
            },
            error => console.log(error)
        );
    }

    private searchChanged($event: any): void {
        if ($event.target.value) {
            this.router.navigate(['/search'], {
                queryParams: {
                    query: $event.target.value,
                    page: 1
                }
            });
        }
    }
}
