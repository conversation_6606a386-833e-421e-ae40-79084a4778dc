<div class="search-section row">
    <div class="page-wrap">
        <div class="row">
            <div class="col col-xs-12 col-md-8 col-md-push-2">
                <search-box></search-box>
            </div>
        </div>
    </div>
</div>
<div class="page-wrap">
    <div class="row row-gutters">
        <div class="col col-xs-12 col-md-12">
            <h1>
                <span *ngIf="numResults > 0">{{ numResults }} {{ numResults > 1 ? "results" : "result" }}
                    <span *ngIf="searchText">for "{{ searchText }}"</span>
                </span>
            </h1>
        </div>
    </div>
    <div class="row row-gutters">
        <div class="items col col-xs-12 col-md-8">
            <div *ngIf="loading">
                <div class="loading"></div>
            </div>
            <div *ngIf="!loading">
                <business-feed
                        [listings]="listings"
                        [numPages]="numPages"
                        [cursor]="cursor"
                        [defaultPageSize]="defaultPageSize"
                        [searchText]="searchText"
                        [currentCategory]="searchCategory">
                </business-feed>
            </div>
        </div>
        <div class="col col-xs-12 col-md-4">
            <map
                    (window:scroll)="onScroll($event)"
                    (window:resize)="onResize($event)"
                    [listings]="listings"
                    [googleMapsApiKey]="googleMapsApiKey"
            >
            </map>
        </div>
    </div>
</div>
