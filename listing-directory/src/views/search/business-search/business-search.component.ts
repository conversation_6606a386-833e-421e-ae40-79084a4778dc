import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs/Subscription';
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/operator/switchMap';

import {
    Listing, ListingService
} from '../listing';

const DEFAULT_PAGE_SIZE = 10;
declare var window: any;

@Component({
    selector: 'business-search',
    template: require('./business-search.component.html')
})
export class SearchComponent implements OnInit, OnDestroy {
    listings: Listing[] = [];
    numPages: number;
    loading: boolean;
    cursor: number;
    searchText: string;
    searchCategory: string;
    numResults: number;
    defaultPageSize: number = DEFAULT_PAGE_SIZE;
    subscription: Subscription;
    querySubscription: Subscription;
    googleMapsApiKey: string = 'AIzaSyB90ebQ9ZBzAKdMGcQtQFxkc3KRw5ggKhE';

    constructor(private listingService: ListingService,
                private route: ActivatedRoute) { }

    ngOnInit(): void {
        this.querySubscription = this.route.queryParams
            .map(params => {
                const {page, query, category} = params;
                let pageNum = (parseInt(page, 10) - 1) * DEFAULT_PAGE_SIZE;
                if (!pageNum) {
                    pageNum = 0;
                }
                this.searchText = query;
                this.searchCategory = category;
                this.loading = true;
                return {pageNum, query, category};
            })
            .switchMap(({pageNum, query, category}) => this.searchListings(null, query, category, pageNum))
            .subscribe(
                response => {
                    this.listings = response.data;
                    this.numResults = parseInt(response.totalResults, 10);
                    this.numPages =  Math.ceil(parseFloat((parseInt(response.totalResults, 10) / this.defaultPageSize).toFixed(1)));
                    this.loading = false;
                },
                error => console.log(error),
                () => this.loading = false
            );
    }

    public searchListings(url: string, searchText: string, searchCategory: string, cursor: number): Observable<any> {
        this.numResults = 0;

        this.loading = true;
        this.cursor = cursor;
        return this.listingService.getListings(url, searchText, searchCategory, cursor);
    }

    private onScroll(): void {
        const map = document.getElementById('map');
        const items = document.getElementsByClassName('items')[0] as HTMLElement;

        if (map.offsetWidth !== items.offsetWidth - 20) {

            const atTop: boolean = items.getBoundingClientRect().top > 0;
            const atBottom: boolean = items.getBoundingClientRect().bottom <= map.getBoundingClientRect().bottom;
            const scrolledAboveMap: boolean = map.getBoundingClientRect().top > 16;

            if (!atTop) {
                if (atBottom && !scrolledAboveMap) {
                    map.style.position = 'relative';
                    map.style.top = '0';
                    map.style.width = 'auto';
                    map.parentElement.parentElement.style.display = 'flex';
                    map.parentElement.parentElement.style.justifyContent = 'flex-end';
                    map.parentElement.parentElement.style.flexDirection = 'column';
                }
                else {
                    const fixedWidthSize = map.parentElement.parentElement.offsetWidth - 20;
                    map.parentElement.parentElement.style.display = null;
                    map.parentElement.parentElement.style.justifyContent = null;
                    map.parentElement.parentElement.style.flexDirection = null;
                    map.style.position = 'fixed';
                    map.style.width = fixedWidthSize + 'px';
                    map.style.top = '16px';
                }
            }
            else {
                map.style.position = 'relative';
                map.style.top = '0';
                map.style.width = 'auto';
            }
        }
        else {
            map.style.position = 'relative';
            map.style.top = '0';
            map.style.width = 'auto';
        }
    }

    private onResize(): void {
        const map = document.getElementById('map');
        const items = document.getElementsByClassName('items')[0];

        const atTop: boolean = items.getBoundingClientRect().top > 0;
        const atBottom: boolean = items.getBoundingClientRect().bottom <= map.getBoundingClientRect().bottom;

        if (!atTop && !atBottom) {
            const fixedWidthSize = map.parentElement.parentElement.offsetWidth - 20;
            map.style.width = fixedWidthSize + 'px';
            this.onScroll();
        }
    }

    ngOnDestroy(): void {
        this.querySubscription.unsubscribe();
    }
}

