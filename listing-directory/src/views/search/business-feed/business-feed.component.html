<div *ngIf="listings.length == 0">
    <h1>No search results</h1>
</div>
<div *ngFor="let listing of listings; let i = index">
    <business-card [listing]="listing" [mapNumber]="i + 1"></business-card>
    <br />
</div>

<div class="pagination col col-xs-12">
    <span *ngIf="pages.length > 1">
        <button class="btn btn-primary"
                [disabled]="!cursor"
                [class.btn-disabled]="!cursor"
                (click)="goToPage(currentPageNumber - 1)">
            <i class="icon icon-left"></i>
        </button><!--
     --><span *ngFor="let page of pages" class="hide-pages-on-small">
            <button class="btn btn-primary"
                    [class.btn-secondary]="page==currentPageNumber"
                    (click)="goToPage(page)">
                {{ page+1 }}
            </button>
        </span><!--
     --><button class="btn btn-primary"
                [disabled]="(currentPageNumber + 1) >= numPages"
                [class.btn-disabled]="(currentPageNumber + 1) >= numPages"
                (click)="goToPage(currentPageNumber + 1)">
            <i class="icon icon-right"></i>
        </button>
    </span>
</div>

