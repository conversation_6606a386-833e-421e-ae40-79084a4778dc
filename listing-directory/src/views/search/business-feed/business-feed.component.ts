import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

import {
    Listing,
    BusinessCardComponent
} from '../../search';

@Component({
    selector: 'business-feed',
    template: require('./business-feed.component.html')
})
export class BusinessFeedComponent implements OnInit {
    @Input() listings: Listing[];
    @Input() numPages: number;
    @Input() cursor: number;
    @Input() defaultPageSize: number;
    @Input() searchText: string;
    @Input() currentCategory: string;
    @Output() goToPageEmitter: EventEmitter<any> = new EventEmitter();

    currentPageNumber: number;
    pages: number[] = [];

    constructor(private router: Router, private route: ActivatedRoute) {}

    ngOnInit(): void {
        this.currentPageNumber = this.cursor / this.defaultPageSize;
        if (this.currentPageNumber <= 4) {
            for (let i = 0; i < Math.min(10, this.numPages); i++) {
                this.pages.push(i);
            }
        } else if (this.currentPageNumber > 4 && (this.numPages - this.currentPageNumber) > 5) {
            for (let i = this.currentPageNumber - 4; i < this.currentPageNumber + 6; i++) {
                this.pages.push(i);
            }
        } else {
            for (let i = Math.max(this.numPages - 10, 1); i < this.numPages; i++) {
                this.pages.push(i);
            }
        }
    }

    private goToPage($event: number): void {
        this.currentPageNumber = $event;
        let queryParams: any = {
            page: ($event + 1)
        };

        if (this.searchText) {
            queryParams.query = this.searchText;
        }
        if (this.currentCategory) {
            queryParams.category = this.currentCategory;
        }

        this.router.navigate(['/search'], {
            queryParams
        });
    }
}
