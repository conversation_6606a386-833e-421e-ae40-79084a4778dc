import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';

import {SharedModule} from '../../core/shared';

import {BusinessCardComponent} from './business-cards';
import {BusinessFeedComponent} from './business-feed';
import {SearchComponent} from './business-search';
import {Listing, ListingService} from './listing';

@NgModule({
  declarations: [BusinessCardComponent, BusinessFeedComponent, SearchComponent],
  imports: [CommonModule, SharedModule],
  providers: [ListingService]
})
export class SearchModule {
}
