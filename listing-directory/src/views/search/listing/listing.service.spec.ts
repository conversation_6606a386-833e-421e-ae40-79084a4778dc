import { ListingService } from "./listing.service";
import { Response, ResponseOptions } from "@angular/http";
import raw = require("core-js/fn/string/raw");


describe('ListingService', () => {

    beforeEach(() => {
        this.listingService = new ListingService({});
    });
    
    describe('extractData', () => {
        it('should return a response parsed as json', () => {
           let options = new ResponseOptions({
               status: 200,
               body: {'data': [1, 2, 3]}
           });
            let resp = new Response(options);
            let result = resp.json();
            expect(result.data).toEqual([1, 2, 3]);
        });
    });

    describe('deserializeRecord', () => {
        
        it('should return an entity with the provided attributes', () => {
            let rawRecord = {
                'company_name': 'Walmart',
                'address': '1 Main St.',
                'city': 'Saskatoon',
                'zip': 'S7H2K3',
                'state': 'SK',
                'work_number': '3065551234',
                'latitude': '124.245',
                'longitude': '89.1234',
                'logo_url': '/static/images/Profile.svg',
                'website': 'http://google.com',
                'average_review_score': '5.0',
                'number_of_reviews': '9001',
                'location_page_url': 'http://google.ca'
            }
            let result = this.listingService.deserializeRecord(rawRecord);
            expect(result.name).toEqual(rawRecord.company_name);
            expect(result.address).toEqual(rawRecord.address);
            expect(result.city).toEqual(rawRecord.city);
            expect(result.zip).toEqual(rawRecord.zip);
            expect(result.state).toEqual(rawRecord.state);
            expect(result.phoneNumber).toEqual(rawRecord.work_number);
            expect(result.latitude).toEqual(rawRecord.latitude);
            expect(result.longitude).toEqual(rawRecord.longitude);
            expect(result.logo).toEqual(rawRecord.logo_url);
            expect(result.website).toEqual(rawRecord.website);
            expect(result.averageReviewScore).toEqual(rawRecord.average_review_score);
            expect(result.numberOfReviews).toEqual(rawRecord.number_of_reviews);
            expect(result.listingUrl).toEqual(rawRecord.location_page_url);
        })

    });

});
