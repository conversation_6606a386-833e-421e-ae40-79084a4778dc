import { Injectable } from '@angular/core';
import { Http, URLSearchParams } from '@angular/http';
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/operator/map';

import {Listing, ListingApiResponse} from './listing';

@Injectable()
export class ListingService {
    private url: string = '/ajax/listing-feed/';

    constructor(private _http: Http) { }

    getListings(nextUrl: string, searchText: string = '', searchCategory: string = '', cursor: number = 0): Observable<any> {
        let url = nextUrl || this.url;
        let params = new URLSearchParams();

        if (nextUrl) {
            return this._http.get(url).map(res => res.json());
        }

        params.set('searchTerm', searchText);
        params.set('cursor', String(cursor));
        params.set('searchCategory', searchCategory);
        return this._http.get(url, {search: params})
            .map(res => res.json())
            .map(res => { res.data = res.data.map(ListingService.deserializeRecord); return res; });
    }

    static deserializeRecord(rawItem: ListingApiResponse): Listing {
        return {
            name: rawItem.company_name,
            address: rawItem.address,
            city: rawItem.city,
            zip: rawItem.zip,
            state: rawItem.state,
            phoneNumber: rawItem.work_number,
            formattedWorkNumber: rawItem.formatted_work_number,
            latitude: rawItem.latitude,
            longitude: rawItem.longitude,
            logo: rawItem.logo_url,
            website: rawItem.website,
            averageReviewScore: rawItem.average_review_score,
            ratingStarArray: rawItem.rating_star_array,
            numberOfReviews: rawItem.number_of_reviews,
            listingUrl: rawItem.location_page_url,
            msid: rawItem.msid
        };
    }
}
