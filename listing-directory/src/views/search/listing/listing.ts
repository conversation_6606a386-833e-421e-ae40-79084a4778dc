export interface Listing {
    name: string;
    address: string;
    city: string;
    zip: string;
    state: string;
    phoneNumber: string;
    formattedWorkNumber: string;
    latitude: number;
    longitude: number;
    logo: string;
    website: string;
    averageReviewScore: number;
    ratingStarArray: number[];
    numberOfReviews: number;
    listingUrl: string;
    msid: string;
}

export interface ListingApiResponse {
    company_name: string;
    address: string;
    city: string;
    zip: string;
    state: string;
    work_number: string;
    formatted_work_number: string;
    latitude: number;
    longitude: number;
    logo_url: string;
    website: string;
    average_review_score: number;
    rating_star_array: number[];
    number_of_reviews: number;
    location_page_url: string;
    msid: string;
}
