import { Component } from '@angular/core';
import { Router } from '@angular/router';
import {
    SearchBoxComponent,
    CategoryFeedComponent,
    AllCategoriesService,
    Category
} from '../../core/shared';

@Component({
    selector: 'all-categories',
    template: require('./all-categories.component.html')
})
export class AllCategoriesComponent {
    categories: Category[];

    constructor(private router: Router,
                private allcategoriesService: AllCategoriesService) {}

    ngOnInit(): void {
        this.allcategoriesService.getCategories().subscribe(
            response => {
                this.categories = response.data;
            },
            error => console.log(error)
        );
    }

    private searchChanged($event: any): void {
        if ($event.target.value) {
            this.router.navigate(['/search'], {
                queryParams: {
                    query: $event.target.value,
                    page: 1
                }
            });
        }
    }
}
