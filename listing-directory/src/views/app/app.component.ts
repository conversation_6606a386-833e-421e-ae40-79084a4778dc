import { Component, ElementRef } from '@angular/core';

@Component({
  selector: 'listing-directory',
  template: `
    <header class="page-header">
        <span *ngIf="whiteLabelLogo !== 'None'">
            <a [routerLink]="['/directory']"><img src="{{ whiteLabelLogo }}" class="logo"/></a>
        </span>
        <div class="product-name"><a [routerLink]="['/directory']">{{ whiteLabelProductName }}</a></div>
    </header>
    <router-outlet></router-outlet>
    <footer class="site-footer">
        <div class="page-wrap">
            Provided by {{ whiteLabelPartnerName }}
        </div>
    </footer>
  `
})
export class AppComponent {
    whiteLabelColor: string;
    whiteLabelLogo: string;
    whiteLabelPartnerName: string;
    whiteLabelProductName: string;

    constructor(el: ElementRef) {
        const nativeElement = el.nativeElement;
        this.whiteLabelColor = nativeElement.getAttribute('whiteLabelColor');
        this.whiteLabelLogo = nativeElement.getAttribute('whiteLabelLogo');
        this.whiteLabelPartnerName = nativeElement.getAttribute('whiteLabelPartnerName');
        this.whiteLabelProductName = nativeElement.getAttribute('whiteLabelProductName');
    }
}
