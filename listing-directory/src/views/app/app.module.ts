import {NgModule} from '@angular/core';
import {FormsModule} from '@angular/forms';
import {MaterialModule} from '@angular/material';
import {BrowserModule} from '@angular/platform-browser';
import {RouterModule} from '@angular/router';

import {ApiService} from '../../core/api';
import {ProjectService} from '../../core/projects';
import {routes} from '../routes';

import {DirectoryModule} from '../directory';
import {SharedModule} from '../../core/shared';
import {SearchModule} from '../search';
import {AppComponent} from './app.component';
import {AllCategoriesComponent} from '../all-categories';

@NgModule({
  declarations: [AppComponent, AllCategoriesComponent],
  imports: [BrowserModule, RouterModule.forRoot(routes), FormsModule, MaterialModule.forRoot(),
            DirectoryModule, SharedModule, SearchModule],
  bootstrap: [AppComponent],
  providers: [ApiService, ProjectService]
})
export class AppModule {
}
