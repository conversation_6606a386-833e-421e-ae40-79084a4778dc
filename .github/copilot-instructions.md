This project is going through a Python 2.7 to 3 upgrade. When looking for or fixing bugs, always consider that there might be leftover 2.7 syntax issues or anything else related to the upgrade (order of sets and dicts, etc).

In the process, it is also going through a webapp2 to flask upgrade. Be sure to keep a lookout for any leftover webapp2 syntax like self.request, etc. When upgrading to flask, we always prefer to use MethodView classes, since it very closely matches webapp2's class based handlers.

All minimock use should be upgraded to unittest.mock. Note that I always prefer to use patch.object over patch.

All proposed line fixes should be kept to 120 characters or less.
