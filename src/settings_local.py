"""
Copy and rename this file to settings_local.py for local development.
"""
import vauth.foundational_products

# webapp2 app configuration settings
SESSION_COOKIE_NAME = '__ms-local'
SSL_ENABLED = False
SECRET_KEY = 'cGcSPKNTLkFCDNPoBaK7Bb4WFvsz1VVB'

PARTNER_CENTRAL_HOST = 'http://localhost:8081'

SOCIAL_MARKETING_ROOT_URL = 'http://localhost:8084/'
SOCIAL_MARKETING_SECURE_ROOT_URL = 'https://localhost:8084/'

SALES_TOOL_ROOT_URL = 'localhost:8086/'

VENDASTA_FRONTEND_HOST = '//vff-test.appspot.com'

DEVELOPER_CENTRAL_HOST = 'localhost:8096'

SECURE_HOST = 'localhost:8085'

ENVIRONMENT_NAME = 'local'

GOOGLE_ANALYTICS_TRACKING_CODE = 'UA-53196379-8'
GOOGLE_TAG_MANAGER_AUTH_CODE = 'IWYD7CQ1iKgtl8NWTb16QQ'
GOOGLE_TAG_MANAGER_PREVIEW = 'env-7'

# use to generate a [pid].default-domain.com hostname if no hostname provided during partner creation
DEFAULT_DOMAIN = 'localhost:8085'

# VSocial Configuration
from coresdk.keys import API_KEY as VS_KEY
VSOCIAL_USER = 'ms'
VSOCIAL_API = '4dad1724c0c4f88d6c0c191e447b8da8'
VSOCIAL_FB_APP_ID = 'FB-APP-ID'

# VBC Configuration
from app.domain.vbcsdk_keys import API_KEY as VBC_KEY
VBC_USER = 'MS'
VBC_API = 'dCwgcGxlYXNlIGVuY29kZSE'
VBC_CONFIG = VBC_KEY.LOCAL

AA_API_USER = 'MS'
AA_API_KEY = 'Z2HTTGAYBZINZBBBWVPU'
AA_TARGET = 'local'

# VPubsub Configurations
VPUBSUB_SCHEME = 'http://'
VPUBSUB_DEVELOPER_KEY = 'AIzaSyCApWSLm3KGY5SKMN-aY2ylBrWBOw26Ls4'
VPUBSUB_STATIC_ROUTES = {
    'interest-in-listing-sync-pro-v1': [
        {
            'endpoint': VPUBSUB_SCHEME + SALES_TOOL_ROOT_URL + 'subscribe/interested-in-listing-sync-pro',
            'subscription': 'salestool-test/interested-in-listing-sync-pro'
        }
    ]
}

GOOGLE_API_KEY = VPUBSUB_DEVELOPER_KEY

VAUTH_ENV = vauth.foundational_products.Environments.LOCAL

MICROSITE_PARTNER_PID_MAP = {}

try:
    from settings_local_override import *
except ImportError:
    pass
