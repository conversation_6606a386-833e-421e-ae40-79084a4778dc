// Utility functions for Google Analytics

function GATrackEvent(category, action, label) {
    // Support both Universal Analytics (ga) and Classic Analytics (_gaq) for backward compatibility
    if (typeof(ga) !== 'undefined' && ga) {
        ga('send', 'event', category, action, label);
    } else if (typeof(_gaq) !== 'undefined' && _gaq) {
        _gaq.push(['_trackEvent', category, action, label]);
    }
}

function GATrackPageView(path) {
    // Support both Universal Analytics (ga) and Classic Analytics (_gaq) for backward compatibility
    if (typeof(ga) !== 'undefined' && ga) {
        ga('send', 'pageview', path);
    } else if (typeof(_gaq) !== 'undefined' && _gaq) {
        _gaq.push(['_trackPageView', path]);
    }
}
