// Google Analytics Debug Utility
// This script helps debug Google Analytics implementation issues

(function() {
    'use strict';
    
    var AnalyticsDebugger = {
        
        init: function() {
            this.checkAnalyticsImplementation();
            this.addDebugConsoleCommands();
        },
        
        checkAnalyticsImplementation: function() {
            console.group('🔍 Google Analytics Debug Report');
            
            // Check for Universal Analytics
            if (typeof ga !== 'undefined') {
                console.log('✅ Universal Analytics (ga) is loaded');
                this.checkUniversalAnalytics();
            } else {
                console.warn('❌ Universal Analytics (ga) is NOT loaded');
            }
            
            // Check for Classic Analytics
            if (typeof _gaq !== 'undefined') {
                console.log('⚠️  Classic Analytics (_gaq) is loaded (deprecated)');
                this.checkClassicAnalytics();
            } else {
                console.log('ℹ️  Classic Analytics (_gaq) is not loaded');
            }
            
            // Check for Google Tag Manager
            if (typeof dataLayer !== 'undefined') {
                console.log('✅ Google Tag Manager dataLayer is available');
                this.checkGoogleTagManager();
            } else {
                console.warn('❌ Google Tag Manager dataLayer is NOT available');
            }
            
            // Check for analytics utilities
            if (typeof GATrackEvent !== 'undefined') {
                console.log('✅ GATrackEvent utility function is available');
            } else {
                console.warn('❌ GATrackEvent utility function is NOT available');
            }
            
            if (typeof GATrackPageView !== 'undefined') {
                console.log('✅ GATrackPageView utility function is available');
            } else {
                console.warn('❌ GATrackPageView utility function is NOT available');
            }
            
            console.groupEnd();
        },
        
        checkUniversalAnalytics: function() {
            try {
                ga(function(tracker) {
                    var trackingId = tracker.get('trackingId');
                    var cookieDomain = tracker.get('cookieDomain');
                    console.log('📊 Universal Analytics Tracking ID:', trackingId);
                    console.log('🍪 Cookie Domain:', cookieDomain);
                });
            } catch (e) {
                console.error('❌ Error checking Universal Analytics:', e);
            }
        },
        
        checkClassicAnalytics: function() {
            if (_gaq && _gaq.length > 0) {
                console.log('📊 Classic Analytics queue has', _gaq.length, 'items');
                console.log('📋 Classic Analytics queue:', _gaq);
            }
        },
        
        checkGoogleTagManager: function() {
            if (dataLayer && dataLayer.length > 0) {
                console.log('📊 GTM dataLayer has', dataLayer.length, 'items');
                console.log('📋 GTM dataLayer:', dataLayer);
            }
        },
        
        addDebugConsoleCommands: function() {
            // Add global debug functions
            window.GADebug = {
                testEvent: function(category, action, label) {
                    category = category || 'Debug';
                    action = action || 'Test Event';
                    label = label || 'Manual Test';
                    
                    console.log('🧪 Testing analytics event:', category, action, label);
                    
                    if (typeof GATrackEvent !== 'undefined') {
                        GATrackEvent(category, action, label);
                        console.log('✅ Event sent via GATrackEvent');
                    } else if (typeof ga !== 'undefined') {
                        ga('send', 'event', category, action, label);
                        console.log('✅ Event sent via Universal Analytics');
                    } else if (typeof _gaq !== 'undefined') {
                        _gaq.push(['_trackEvent', category, action, label]);
                        console.log('✅ Event sent via Classic Analytics');
                    } else {
                        console.error('❌ No analytics implementation found');
                    }
                },
                
                testPageView: function(path) {
                    path = path || '/debug-test';
                    
                    console.log('🧪 Testing analytics pageview:', path);
                    
                    if (typeof GATrackPageView !== 'undefined') {
                        GATrackPageView(path);
                        console.log('✅ Pageview sent via GATrackPageView');
                    } else if (typeof ga !== 'undefined') {
                        ga('send', 'pageview', path);
                        console.log('✅ Pageview sent via Universal Analytics');
                    } else if (typeof _gaq !== 'undefined') {
                        _gaq.push(['_trackPageView', path]);
                        console.log('✅ Pageview sent via Classic Analytics');
                    } else {
                        console.error('❌ No analytics implementation found');
                    }
                },
                
                showReport: function() {
                    AnalyticsDebugger.checkAnalyticsImplementation();
                }
            };
            
            console.log('🛠️  Analytics debug commands available:');
            console.log('   GADebug.testEvent(category, action, label) - Test event tracking');
            console.log('   GADebug.testPageView(path) - Test pageview tracking');
            console.log('   GADebug.showReport() - Show analytics debug report');
        }
    };
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            AnalyticsDebugger.init();
        });
    } else {
        AnalyticsDebugger.init();
    }
    
})();
