""" Module for retrieving and process google analytics for a specfic microsite. """
# To many lines in module
# pylint: disable=C0302

import logging
from datetime import date
from google.appengine.api.namespace_manager import namespace_manager
from google.appengine.ext.blobstore import BlobInfo
from app.models.analytics import AnalyticsReport as AnalyticsReportModel, FREQUENCY_MONTHLY

PUBLISH_DELAY = 60*60*8 # allow 8 hours to complete data gathering
NUM_REFERRERS = 10 # the number of referrers to track

#Google Analytics CustomVariable Keys
UAPROFILE_KEY = 'ga:customVarValue1'
MSID_KEY = 'ga:customVarValue2'
DATE_BREAKDOWN = 'ga:date'

COLUMN_HEADERS = 'columnHeaders'
NAME = 'name'
ROWS = 'rows'

# W0613:266:ComputeDateRange.execute: Unused argument 'obj'
# Fantasm interface
# pylint: disable=W0613


class BatchMicrositeTrafficAdapter(dict):
    """ This class takes a traffic data_feed, partitioned by only ga:customVarValue2 (msid), and populates its
        dictionary, i.e., the output of GoogleAnalytics.get_traffic_for_report().

    adapter = BatchMicrositeTrafficAdapter(traffic_data)
    for msid, metrics_dict in adapter.iteritems():
        visitors = metrics_dict['visitors']
        new_visitors = metrics_dict['new_visits']
        visits = metrics_dict['visits']
        page_views = metrics_dict['page_views']
    """

    KEY_MAPPINGS = {
        'ga:visitors': 'visitors',
        'ga:newvisits': 'new_visits',
        'ga:visits': 'visits',
        'ga:pageviews': 'page_views'
    }

    def __init__(self, traffic_data):
        """ Initialize, populating dictionary.

        Example data:{
            ...
            u'rows': [
                        [u'MS-123435',u'4',u'5',u'7',u'8'],
                        ...
                    ],
            u'columnHeaders': [{u'dataType': u'STRING', u'columnType': u'DIMENSION', u'name': u'ga:customVarValue2'},
                               {u'dataType': u'INTEGER', u'columnType': u'METRIC', u'name': u'ga:visits'},
                               {u'dataType': u'INTEGER', u'columnType': u'METRIC', u'name': u'ga:visitors'},
                               {u'dataType': u'INTEGER', u'columnType': u'METRIC', u'name': u'ga:pageviews'},
                               {u'dataType': u'INTEGER', u'columnType': u'METRIC', u'name': u'ga:newvisits'}]
            ...
        }
        """
        super().__init__()

        columns = traffic_data.get(COLUMN_HEADERS, [])
        if len(columns) < 5 or columns[0].get(NAME) != MSID_KEY or columns[1].get(NAME) != 'ga:visits' or \
                columns[2].get(NAME) != 'ga:visitors' or columns[3].get(NAME) != 'ga:pageviews' or \
                columns[4].get(NAME) != 'ga:newvisits':
            raise ValueError('To use this class, the data must be dimensioned by only ga:customVarValue2. It should'
                             'also have ga:visits, ga:visitors, ga:pageviews, and ga:newvisits as metrics.')

        for row in traffic_data.get(ROWS, []):
            msid = row[0]
            ms_dict = {}
            for i, item in enumerate(row):
                if i == 0:
                    continue
                ms_dict[self.KEY_MAPPINGS[columns[i].get(NAME)]] = int(item)
            self[msid] = ms_dict


class BatchMicrositeEventsAdapter(dict):
    """ This class takes an event data, partitioned by only ga:customVarValue2 (msid) and ga:eventAction,
        and populates its dictionary, i.e., the output of GoogleAnalyticsV3.get_event_data(use_msid_dimension=True).

    adapter = BatchMicrositeEventsAdapter(events_data)
    for msid, metrics_dict in adapter.iteritems():
        image_clicks = metrics_dict['image_clicks']
        map_clicks = metrics_dict['map_clicks']
        video_clicks = metrics_dict['video_clicks']
        phone_clicks = metrics_dict['phone_clicks']
        contact_clicks = metrics_dict['contact_clicks']
    """

    KEY_MAPPINGS = {
        'Image': 'image_clicks',
        'Map': 'map_clicks',
        'Video': 'video_clicks',
        'Phone': 'phone_clicks',
        'Contact': 'contact_clicks',
        'Link': 'website_clicks',
        'QR': 'qr_clicks'
    }

    def __init__(self, event_data):
        """ Initialize, populating dictionary.

        Example data:
        {
            ...
            u'rows': [
                        [u'MS-123345', u'Image', u'18'],
                        [u'MS-123345', u'Map', u'3']
                        ...
                    ],
            u'columnHeaders': [{u'dataType': u'STRING', u'columnType': u'DIMENSION', u'name': u'ga:customVarValue2'},
                               {u'dataType': u'STRING', u'columnType': u'DIMENSION', u'name': u'ga:eventAction'},
                               {u'dataType': u'INTEGER', u'columnType': u'METRIC', u'name': u'ga:totalEvents'}]
            ...
        }
        """
        super().__init__()

        columns = event_data.get(COLUMN_HEADERS, [])
        if len(columns) < 3 or columns[0].get(NAME) != MSID_KEY or columns[1].get(NAME) != 'ga:eventAction' or \
                columns[2].get(NAME) != 'ga:totalEvents':
            raise ValueError('To use this class, the data must be dimensioned by ga:customVarValue2 and '
                             'ga:eventAction. It should also have ga:totalEvents as metric.')

        for row in event_data.get(ROWS, []):
            msid = row[0]
            action = row[1]

            if action in self.KEY_MAPPINGS:
                if not msid in self:
                    self[msid] = {}
                self[msid][self.KEY_MAPPINGS[action]] = int(row[2])
            else:
                logging.warning('Unknown event action "%s" in events data feed.', action)


class BatchMicrositeSourceAdapter(dict):
    """ This class takes a sources data_feed, paritioned by only ga:customVarValue2 (msid) and ga:source,
        and populates its dictionary, i.e., the output of GoogleAnalytics.get_sources_for_report().

        adapter = BatchMicrositeSourceAdapter(source_data)
        for msid, source_list in adapter.iteritems():
            for source, visitors in source_list:
                source = source
                visitors = visitors
    """

    def __init__(self, source_data):
        """ Initialize, populating dictionary.

        Example entry:
        {
            ...
            u'rows': [
                        [u'MS-123123', u'google', u'141'],
                        [u'MS-123123', u'(direct)', u'5']
                        ...
                    ],
            u'columnHeaders': [{u'dataType': u'STRING', u'columnType': u'DIMENSION', u'name': u'ga:customVarValue2'},
                               {u'dataType': u'STRING', u'columnType': u'DIMENSION', u'name': u'ga:source'},
                               {u'dataType': u'INTEGER', u'columnType': u'METRIC', u'name': u'ga:visitors'}]
            ...
        }
        """
        super().__init__()
        columns = source_data.get(COLUMN_HEADERS, [])
        if len(columns) < 3 or columns[0].get(NAME) != MSID_KEY or columns[1].get(NAME) != 'ga:source' or \
                columns[2].get(NAME) != 'ga:visitors':
            raise ValueError('To use this class, the data must be dimensioned by ga:customVarValue2 and '
                             'ga:source. It should also have ga:visitors as metric.')

        for row in source_data.get(ROWS, []):
            msid = row[0]
            source = row[1]

            if not msid in self:
                self[msid] = []

            self[msid].append((source, int(row[2])))


class BatchMicrositeMobileViewsAdapter(dict):
    """ This class takes a mobile traffic data_feed, partitioned by only ga:customVarValue2 (msid),
        and populates its dictionary, i.e., the output of GoogleAnalytics.get_mobile_traffic_for_report().

    adapter = BatchMicrositeTrafficAdapter(traffic_data)
    """

    KEY_MAPPINGS = {
        'ga:pageviews': 'mobile_views'
    }

    def __init__(self, traffic_data):
        """
        Initialize, populating dictionary.
        Example data:
        {
            ...
            u'rows': [
                        [u'MS-123123', u'292'],
                        [u'MS-123234', u'54']
                        ...
                    ],
            u'columnHeaders': [{u'dataType': u'STRING', u'columnType': u'DIMENSION', u'name': u'ga:customVarValue2'},
                               {u'dataType': u'INTEGER', u'columnType': u'METRIC', u'name': u'ga:pageviews'}]
            ...
        }
        """
        super().__init__()

        columns = traffic_data.get(COLUMN_HEADERS, [])
        if len(columns) < 2 or columns[0].get(NAME) != MSID_KEY or columns[1].get(NAME) != 'ga:pageviews':
            raise ValueError('To use this class, the data must be dimensioned by ga:customVarValue2 and metric by '
                             'ga:pageviews.')

        for row in traffic_data.get(ROWS, []):
            msid = row[0]
            if not msid in self:
                self[msid] = {}
            self[msid][self.KEY_MAPPINGS[columns[1].get(NAME)]] = int(row[1])


class BatchMicrositeImageDetailAdapter(dict):
    """ This class takes a image clicks data_feed, partitioned by only ga:customVarValue2 (msid) and
         ga:pageTitle and populates its dictionary with the total number of event for yellow ad and phot clicks
         combining mobile and desktop into one. Use i.e., the output of GoogleAnalytics.get_imgdetails_for_report().

    The Data that comes back looks like this

    ga:customVarValue2,ga:pageTitle,ga:totalEvents

    Page Title needs to be filtered by "Yellow Ads" and "Photos"

    adapter = BatchMicrositeDWImageDetailAdapter(traffic_data)

    """

    KEY_MAPPINGS = {
        'ga:totalEvents': 'events'
    }

    #All prefixes have to be unique
    PREFIX_MAPPINGS = {
        "Yellow Ads": 'yellow_clicks',
        "Photos": 'photo_clicks'
    }

    def __init__(self, traffic_data):
        """
        Initialize, populating dictionary.
        Example data:
        {
            ...
            u'rows': [
                        [u'MS-123123', u'Yellow Ads | Triple 8 Pizza Regina Restaurants', u'18'],
                        [u'MS-123123', u'Yellow Ads | Preston Crossing Saskatoon Shopping Centres', u'1']
                        ...
                    ],
            u'columnHeaders': [{u'dataType': u'STRING', u'columnType': u'DIMENSION', u'name': u'ga:customVarValue2'},
                               {u'dataType': u'STRING', u'columnType': u'DIMENSION', u'name': u'ga:pageTitle'},
                               {u'dataType': u'INTEGER', u'columnType': u'METRIC', u'name': u'ga:totalEvents'}]
            ...
        }

        """
        super().__init__()

        columns = traffic_data.get(COLUMN_HEADERS, [])
        if len(columns) < 3 or columns[0].get(NAME) != MSID_KEY or columns[1].get(NAME) != 'ga:pageTitle' or \
                columns[2].get(NAME) != 'ga:totalEvents':
            raise ValueError('To use this class, the data must be dimensioned by ga:customVarValue2 and '
                             'ga:pageTitle, and metric by ga:totalEvents.')

        for row in traffic_data.get(ROWS, []):
            msid = row[0]
            page_title = row[1]

            if not msid in self:
                self[msid] = {}

            prefix = next((prefix for prefix in list(self.PREFIX_MAPPINGS.keys()) if page_title.startswith(prefix)), None)
            if prefix:
                mapping = self.PREFIX_MAPPINGS[prefix]
                if mapping in self[msid]:
                    self[msid][mapping] += int(row[2])
                else:
                    self[msid][mapping] = int(row[2])


class BatchMicrositePageViewsAdapter(dict):
    """ This class takes pageviews data_feed, partitioned by
     only ga:customVarValue2 (msid), ga:customVarValue1 (profile), ga:pagePath and ga:pageTitle
     and populates its dictionary with the total number of page views for each page
     combining mobile and desktop into one. Use i.e., the output of GoogleAnalytics.get_page_views_for_report().

    The Data that comes back looks like this

    ga:customVarValue2,ga:customVarValue1,ga:pagePath,ga:pageTitle

    Page Title needs to be filtered by "Yellow Ads" and "Photos"

    adapter = BatchMicrositeDWPageViewsAdapter(traffic_data)

    """

    KEY_MAPPINGS = {
        'ga:pageviews': 'views'
    }


    ABOUT_US_SLUG = 'about-us'
    DISPLAY_ADS_SLUG = 'yellow-ads'
    INFORMATIONALS_SLUG = 'text-ads'
    CONTACT_US_SLUG = 'contact-us'
    VIDEOS_SLUG = 'videos'
    IMAGES_SLUG = 'photos'
    COUPONS_SLUG = 'coupons'
    HOURS_SLUG = '_mvh/hours'
    ABOUT_US_MOBILE = '_mvh/index'

    SLUGS = (ABOUT_US_SLUG, DISPLAY_ADS_SLUG, INFORMATIONALS_SLUG, CONTACT_US_SLUG, VIDEOS_SLUG, \
             IMAGES_SLUG, COUPONS_SLUG, HOURS_SLUG)

    DESKTOP = 'desktop'
    PHONE = 'phone'

    #R0912:149:BatchMicrositeDWPageViewsAdapter.__init__: Too many branches (25/20)
    # pylint: disable=R0912
    def __init__(self, traffic_data):
        """
        Initialize, populating dictionary.
        Example data:
        {
            ...
            u'rows': [
                        [u'MS-123123', u'desktop', u'/triple-eight-pizza/', u'Triple 8 Pizza Regina Pizza', u'71'],
                        [u'MS-123345', u'desktop', u'/triple-eight-pizza/contact-us/', u'Contact Us', u'2']
                        ...
                    ],
            u'columnHeaders': [{u'dataType': u'STRING', u'columnType': u'DIMENSION', u'name': u'ga:customVarValue2'},
                               {u'dataType': u'STRING', u'columnType': u'DIMENSION', u'name': u'ga:customVarValue1'},
                               {u'dataType': u'STRING', u'columnType': u'DIMENSION', u'name': u'ga:pagePath'},
                               {u'dataType': u'STRING', u'columnType': u'DIMENSION', u'name': u'ga:pageTitle'},
                               {u'dataType': u'INTEGER', u'columnType': u'METRIC', u'name': u'ga:pageviews'}]
            ...
        }
        """
        super().__init__()

        columns = traffic_data.get(COLUMN_HEADERS, [])
        if len(columns) < 5 or columns[0].get(NAME) != MSID_KEY or columns[1].get(NAME) != UAPROFILE_KEY or \
                columns[2].get(NAME) != 'ga:pagePath' or columns[3].get(NAME) != 'ga:pageTitle' or \
                columns[4].get(NAME) != 'ga:pageviews':
            raise ValueError('To use this class, the data must be dimensioned by ga:customVarValue1,ga:customVarValue2,'
                             'ga:pagePath, and ga:pageTitle. And must be metric by ga:pageviews.')

        ua_profilecount = 0
        for row in traffic_data.get(ROWS, []):
            msid = row[0]
            profile = row[1]
            page_path = row[2]
            paths = page_path.strip('/').split('/', 1)
            page_title = row[3]
            metric_value = int(row[4])

            if len(paths) == 2:
                #simple case where we have the full page slug
                path_of_page = paths[1]
                if path_of_page == self.ABOUT_US_MOBILE:
                    #catching the Mobile About Us page
                    path_of_page = self.ABOUT_US_SLUG
                if path_of_page in self.SLUGS:
                    #good path where the slug is one of the known ones
                    self.updateValue(msid, path_of_page, metric_value)
                elif profile == self.DESKTOP and path_of_page == '?ua_profile=desktop':
                    #this special case is needed since we have some legacy Analytics collected that
                    #contained this chunk as part of the page path. Should be possible to remove
                    #in November 2011
                    self.updateValue(msid, self.ABOUT_US_SLUG, metric_value)
                    ua_profilecount += 1
                else:
                    logging.info("Ignored 2 length Entry: %s, %s, %s", profile, page_path, page_title)

            elif len(paths) == 1:
                #there was no url
                if profile == self.DESKTOP:
                    #Must be about Us Page
                    self.updateValue(msid, self.ABOUT_US_SLUG, metric_value)
                elif profile == self.PHONE:
                    #this whole branch in the logic can  hopefully be removed in November 2011
                    #It is needed as a workaround since Phone Analytics weren't collected properly until
                    #Mid October 2011.
                    #once the Analytics are collecte correctly we can remove all this special code
                    if page_title.startswith('About Us'):
                        self.updateValue(msid, self.ABOUT_US_SLUG, metric_value)
                    elif page_title.startswith('Yellow Ads'):
                        self.updateValue(msid, self.DISPLAY_ADS_SLUG, metric_value)
                    elif page_title.startswith('Text Ads'):
                        self.updateValue(msid, self.INFORMATIONALS_SLUG, metric_value)
                    elif page_title.startswith('Contact Us'):
                        self.updateValue(msid, self.CONTACT_US_SLUG, metric_value)
                    elif page_title.startswith('Video'):
                        self.updateValue(msid, self.VIDEOS_SLUG, metric_value)
                    elif page_title.startswith('Coupons'):
                        self.updateValue(msid, self.COUPONS_SLUG, metric_value)
                    elif page_title.startswith('Photos'):
                        self.updateValue(msid, self.IMAGES_SLUG, metric_value)
                    elif page_title.startswith('Hours of Operation'):
                        self.updateValue(msid, self.HOURS_SLUG, metric_value)
                    else:
                        logging.info("Ignored 1 length Entry: %s, %s, %s", profile, page_path, page_title)

                else:
                    raise ValueError("Unknown Profile:" + str(profile) + " should be either 'phone' or 'desktop'")
            else:
                raise ValueError("There should be either 1 or two path entries. Currently " + str(len(paths))\
                                 + " original path:" + page_path)

        if ua_profilecount > 0:
            logging.warn("The Analytics had" + str(ua_profilecount) + " entries with ua_profile==")

    def updateValue(self, msid, mapping, value):
        """ small helper method for cleaner code """
        if not msid in self:
            self[msid] = {}
        if mapping in self[msid]:
            self[msid][mapping] += value
        else:
            self[msid][mapping] = value


def create_analytics_report_entity(**kwargs):
    """ Creates an AnalyticsReport entity. """
    pid = kwargs.get('pid')
    start_date = kwargs.get('start_date')
    end_date = kwargs.get('end_date')
    blobkey = kwargs.get('blobkey')
    frequency = kwargs.get('frequency', FREQUENCY_MONTHLY)

    pid = pid or namespace_manager.get_namespace()
    if not pid:
        raise ValueError('pid is required')
    pid = pid.upper()

    kwargs['pid'] = pid
    kwargs['frequency'] = frequency

    if not start_date:
        raise ValueError('start_date is required.')
    if not isinstance(start_date, date):
        raise ValueError('start_date must be datetime.date.')
    if not end_date:
        raise ValueError('end_date is required.')
    if not isinstance(end_date, date):
        raise ValueError('end_date must be datetime.date.')
    if not blobkey:
        raise ValueError('blobkey is required.')
    if not frequency:
        raise ValueError('frequency is required')

    key = AnalyticsReportModel.build_key(start_date, frequency, pid=pid)
    report = AnalyticsReportModel(key=key, **kwargs)
    report.put()
    return AnalyticsReport.from_model(report)


class AnalyticsReport:
    """ A prepared Google Analytics report. """

    def __init__(self, **kwargs):
        """ Initialize """
        self.pid = kwargs.get('pid')
        self.start_date = kwargs.get('start_date')
        self.end_date = kwargs.get('end_date')
        self.frequency = kwargs.get('frequency')
        self.blobinfo = None
        blobkey = kwargs.get('blobkey')
        if blobkey:
            self.blobinfo = BlobInfo(blobkey)

    @property
    def filename(self):
        """ Generates a friendly filename for the report. """
        if self.frequency != FREQUENCY_MONTHLY:
            raise ValueError('Unknown frequency "%s".' % self.frequency)
        return 'SiteAnalyticsReport-%s-%d-%02d.csv' % (self.frequency,
                                                            self.start_date.year, self.start_date.month)

    @classmethod
    def from_model(cls, model):
        """ Builds a domain object from model. """
        if not model:
            return None
        return cls(**model.to_dict())

    @classmethod
    def lookup_most_recent(cls, pid=None, count=50):
        """ Looks up the most recent reports. """
        qr = AnalyticsReportModel.lookup_all(pid=pid, count=count)
        return [cls.from_model(result) for result in qr.results]
