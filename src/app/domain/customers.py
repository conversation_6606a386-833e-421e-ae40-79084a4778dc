""" Customers domain logic """
import logging

from google.appengine.ext.deferred import PermanentTaskFailure

from customer_voice_service_sdk import CustomerVoiceAPI
from customer_voice_service_sdk._internal.customers import ReviewRequestStatus
from customer_voice_service_sdk.errors import UpdateCustomerException, CustomerDoesntExistException
from sms_sdk import SmsAPI


def update_customer_sms_status_to_clicked_and_source(account_group_id, customer_id, source_id=None):
    """ Update the sms status of a Customer Voice customer to clicked, as well as source if provided """
    try:
        CustomerVoiceAPI.update(business_id=account_group_id,
                                customer_id=customer_id,
                                sms_review_request_status=ReviewRequestStatus.REVIEW_REQUEST_STATUS_CLICKED.value,
                                sms_last_clicked_source=source_id)
    except UpdateCustomerException:
        raise UpdateCustomerException('Unable to update customer: %s', customer_id)
    except CustomerDoesntExistException:
        logging.warning("Customer %s does not exists for business: %s", customer_id, account_group_id)
        raise PermanentTaskFailure


def create_customer_sms_clicked_event(message_uuid=None):
    """ Create a customer voice sms clicked event """
    try:
        SmsAPI.create_sms_clicked_event(message_uuid=message_uuid)
    except:
        logging.warning("Unable to create sms clicked event with message_uuid: %s", message_uuid)
        raise PermanentTaskFailure
