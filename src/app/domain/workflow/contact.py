""" Workflows for the Contact page. """

from google.appengine.api.namespace_manager import namespace_manager
from google.appengine.api import taskqueue

import statsd
from wtforms import validators
from wtforms import fields
from app.domain.workflow import exceptions, CleaningForm

def email_contact(form):
    """ Queues task to email a microsite contact. """

    if not form:
        raise ValueError('form is required.')
    if not isinstance(form, ContactForm):
        raise ValueError('form must be a ContactForm.')
    if not form.validate():
        raise exceptions.ValidationException(form.errors)

    params = {
        'pid': namespace_manager.get_namespace(),
        'to' : form.to_email.data,
        'message' : form.message.data,
        'reply_to' : form.email.data,
        'subject' : form.subject.data
    }
    if form.source_url.data:
        params['source_url'] = form.source_url.data

    metric = statsd.StatsDMetric('email_contact', tags="smb_contact_form")
    statsd.tick_metric(metric)

    taskqueue.add(url='/_queue/email/smb-contact/', params=params)

REQUIRED = validators.data_required()
OPTIONAL = validators.optional()
EMAIL = validators.Email()

class ContactForm(CleaningForm):
    """ Contact Form """
    pageid = fields.HiddenField(validators=[REQUIRED])
    to_email = fields.HiddenField(validators=[REQUIRED, EMAIL])
    email = fields.TextField('Your Email', [REQUIRED, EMAIL])
    subject = fields.TextField('Subject', [REQUIRED])
    source_url = fields.HiddenField('Source Url', [OPTIONAL])
    message = fields.TextAreaField('Message', [REQUIRED])

    navslug = fields.HiddenField('Nav Slug', [OPTIONAL])
    virtslug = fields.HiddenField('Virt Slug', [OPTIONAL])
