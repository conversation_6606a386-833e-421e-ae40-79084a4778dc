""" Workflows for microsites. """
import logging

from google.appengine.api.namespace_manager import namespace_manager
from google.appengine.ext import ndb

from app.layouts import LAYOUTS
from wtforms import fields, validators
from vautil import phonenumber

from app.constants import VALID_HOSTNAME_REGEX_STRING, VALID_SLUG_REGEX_STRING, SLUG_MIN_LENGTH, \
    LAYOUT_API_KEY, NAME, API_KEY_TO_LAYOUT, PHONE_THEME, DESKTOP_THEME, DEFAULT_LAYOUT, \
    MICROSITE_KEYS as MS_KEYS, VALID_HEX_COLOR_REGEX_STRING, DEFAULT_COMPANY_NAME
from app.country_state import get_country_choices, validate_country_code
from app.domain.microsite import create_microsite_entity, update_microsite_entity
from app.domain.workflow import exceptions, CleaningForm, TextListField, MultiTextField
from settings import get_account_origin


def __pull_common_microsite_fields(form):
    """ Peels the common microsite fields out of an AbstractMicrositeForm. """
    ms_kwargs = {
        MS_KEYS.SPID: form.spid.data,
        MS_KEYS.AGID: form.agid.data,
        MS_KEYS.SSO_TOKEN: form.sso_token.data,
        MS_KEYS.NAME: form.name.data,
        MS_KEYS.BLURB: form.blurb.data,
        MS_KEYS.PHONE_WORK: form.phone_work.data,
        MS_KEYS.PHONE_CALL_TRACKING: form.phone_call_tracking.data,

        MS_KEYS.EMAIL: form.email.data,
        MS_KEYS.WEBSITE: form.website.data,
        MS_KEYS.USE_WEBSITE_FOR_FULL_SITE_LINK: form.use_website_for_full_site_link.data,

        MS_KEYS.ADDRESS1: form.address1.data,
        MS_KEYS.ADDRESS2: form.address2.data,
        MS_KEYS.CITY: form.city.data,
        MS_KEYS.STATE: form.state.data,
        MS_KEYS.COUNTRY: form.country.data,
        MS_KEYS.ZIP_CODE: form.zipcode.data,
        MS_KEYS.PLACE: form.place.data,

        MS_KEYS.FACEBOOK_URL: form.facebook_url.data,
        MS_KEYS.TWITTER_URL: form.twitter_url.data,
        MS_KEYS.RSS_URL: form.rss_url.data,
        MS_KEYS.YOUTUBE_URL: form.youtube_url.data,
        MS_KEYS.LINKEDIN_URL: form.linkedin_url.data,
        MS_KEYS.INSTAGRAM_URL: form.instagram_url.data,
        MS_KEYS.PINTEREST_URL: form.pinterest_url.data,
        MS_KEYS.FOURSQUARE_URL: form.foursquare_url.data,
        MS_KEYS.GOOGLEPLUS_URL: form.googleplus_url.data,

        MS_KEYS.HOURS_OF_OPERATION: form.hours_of_operation_json.data,
        MS_KEYS.COLOR: form.color.data,
        MS_KEYS.CUSTOMER_IDENTIFIER: form.customer_identifier.data,
        MS_KEYS.ANNOUNCEMENT: form.announcement.data,
        MS_KEYS.CATEGORIES: form.categories.data,
        MS_KEYS.PMSID: form.pmsid.data,
        MS_KEYS.MARKET_ID: form.market_id.data,
        MS_KEYS.TAX_ID: form.tax_id.data,

        MS_KEYS.PHONE_THEME: LAYOUTS[API_KEY_TO_LAYOUT[form.layout.data]][PHONE_THEME],
        MS_KEYS.DESKTOP_THEME: LAYOUTS[API_KEY_TO_LAYOUT[form.layout.data]][DESKTOP_THEME],
    }

    lat = form.latitude.data
    lon = form.longitude.data
    if lat is not None and lon is not None:
        ms_kwargs[MS_KEYS.GEO] = ndb.GeoPt(lat, lon)
    else:
        ms_kwargs[MS_KEYS.GEO] = None

    return ms_kwargs

def update_microsite(form, pid=None):
    """ Updates a microsite from an UpdateMicrositeForm form. """

    if not form:
        raise ValueError('form is required.')
    if not isinstance(form, UpdateMicrositeForm):
        raise ValueError('form must be an UpdateMicrositeForm.')
    if not form.validate():
        raise exceptions.ValidationException(form.errors)

    pid = pid or namespace_manager.get_namespace()

    ms_kwargs = __pull_common_microsite_fields(form)
    ms_kwargs['pid'] = pid
    ms_kwargs['msid'] = form.msid.data

    # Don't update any field to None when it isn't passed in!
    if form.market_id.data is None:
        del ms_kwargs['market_id']
    if form.agid.data is None:
        del ms_kwargs['agid']
    if form.spid.data is None:
        del ms_kwargs['spid']

    update_microsite_entity(**ms_kwargs)


def update_microsite_full_website_settings(form, pid=None):
    """ Only updates the full website url and checkbox """

    if not form:
        raise ValueError('form is required.')
    if not isinstance(form, UpdateMicrositeForm):
        raise ValueError('form must be an UpdateMicrositeForm.')
    if not form.validate():
        raise exceptions.ValidationException(form.errors)

    pid = pid or namespace_manager.get_namespace()
    ms_kwargs = {
        'pid': pid,
        'msid': form.msid.data,
        'use_website_for_full_site_link': form.use_website_for_full_site_link.data,
        'website': form.website.data,
    }

    update_microsite_entity(**ms_kwargs)


def create_blank_microsite(form, pid=None, msid=None, market_id=None, api_user=None):
    """ Creates a blank Microsite with no page from a CreateMicrositeForm form.

    @returns the created microsite
    NOTE: This is different than create_microsite which just returns the msid
    """

    if not form:
        raise ValueError('form is required.')
    if not isinstance(form, CreateMicrositeForm):
        raise ValueError('form must be a CreateMicrositeForm.')
    if not form.validate():
        raise exceptions.ValidationException(form.errors)

    pid = pid or namespace_manager.get_namespace()
    hostname = form.hostname.data.lower()
    slug = form.slug.data

    account_origin = get_account_origin(api_user)
    logging.debug("Creating site for api user '%s', origin '%s'", api_user, account_origin)

    ms_kwargs = __pull_common_microsite_fields(form)
    ms_kwargs['pid'] = pid
    ms_kwargs['msid'] = msid
    ms_kwargs['pmsid'] = form.pmsid.data
    ms_kwargs['market_id'] = market_id
    ms_kwargs['account_origin'] = account_origin
    ms_kwargs['hostname'] = hostname
    ms_kwargs['slug'] = slug

    microsite = create_microsite_entity(**ms_kwargs)
    logging.info("Created Microsite Entity. MSID: '%s'", microsite.msid)

    return microsite



# C0321: More than one statement on a single line
# more readable in this case
# pylint: disable=C0321
def generate_home_title(**kwargs):
    """ Generates a title for the Home page. """
    name = kwargs.get('name')
    city = kwargs.get('city')
    state = kwargs.get('state')

    result = ''
    if name:
        result += name
    if city:
        result += ', ' + city if result else city
    if state:
        result += ', ' + state if result else state
    return result or None


def generate_home_h1(**kwargs):
    """ Generates an h1 for the Home page."""
    return generate_home_title(**kwargs)  # may change later


def generate_home_meta_description(**kwargs):
    """ Generates a meta description for the Home page. """
    address1 = kwargs.get('address1')
    address2 = kwargs.get('address2')
    city = kwargs.get('city')
    state = kwargs.get('state')
    zipcode = kwargs.get('zipcode')
    country = kwargs.get('country')
    name = kwargs.get('name')
    phone = kwargs.get('phone')

    result = ''
    if name:
        result += name
    if address1:
        if result:
            result += ', '
        result += address1
    if address2:
        if result:
            result += ', '
        result += address2
    if city:
        if result:
            result += ', '
        result += city
    if state:
        if result:
            result += ', '
        result += state
    if zipcode:
        if result:
            result += state and ' ' or ', '
        result += zipcode
    if country:
        if result:
            result += ', '
        result += country
    if phone:
        if result:
            result += ', '
        result += phone
    return result or None


def validate_phone_numbers(form, field):
    """ Verifies that the phone number is valid. """
    phone = field.data and field.data.strip() or None
    country_code = form.country.data and form.country.data.strip() or None
    valid_country = validate_country_code(country_code)
    if not phone or not valid_country:
        return
    try:
        phone = phonenumber.clean_phone_number(phone, country_code)
    except phonenumber.InvalidPhoneNumberException:
        raise validators.ValidationError('Invalid phone number for the selected country.')
    else:
        form.phone.data = phone


def validate_company_name(form, field):
    """ Verify company name and if it empty use "no-available-company-name" """
    company_name = field.data and field.data.strip() or None
    if not company_name:
        form.name.data = DEFAULT_COMPANY_NAME

REQUIRED = validators.Required()
LENGTH = validators.Length
URL = validators.URL()
OPTIONAL = validators.Optional()
EMAIL = validators.Email()
NUM_RANGE = validators.NumberRange
REGEXP = validators.Regexp
VALID_PHONE_NUMBERS = validate_phone_numbers
VALID_COMPANY_NAME = validate_company_name


class AbstractMicrositeForm(CleaningForm):
    """ Fields common to all microsite fields. This form should not be instantiated. """

    spid = fields.TextField(
        'Social Profile Id',
        [OPTIONAL, LENGTH(max=100)],
        description='Social Profile ID used to link a Site with a Social Profile.')

    agid = fields.TextField(
        'Account Group Id',
        [OPTIONAL, LENGTH(max=100)],
        description='Account Group ID used to group accounts across products.')

    sso_token = fields.TextField(
        'SSO Token',
        [OPTIONAL, LENGTH(max=200)],
        description='SSO token used to authorize access to this Site.')

    name = fields.TextField(
        'Company Name',
        [OPTIONAL, LENGTH(max=100), VALID_COMPANY_NAME],
        description='Used for creating the URL and site name and to support SEO. e.g. "Ace Plumbing Boston"')

    blurb = fields.TextField(
        'Tagline',
        [OPTIONAL, LENGTH(max=300)],
        description='A short phrase that appears with your logo, use a tagline or motto.')

    phone_work = MultiTextField(
        fields.TextField(
            'Work Phone Number',
            [OPTIONAL, LENGTH(max=30), VALID_PHONE_NUMBERS],
            description='e.g., "****************"')
    )

    phone_call_tracking = MultiTextField(
        fields.TextField(
            'Call Tracking Phone Number',
            [OPTIONAL, LENGTH(max=30), VALID_PHONE_NUMBERS],
            description='e.g., "****************"')
    )

    email = fields.TextField(
        'Contact Email',
        [OPTIONAL, EMAIL, LENGTH(max=100)],
        description='An email address used to contact the owner of the site.')

    website = fields.TextField(
        'Website',
        [OPTIONAL, URL, LENGTH(max=250)],
        description='Optional. Enter the address of the main non-mobile web site, \
                     or an alternate web site. e.g "http://www.ace.com"')

    use_website_for_full_site_link = fields.BooleanField(
        'Use Website for Full Site link',
        description='When this is selected it will use the url in the website field \
                    for the Full Site link on the mobile site.',
        default=True
    )

    address1 = fields.TextField(
        'Address',
        [OPTIONAL, LENGTH(max=100)])

    address2 = fields.TextField(
        'Address 2',
        [OPTIONAL, LENGTH(max=100)])

    city = fields.TextField(
        'City',
        [OPTIONAL, LENGTH(max=50)])

    state = fields.TextField(
        'State / Province',
        [OPTIONAL, LENGTH(max=50)])

    country = fields.TextField(
        'Country',
        [OPTIONAL, LENGTH(max=50)])

    zipcode = fields.TextField(
        'Zip / Postal Code',
        [OPTIONAL, LENGTH(max=50)])

    place = fields.TextField(
        'Place',
        [OPTIONAL, LENGTH(max=50)],
        description='A friendly place name, e.g., "Twin Cities", or just a city name.')

    latitude = fields.DecimalField(
        'Latitude',
        [OPTIONAL, NUM_RANGE(min=-90, max=90)],
        description='Optionally enter these values if the address is unknown, not specific, \
                     or to place the pin at a specific point. From -90.0 to 90.0',
        places=5)

    longitude = fields.DecimalField(
        'Longitude',
        [OPTIONAL, NUM_RANGE(min=-180, max=180)],
        description='Optionally enter these values if the address is unknown, not specific, \
                     or to place the pin at a specific point. From -180.0 to 180.0',
        places=5)

    facebook_url = fields.TextField(
        'Facebook Profile',
        [OPTIONAL, URL, LENGTH(max=1000)],
        description='e.g., "http://www.facebook.com/abc"')

    twitter_url = fields.TextField(
        'Twitter Profile',
        [OPTIONAL, URL, LENGTH(max=1000)],
        description='e.g., "http://twitter.com/#!/abc"')

    rss_url = fields.TextField(
        'RSS / Blog',
        [OPTIONAL, URL, LENGTH(max=1000)],
        description='e.g., "http://blog.abc.com/feed/"')

    youtube_url = fields.TextField(
        'YouTube Channel',
        [OPTIONAL, URL, LENGTH(max=1000)],
        description='e.g., "http://www.youtube.com/user/abc"')

    linkedin_url = fields.TextField(
        'LinkedIn Profile',
        [OPTIONAL, URL, LENGTH(max=1000)],
        description='e.g., "http://www.linkedin.com/pub/abc"')

    instagram_url = fields.TextField(
        'Instagram Profile',
        [OPTIONAL, URL, LENGTH(max=1000)],
        description='e.g., "http://instagram.com/abc"')

    pinterest_url = fields.TextField(
        'Pinterest Profile',
        [OPTIONAL, URL, LENGTH(max=1000)],
        description='e.g., "http://www.pinterest.com/abc"')

    foursquare_url = fields.TextField(
        'Foursquare Venue',
        [OPTIONAL, URL, LENGTH(max=1000)],
        description='e.g., "https://foursquare.com/abc"')

    googleplus_url = fields.TextField(
        'Google+ Profile',
        [OPTIONAL, URL, LENGTH(max=1000)],
        description='e.g., "https://plus.google.com/abc"')

    hours_of_operation_json = fields.HiddenField(
        'Hours of Operation (as JSON)',
        [OPTIONAL])

    color = fields.TextField(
        'Color',
        [OPTIONAL, LENGTH(max=9), REGEXP(VALID_HEX_COLOR_REGEX_STRING, message="Missing hash or invalid color.")],
        description='Background color. Enter the hex value with the "#" symbol. e.g "#CC9900".')

    customer_identifier = fields.TextField(
        'Customer Identifier',
        [OPTIONAL, LENGTH(max=100)],
        description="Used as a reference only, enter your customer's ID, order number or other reference info.")

    announcement = fields.TextField(
        'Announcement',
        [OPTIONAL, LENGTH(max=300)],
        description='Enter a short announcement of up to 140 characters e.g. "Visit our open house this weekend."')

    categories = TextListField(
        'Categories',
        [OPTIONAL],
        description='A comma separated list of likely search terms for the business. \
                     Used in meta-content for SEO. e.g. "plumbing, sewer service, commercial plumbing"')

    layout = fields.SelectField(
        'Layout',
        [OPTIONAL, LENGTH(max=50)],
        description='The layout template to be used when rendering the site.',
        choices=[(LAYOUTS[layout][LAYOUT_API_KEY], LAYOUTS[layout][NAME]) for layout in list(LAYOUTS.keys())],
        default=DEFAULT_LAYOUT
    )

    market_id = fields.TextField(
        'Market Identifier')

    tax_id = MultiTextField(
        fields.TextField('Tax Id', [OPTIONAL])
    )


class UpdateMicrositeForm(AbstractMicrositeForm):
    """ Form to update an existing microsite. """

    msid = fields.HiddenField(
        'MSID',
        [REQUIRED])

    pmsid = fields.TextField(
        'Site Identifier',
        [OPTIONAL, LENGTH(max=100)],
        description='A unique identifier for this microsite.')


class UpdateMicrositeFormUI(UpdateMicrositeForm):
    """ Form to update an existing microsite from admin UI. """

    name = fields.TextField(
        'Company Name',
        [REQUIRED, LENGTH(max=100), VALID_COMPANY_NAME],
        description='Used for creating the URL and site name and to support SEO. e.g. "Ace Plumbing Boston"')

    country = fields.SelectField(
        'Country',
        [OPTIONAL],
        choices=[('', 'Select Country')] + get_country_choices())

    state = fields.SelectField(
        'State / Province',
        [OPTIONAL],
        choices=[], )

    latitude = fields.DecimalField(
        'Latitude',
        [REQUIRED, NUM_RANGE(min=-90, max=90)],
        description='Optionally enter these values if the address is unknown, not specific, \
                     or to place the pin at a specific point. From -90.0 to 90.0',
        places=5)

    longitude = fields.DecimalField(
        'Longitude',
        [REQUIRED, NUM_RANGE(min=-180, max=180)],
        description='Optionally enter these values if the address is unknown, not specific, \
                     or to place the pin at a specific point. From -180.0 to 180.0',
        places=5)

    # TODO: Remove this phone field in MS-1739 and use only phone_work for api and UI
    phone = fields.TextField(
        'Contact Phone',
        [OPTIONAL, LENGTH(max=30), VALID_PHONE_NUMBERS],
        description='e.g., "****************"')


class CreateMicrositeForm(AbstractMicrositeForm):
    """ Form to create a new microsite via batch import, plus related entities. """

    hostname = fields.SelectField(
        'Host',
        [REQUIRED,
         REGEXP(VALID_HOSTNAME_REGEX_STRING, message="Invalid host name")],
        description="The hostname that should be used.",
        choices=[])

    slug = fields.TextField(
        'Slug',
        [REQUIRED,
         REGEXP(VALID_SLUG_REGEX_STRING, message="Invalid slug.")],
        description="Minimum %d characters." % SLUG_MIN_LENGTH)

    home_page_content = fields.TextAreaField(
        'Home Page Content',
        [OPTIONAL])

    about_us_content = fields.TextAreaField(
        'About Us Content',
        [OPTIONAL])

    pmsid = fields.TextField(
        'Site Identifier',
        [OPTIONAL, LENGTH(max=100)],
        description='A unique identifier for this microsite.')


class VerifyRedirectScriptInstallationForm(CleaningForm):
    """ Take's a url and checks that the scripts were added to the webpage """

    url_to_be_verified = fields.TextField(
        'URL to verify',
        [OPTIONAL, URL, LENGTH(max=100)],
        description='e.g., "http://www.non-mobile-site.com"')
