""" Exceptions for the app.domain.workflow modules. """

class WorkflowException(Exception):
    """ A workflow exception. """
    pass
    
class ValidationException(Exception):
    """ A form validation exception. """
    
    def __init__(self, validation_errors=None):
        """ Initialize.
        
        @validation_errors a dict of a list of validation errors as returned by form.errors
        """
        validation_errors = validation_errors or {}
        message = 'The following validation errors were found\n'
        for key, value in validation_errors.items():
            for error in value:
                message += '  {}: {}\n'.format(key, error)
        super().__init__(message)
