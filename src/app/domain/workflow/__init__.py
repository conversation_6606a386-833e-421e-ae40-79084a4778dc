""" This package holds functions for the view's POST actions to call to get work done.

Each view's POST should only call a single workflow function to get its work done.

Forms are stored in this package and are the method of communication between the view and this packge.
"""

import logging
import json
from datetime import datetime
from wtforms import Form, validators
from wtforms.fields import Field, <PERSON>Field as TextField, TextAreaField, FieldList
from wtforms.widgets import TextInput, HiddenInput
from html.parser import HTMLParser

class CleaningForm(Form):
    """ A form that cleans it's values (strips, turns '' into None) on validate.

    This form also keeps track of when validate() was called and caches the result.
    Note: if someone/something changes a value of a field of this form, the cached validation still
    holds! Call clear_validation_result() to reset it so that validation works again!
    """

    def __init__(self, *args, **kwargs):
        """ Initialize. """
        super().__init__(*args, **kwargs)
        self._validationResult = None

    def clear_validation_result(self):
        """ Clears the cached validation result. """
        self._validationResult = None # an item was set, clear the validation flag to get a re-validation

    def validate(self):
        """ Validate (and clean on success) the form. """

        if self._validationResult is not None:
            return self._validationResult

        return self._actually_validate_and_clean()

    def _actually_validate_and_clean(self):
        """ Actually validates and cleans. """

        result = super().validate()
        self._validationResult = result

        if result:

            for field in self._fields.values():
                if field.__class__ is TextField or field.__class__ is TextAreaField:
                    if field.data:
                        if hasattr(field.data, 'strip'):
                            field.data = field.data.strip()
                    else:
                        field.data = None

        return result

    def _get_translations(self):
        """
        When passing a dictionary (instead of a Request object) for the form data
        an error occurs in tipyext.wtforms.form._get_translations:102 so this
        method is to override the method and avoid that error.
        """
        # TODO: remove this method and determine why it is failing in tipyext.wtforms.form
        return None

class TextListField(Field):
    """
    Field for converting comma delimited list into an actual python list.
    See here for more info on Custom Fields: http://wtforms.simplecodes.com/docs/0.6.1/fields.html#custom-fields
    """
    widget = TextInput()

    def __init__(self, *args, **kwargs):
        """ Declare self.data to quiet pylint. """
        self.data = None
        super().__init__(*args, **kwargs)

    def _value(self):
        """ Return the value of this field as a string. """
        if self.data:
            return ', '.join(self.data)
        else:
            return ''

    def process_formdata(self, valuelist):
        """
        This will be called during form construction with data supplied through the formdata argument.
        """
        if len(valuelist) == 1:
            # data entered from form
            self.data = [text.strip() for text in valuelist[0].split(',') if text.strip()]
        else:
            # batch import list of fields
            self.data = valuelist

class GenericJsonField(Field):
    """
    Field for accepting data and converting into an consumable json format.

    Acceptable values include a list of dictionaries.

    Eg.
    [{
        "field1": "http://www.example.com/vid1.mp4",
        "key2": "http://www.example.com/th1.jpg",
        "something": "Watch us in action!"
    }, {
        "field1": "http://www.example.com/vid2.mp4",
        "something": "Last Thursday's Outing"
    }]
    """
    widget = HiddenInput()

    def __init__(self, *args, **kwargs):
        """ Initialize data. """
        kwargs['default'] = kwargs.get('default', '[]')
        self.data = "[]"
        super().__init__(*args, **kwargs)

    def _value(self):
        """ Returns the value as a json string for displaying in a form. """
        return self.data

    def process_formdata(self, valuelist):
        """
        This will be called during form construction with data supplied through the formdata argument.
        If valuelist is just a list of urls it will be converted into a list of dicts and
        its json representation assigned to self.data.

        NOTE:
        This is a little strange because of the difference when passing data from a form submission or via batch import.
        In the case of a form submission, the value will be contained in a json string in the first item in valuelist.
        When batch importing the valuelist contains the actual python list of values we need to dump to json.
        """
        if valuelist and len(valuelist) > 0:
            if isinstance(valuelist[0], str):
                # data from form submission just pass json value
                self.data = valuelist[0]
            else:
                # python data from batch import, dump to json
                self.data = json.dumps(valuelist)

    #pylint: disable=W0613
    def post_validate(self, form, validation_stopped):
        """ Check that the data will JSON deserialize. """
        if not validation_stopped:
            try:
                json.loads(self.data)
            except Exception as e:
                raise ValueError(e.args[0])

class DictConvertingJsonField(GenericJsonField):
    """
    Field for accepting a list of strings or a list of dictionaries. If a list of strings are provided,
    they are converted into a list of dictionaries using the key STRING_KEY.

    So, for an input of ["abc", "123"], process_formdata will convert this to [{"str":"abc"}, {"str":"123"}].
    """
    STRING_KEY = 'str' # To override

    def process_formdata(self, valuelist):
        """
        This will be called during form construction with data supplied through the formdata argument.
        If valuelist is just a list of urls it will be converted into a list of dicts and
        its json representation assigned to self.data.
        """

        if valuelist and len(valuelist) > 0:
            if not isinstance(valuelist[0], dict):

                try:
                    # if data from form submission need to load from json value
                    valuelist = json.loads(valuelist[0])
                except ValueError:
                    valuelist = [{self.STRING_KEY : value} for value in valuelist]

            self.data = json.dumps(valuelist)

class VideosJsonField(DictConvertingJsonField):
    """
    Field for accepting video data and converting into an consumable json format.

    Acceptable values include a list of urls or video dictionaries.

    E.g.,
    ["http://www.youtube.com/watch?v=zzfQwXEqYaI", "http://www.youtube.com/watch?v=ntT7v47RIds"]

    (or)

    [{
        "url": "http://www.example.com/vid1.mp4",
        "thumbnail_url": "http://www.example.com/th1.jpg",
        "title": "Watch us in action!"
    }, {
        "url": "http://www.example.com/vid2.mp4",
        "title": "Last Thursday's Outing"
    }]
    """
    STRING_KEY = 'url'

class ImagesJsonField(DictConvertingJsonField):
    """
    Field for accepting images data and converting into a consumable json format.

    Acceptable values include a list of urls or image dictionaries.

    E.g.,
    ["http://www.images.com/foo.gif", "http://www.images.com/bar.jpg"]

    (or)

    [{
        "url": "http://www.images.com/foo.gif",
        "caption": "My Caption",
        "alt_text": "My Alt Text",
        "title": "My Title"
    }, {
        "url": "http://www.images.com.bar.jpg"
    }]
    """
    STRING_KEY = 'url'


class MultiTextField(FieldList):
    """ FieldList that add entry from the given list using getlist() """

    def __init__(self, unbound_field, label=None, validator=None, **kwargs):
        super().__init__(unbound_field, label, validator, **kwargs)
        self.entries = []

    def process(self, formdata, data=None):
        """ Override process of FieldList """
        data = data or []
        if formdata:
            values = formdata.getlist(self.name)
            for val in values:
                self.append_entry(val)
        else:
            for val in data:
                self.append_entry(val)

    def set_data(self, data):
        """ Sets data for the multi text field control"""
        data = data or []
        for val in data:
            self.append_entry(val)


def validate_json(field, required_fields):
    """ Validates the json field values.

    @param field: The field to validate the data of
    @param required_fields: A string, or list of strings, containing the keys of the required fields
    """

    if not isinstance(required_fields, list):
        required_fields = [required_fields]

    values = json.loads(field.data)

    if not values and required_fields:
        for required_field in required_fields:
            raise validators.ValidationError('%s is required.' % required_field)

    for value in values:
        for required_field in required_fields:
            if not value.get(required_field):
                raise validators.ValidationError('%s is required.' % required_field)


def validate_json_date_format(field):
    """ Validates that a given date is in the format %Y-%m-%d """
    date_list = json.loads(field.data)
    for dates in date_list:
        start_date_string = dates.get("start")
        end_date_string = dates.get("end")
        expiry_date_string = dates.get("expiry")
        if start_date_string:
            try:
                datetime.strptime(start_date_string, "%Y-%m-%d")
            except:
                raise validators.ValidationError('Start Date is in incorrect format (should be YYYY-MM-DD)')
        if end_date_string:
            try:
                datetime.strptime(end_date_string, "%Y-%m-%d")
            except:
                raise validators.ValidationError('End Date is in incorrect format (should be YYYY-MM-DD)')
        if expiry_date_string:
            try:
                datetime.strptime(expiry_date_string, "%Y-%m-%d")
            except:
                raise validators.ValidationError('Expiry Date is in incorrect format (should be YYYY-MM-DD)')

# pylint: disable=W0613
# Unused argument 'form'
def validate_coupons(form, field):
    """ Make sure all coupons have the required 'title' fields.
        Make sure that all optional date fields are in the correct format.
    """
    validate_json(field, 'title')
    validate_json_date_format(field)

def validate_images(form, field):
    """ Make sure all images have the required 'url' field. """
    values = json.loads(field.data)
    if not values:
        raise validators.ValidationError('This field is required.')

def validate_videos(form, field):
    """ Make sure all videos have the required 'videos' field. """
    validate_json(field, 'url')

class MicrositeHTMLParse(HTMLParser):
    """ Parse HTML-formatted text to check that open, close tags match. """
    IGNORED_TAGS = ['area', 'br', 'hr', 'img', 'input', 'option', 'param', 'wbr']
    def __init__(self):
        HTMLParser.__init__(self)
        self.open_tags = []

    def handle_starttag(self, tag, attrs):
        if tag not in self.IGNORED_TAGS:
            self.open_tags.insert(0, tag)

    def handle_endtag(self, tag):
        if tag in self.IGNORED_TAGS:
            return
        if self.open_tags[0] != tag:
            logging.warn("HTML Tag mismatch: expect %s, get %s", self.open_tags[0], tag)
        else:
            self.open_tags.pop(0)

def validate_textarea(form, field):
    """ Check for missing/unmatched HTML tags, and attempt to close any open tags. """
    if field and field.data:
        parser = MicrositeHTMLParse()
        parser.feed(field.data)
        if len(parser.open_tags) > 0:
            logging.warn("Unmatched HTML tags: (%s) in %s, data %s",
                         ', '.join(parser.open_tags), field.name, field.data)
            close_tags = ''.join(['</%s>' % t for t in parser.open_tags])
            field.data = field.data + close_tags
