""" Workflows for processing partners. """
import settings
from wtforms import validators, fields

from app.models.partner import Partner
from app.domain.workflow import CleaningForm
from app.domain import exceptions
from app.domain.workflow.exceptions import ValidationException
from app.domain.url_mappings import get_host_pid_mapping, add_host_pid_mapping
from settings import VSOCIAL_FB_APP_ID


def generate_default_hostname(pid):
    """
    Builds a default hostname using the DEFAULT_DOMAIN, e.g., '[pid].pdqs.mobi'.
    """
    if not pid:
        raise ValueError('pid is required.')
    hostname = '{}.{}'.format(pid, settings.DEFAULT_DOMAIN)
    hostname = hostname.lower()
    return hostname


def create_partner_with_host(form):
    """ Creates a new parter and adds the host mapping.

    @returns the pid of the newly created partner
    @raises PartnerHostExistsException if there is already an equivalent (pid, host)
    @raises PartnerExistsException if the pid is already in use
    @raises HostPidMappingExistsException if the host is already in use and pointing to a different partner
    """

    if not form:
        raise ValueError('form is required.')
    if not isinstance(form, CreatePartnerForm):
        raise ValueError('form must be a CreatePartnerForm.')
    if not form.validate():
        raise ValidationException(form.errors)

    pid = form.pid.data.upper()
    hostname = form.hostname.data.lower()
    name = form.name.data
    website = form.website.data
    contact_email_from = form.contact_email_from.data
    google_analytics_id = form.google_analytics_id.data
    microsite_cache_time = form.microsite_cache_time.data
    strict_map = form.strict_map.data
    show_partner_footer = form.show_partner_footer.data
    enable_sitemap = form.enable_sitemap.data
    api_key = form.api_key.data

    # perform some early checks; the race condition with the later calls are protected with transactions
    hpm = get_host_pid_mapping(hostname)
    if hpm and hpm.pid == pid:
        raise exceptions.PartnerHostExistsException(hostname, pid)
    if hpm:
        raise exceptions.HostPidMappingExistsException(hostname, hpm.pid)
    p = Partner.get(pid)
    if p:
        raise exceptions.PartnerExistsException(pid)

    p = Partner.create(pid, name, website=website, contact_email_from=contact_email_from,
                       google_analytics_id=google_analytics_id, microsite_cache_time=microsite_cache_time,
                       strict_map=strict_map, show_partner_footer=show_partner_footer,
                       enable_sitemap=enable_sitemap, api_key=api_key)
    add_host_pid_mapping(hostname, pid, is_default=True)
    return p.pid

REQUIRED = validators.required()
REGEXP = validators.Regexp
LENGTH = validators.Length
URL = validators.URL()
OPTIONAL = validators.Optional()


class AbstractPartnerForm(CleaningForm):
    """ Fields common to partner creation and update. """
    pid = fields.TextField(
            'Partner ID (PID)',
            [REQUIRED,
             LENGTH(min=2, max=6)])

    google_analytics_id = fields.TextField(
        'Google Analytics Web Property Id',
        [OPTIONAL, LENGTH(max=15)],
        description='The web property id to use for tracking analytics for this partner.'
    )

    microsite_cache_time = fields.IntegerField(
        'Cache Time',
        [OPTIONAL],
        description='The number of seconds to cache a microsite page.')

    strict_map = fields.BooleanField(
        'Strict Map',
        description='Only render google map if lat/long provided.'
    )

    show_partner_footer = fields.BooleanField(
        'Show "Provided By" in Footer',
        description="""Show the "Provided By <partner or provider's name>" info in the page footer.""",
        default=True
    )

    enable_sitemap = fields.BooleanField(
        'Enable Sitemap',
        description='Include a sitemap that lists all of the My Listings for this partner.',
        default=True
    )

    api_key = fields.TextField(
        'API Key',
        [OPTIONAL, LENGTH(min=1)],
        description='This Key is used to access the batch upload API.'
    )

    fb_app_id = fields.TextField(
        'Facebook Application ID',
        [OPTIONAL, LENGTH(max=100)],
        description='Facebook Application ID is used for partner whitelabel setup. ' +
                    'If not set, the default Facebook Application is used.',
        default=VSOCIAL_FB_APP_ID)


class CreatePartnerForm(AbstractPartnerForm):
    """ Form to create a new partner. """

    name = fields.TextField(
            'Name',
            [REQUIRED])

    hostname = fields.TextField(
            'Hostname',
            [REQUIRED])

    website = fields.TextField(
        'Website',
        [OPTIONAL, URL, LENGTH(max=100)],
        description='(e.g., "http://www.example.com")')

    contact_email_from = fields.TextField(
        'Contact Email From Address',
        [OPTIONAL, LENGTH(max=100)],
        description='The email address contact forms should come from.')


class UpdatePartnerForm(AbstractPartnerForm):
    """ Form to update a partner. """
    name = fields.TextField(
        'Name',
        [REQUIRED]
    )
