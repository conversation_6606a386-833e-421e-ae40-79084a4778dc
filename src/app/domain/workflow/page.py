""" Page management workflows """

import json
import logging
from google.appengine.api.namespace_manager import namespace_manager
from wtforms import fields, validators
from fantasm.fsm import startStateMachine
from app.constants import CUSTOM, MOBILE_GALLERY_STYLES, DESKTOP_GALLERY_STYLES, MGS_TABLE, DGS_LIGHTBOX, TEMPLATES, \
    IMAGES, VIDEOS, VALID_NAVIGATION_SLUG_REGEX_STRING, NAVIGATION_SLUG_MAX_LENGTH, POST_PERMALINK_SLUG, REVIEWS
from app.domain.workflow import exceptions, CleaningForm, VideosJsonField, ImagesJsonField, GenericJsonField, \
                                validate_coupons, validate_images, validate_videos, validate_textarea
from app.domain.exceptions import TooManyPageidBlobMappingsException, PageidBlobMappingExistsException
from app.domain.pageid_blob_mapping import process_image_dicts
from app.domain.page import create_page_entity, update_page_entity, get_page, ImagesPage
from app.domain.validation import require_args


def create_page(form, microsite):
    """ Creates a page on the microsite.

    @raises MicrositeNotFoundException if the microsite cannot be found
    """
    if not form:
        raise ValueError('form is required.')
    if not isinstance(form, PageForm):
        raise ValueError('form must be a PageForm.')
    if not form.validate():
        raise exceptions.ValidationException(form.errors)

    # if the navigation_slug is used, append an integer until it is unique
    def is_navigation_slug_available(microsite, navigation_slug):
        """ Checks to see if the tab_name is available for use (they must be unique). """
        for item in microsite.main_navigation:
            if item.navigation_slug == navigation_slug:
                return False
        return True
    if form.data.get('template') == REVIEWS:
        navigation_slug = 'review'
    else:
        navigation_slug = form.slug.data or ''
        if not is_navigation_slug_available(microsite, navigation_slug):
            for n in range(2, 100):
                alternate_nav_slug = '{}-{}'.format(navigation_slug, n)
                if is_navigation_slug_available(microsite, alternate_nav_slug):
                    navigation_slug = alternate_nav_slug
                    break

    kwargs = form.data
    kwargs['pid'] = microsite.pid
    kwargs.pop('tab_name', None)
    kwargs.pop('slug', None)
    kwargs.pop('order', None)

    pageid = create_page_entity(**kwargs)

    template = kwargs.get('template', CUSTOM)

    tab_name = form.tab_name.data
    order = form.order.data
    kwargs = {}
    if order:
        kwargs['order'] = order
    kwargs['icon'] = template
    microsite.add_navigation(navigation_slug, pageid, tab_name, **kwargs)

    return pageid


def update_page(form, microsite):
    """ Updates a page from an UpdatePageForm. """

    if not form:
        raise ValueError('form is required.')
    if not isinstance(form, UpdatePageForm):
        raise ValueError('form must be an UpdatePageForm.')
    if not form.validate():
        raise exceptions.ValidationException(form.errors)

    kwargs = form.data
    kwargs['pid'] = microsite.pid

    update_page_entity(**kwargs)

    # Update the nav item with a new tab name. This is a bit hacky because we're just going to look
    # for the first navigation item that points to our current pageid (the model however allows multiple
    # tabs to point to the same page). We then assume this is the appropriate navigation item to update.
    if kwargs.get('tab_name'):
        item = None
        for i in microsite.main_navigation:
            if i.pageid == kwargs['pageid']:
                item = i
                break
        if item and item.name != kwargs['tab_name']:
            microsite.update_navigation(item.navigation_slug, name=kwargs['tab_name'])


def process_page(form, pageid, msid, pid=None):
    """ Performs page specific processing. Returns an error message to be gritter'd. """

    pid = pid or form.pid.data or namespace_manager.get_namespace()
    page = get_page(pageid, msid, pid=pid)
    error_message = None

    if page.template == IMAGES:
        image_dicts = json.loads(form.images.data)
        try:
            errors = process_image_dicts(image_dicts, pageid, msid, pid=pid)
            if errors:
                error_message = "There was a problem processing the following urls: %s" % ", ".join(errors)
        except (TooManyPageidBlobMappingsException, PageidBlobMappingExistsException) as e:
            error_message = e.message

    if page.template == VIDEOS:
        youtube_context = {
            'pid': pid,
            'msid': msid,
            'pageid': pageid,
        }
        startStateMachine('RetrieveYouTubeMetaInfo', youtube_context)

    return error_message


@require_args
def process_page_from_api(page, microsite):
    """ Process a page from api data. Includes creating a new page or updating an existing one. """
    error_urls = []
    msid = microsite.msid
    pid = microsite.pid

    nav_item = microsite.get_navigation(page.slug)
    if nav_item:
        form_class = import_string('app.domain.workflow.page.Update%sPageForm' % page.template)
    else:
        form_class = import_string('app.domain.workflow.page.%sPageForm' % page.template)

    page_form = form_class(page.formdata)
    page_form.msid.data = msid
    page_form.pid.data = pid
    page_form.template.data = page.template

    if nav_item:
        pageid = nav_item.pageid
        page_form.pageid.data = pageid
        update_page(page_form, microsite)
        logging.info("Updated %s Page: %s", page.template, pageid)
    else:
        pageid = create_page(page_form, microsite)
        logging.info("Created %s Page: %s", page.template, pageid)

    if page.template == IMAGES:
        image_dicts = json.loads(page_form.images.data)
        error_urls = process_image_dicts(image_dicts, pageid, msid, pid=pid)

    return {
        'error_urls': error_urls,
        'pageid': pageid
    }

REQUIRED = validators.Required()
LENGTH = validators.Length
OPTIONAL = validators.Optional()
EMAIL = validators.Email()
REGEXP = validators.Regexp
VALID_COUPONS = validate_coupons
VALID_IMAGES = validate_images
VALID_VIDEOS = validate_videos
VALID_TEXTAREA = validate_textarea


class AbstractPageForm(CleaningForm):
    """ Fields common to all page forms. This form should not be instantiated. """

    title = fields.HiddenField(
        'Title',
        [REQUIRED, LENGTH(max=120)]
    )
    #pylint: disable=invalid-name
    h1 = fields.TextField(
        'Heading',
        [REQUIRED, LENGTH(max=100)]
    )
    meta_keywords = fields.TextField(
        'Meta Keywords',
        [OPTIONAL, LENGTH(max=500)]
    )
    meta_description = fields.TextField(
        'Meta Description',
        [OPTIONAL, LENGTH(max=500)]
    )
    template = fields.HiddenField(
        'Template',
        [REQUIRED],
        default=CUSTOM
    )
    top_content = fields.TextAreaField(
        'Top Content',
        [OPTIONAL, VALID_TEXTAREA])
    bottom_content = fields.TextAreaField(
        'Bottom Content',
        [OPTIONAL, VALID_TEXTAREA])
    msid = fields.HiddenField(
        'MSID',
        [REQUIRED]
    )
    pid = fields.HiddenField(
        'PID'
    )
    tab_name = fields.TextField(
        'Tab Name',
        [REQUIRED, LENGTH(max=25)]
    )

    def validate_template(self, field):
        """ Ensures template is valid """
        if field.data not in TEMPLATES:
            raise validators.ValidationError('Invalid template "%s"' % field.data)

    def validate_tab_name(self, field):
        """ ensures tab name can not be reserved slug """
        if field.data.lower() == POST_PERMALINK_SLUG:
            raise validators.ValidationError('%s is a resolved name' % field.data)


class PageForm(AbstractPageForm):
    """ Holds all the fields for a Page. """
    slug = fields.HiddenField(
        'Slug',
        [REQUIRED, LENGTH(max=NAVIGATION_SLUG_MAX_LENGTH),
         REGEXP(VALID_NAVIGATION_SLUG_REGEX_STRING, message="Invalid slug, can only be letters, numbers and dashes.")],
        description="The url for this page, e.g., http://www.example.com/your-site/[slug]/"
    )
    order = fields.IntegerField(
        'Tab Position',
        [OPTIONAL],
        description="The position of the tab in the tab list, 1 means first."
    )


class CustomPageForm(PageForm):
    """ Fields for a CustomPage. """
    pass


class ReviewsPageForm(PageForm):
    """ Fields for a CustomPage. """
    #pylint: disable=invalid-name
    h1 = fields.HiddenField(
        'Heading',
        [OPTIONAL],
        default=None
    )
    top_content = fields.HiddenField(
        'Top Content',
        [OPTIONAL],
        default=None
    )
    bottom_content = fields.HiddenField(
        'Bottom Content',
        [OPTIONAL],
        default=None
    )


class ImagesPageForm(PageForm):
    """ Fields for an ImagesPage. """
    mobile_gallery_style = fields.SelectField(
        'Mobile Gallery Style',
        [OPTIONAL],
        default=MGS_TABLE,
        choices=list(zip(list(sorted(MOBILE_GALLERY_STYLES)), list(sorted(MOBILE_GALLERY_STYLES)))),
        description="Indicates how images will be displayed on a mobile device."
    )
    desktop_gallery_style = fields.SelectField(
        'Desktop Gallery Style',
        [OPTIONAL],
        default=DGS_LIGHTBOX,
        choices=list(zip(list(sorted(DESKTOP_GALLERY_STYLES)), list(sorted(DESKTOP_GALLERY_STYLES)))),
        description="Indicates how images will be displayed on a desktop computer."
    )
    images = ImagesJsonField(
        'Images',
        [REQUIRED, VALID_IMAGES]
    )


class VideosPageForm(PageForm):
    """ Fields for a VideosPage. """
    videos = VideosJsonField(
        'Videos',
        [OPTIONAL, VALID_VIDEOS]
    )


class ContactPageForm(PageForm):
    """ Fields for a ContactPage. """
    email = fields.TextField(
        'Email',
        [OPTIONAL, EMAIL],
        description="Email address where responses will be sent."
    )


class CouponsPageForm(PageForm):
    """ Fields for a CouponsPage. """
    coupons = GenericJsonField(
        'Coupons',
        [OPTIONAL, VALID_COUPONS]
    )


class UpdatePageForm(AbstractPageForm):
    """ Form to update an existing page. """
    pageid = fields.HiddenField(
        'PAGEID',
        [REQUIRED]
    )


class UpdateCustomPageForm(UpdatePageForm):
    """ Form to update an existing custom page. """
    pass


class UpdateReviewsPageForm(UpdatePageForm):
    """ Form to update an existing custom page. """
    #pylint: disable=invalid-name
    h1 = fields.HiddenField(
        'Heading',
        [OPTIONAL],
        default=None
    )
    top_content = fields.HiddenField(
        'Top Content',
        [OPTIONAL],
        default=None
    )
    bottom_content = fields.HiddenField(
        'Bottom Content',
        [OPTIONAL],
        default=None
    )


class UpdateImagesPageForm(UpdatePageForm):
    """ Form to update an existing images page. """

    mobile_gallery_style = fields.SelectField(
        'Mobile Gallery Style',
        [OPTIONAL],
        default=MGS_TABLE,
        choices=list(zip(list(sorted(MOBILE_GALLERY_STYLES)), list(sorted(MOBILE_GALLERY_STYLES)))),
        description="Indicates how images will be displayed on a mobile device."
    )
    desktop_gallery_style = fields.SelectField(
        'Desktop Gallery Style',
        [OPTIONAL],
        default=DGS_LIGHTBOX,
        choices=list(zip(list(sorted(DESKTOP_GALLERY_STYLES)), list(sorted(DESKTOP_GALLERY_STYLES)))),
        description="Indicates how images will be displayed on a desktop computer."
    )
    images = ImagesJsonField(
        'Images',
        [REQUIRED, VALID_IMAGES]
    )

    def __init__(self, *args, **kwargs):
        """ Add image data from mappings if it does not already exist. """
        super().__init__(*args, **kwargs)

        formdata = (args and args[0]) or kwargs.get('formdata')
        if formdata:
            # this means that this is a POST request with a unicode multidict
            # we want to use that POST data to update the images
            return

        page = kwargs.get('obj')
        if not page or not isinstance(page, ImagesPage):
            # page wasn't given so nothing to get mappings from
            return

        # loop through the PageidBlobMappings and add get their image data
        images_data = []
        for mapping in page.mappings:
            images_data.append({
                'blobkey': mapping.blobkey and str(mapping.blobkey), # uniquely identifies this image
                'serving_url': mapping.serving_url,
                'url': mapping.import_info and  mapping.import_info.url,
                'md5_hash': mapping.import_info and mapping.import_info.md5_hash,
                'caption': mapping.caption,
                'alt_text': mapping.alt_text,
                'title': mapping.title,
                'filename': mapping.import_info and mapping.import_info.filename
            })
            # set images data to json string of mapping data
        self.images.data = json.dumps(images_data)


class UpdateVideosPageForm(UpdatePageForm):
    """ Form to update an existing videos page. """
    videos = VideosJsonField(
        'Videos',
        [OPTIONAL, VALID_VIDEOS]
    )


class UpdateContactPageForm(UpdatePageForm):
    """ Form to update an existing contact page. """
    email = fields.TextField(
        'Email',
        [OPTIONAL, EMAIL],
        description="Email address where responses will be sent."
    )


class UpdateCouponsPageForm(UpdatePageForm):
    """ Fields for a CouponsPage. """
    coupons = GenericJsonField(
        'Coupons',
        [OPTIONAL, VALID_COUPONS]
    )
