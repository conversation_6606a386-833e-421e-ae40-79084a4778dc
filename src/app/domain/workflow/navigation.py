""" Navigation management workflows. """

from wtforms import fields, validators
from app.domain.microsite import get_microsite
from app.domain.workflow import CleaningForm
from app.constants import VALID_NAVIGATION_SLUG_REGEX_STRING, NAVIGATION_SLUG_MAX_LENGTH


REQUIRED = validators.Required()
LENGTH = validators.Length
OPTIONAL = validators.Optional()
REGEXP = validators.Regexp


class UpdateNavigationForm(CleaningForm):
    """ Form containing fields to update navigation and provide validation. """

    pageid = fields.HiddenField(
        'PAGEID',
        [REQUIRED]
    )
    msid = fields.HiddenField(
        'MSID',
        [REQUIRED]
    )
    pid = fields.HiddenField(
        'PID'
    )

    navigation_slug = fields.HiddenField(
        'Slug',
        [REQUIRED, LENGTH(max=NAVIGATION_SLUG_MAX_LENGTH),
         REGEXP(VALID_NAVIGATION_SLUG_REGEX_STRING, message="Invalid slug, can only be letters, numbers and dashes.")],
        description="The url for this page, e.g., http://www.example.com/your-site/[slug]/"
    )
    order = fields.IntegerField(
        'Tab Position',
        [OPTIONAL],
        description="The position of the tab in the tab list, 1 means first."
    )

    def validate_order(self, field):
        """ Order cannot be less than one. """
        if field.data < 1:
            raise validators.ValidationError('Tab Position must be 1 or greater.')

    def validate_navigation_slug(self, field):
        """ Navigation slug must be unique. """
        microsite = get_microsite(self.msid.data, pid=self.pid.data)
        other_slugs = [nav.navigation_slug for nav in microsite.main_navigation if nav.pageid != self.pageid.data]
        if field.data in other_slugs:
            raise validators.ValidationError('A page already exists with this slug. Please provide a unique slug.')