"""
Social integration related domain logic
"""
import logging
import re

from google.appengine.api.namespace_manager import get_namespace
from google.appengine.ext import deferred, ndb

import settings
from app.domain.google_my_business import GoogleMyBusinessSync
from app.models.partner import Partner

from coresdk import API_KEY as CS_KEY, CSApiException
from coresdk.social_post import SocialPostManager
from coresdk.social_profile import SocialProfileManager
from coresdk.social_service import SocialServiceManager

from app.constants import DEFERRED_ROOT, MSID
from app.domain.exceptions import SiteSpidExistsException, SocialProfileNotFoundException, UnableToAddFbPageException, \
    UnableToAddTabException
from app.domain.validation import require_args, require_arg_list
from app.domain.url_mappings import lookup_hostslug_msid_mapping_for_msid
from app.models.microsite import Microsite
from app.models.social import MicrositeFBPageAssociation
from coresdk_v2.base import FacebookPageClient, ApiException

from settings import LONG_FORM_POST_CLIENT_TAG, MORE_INFO_CLIENT_TAG, MS_CLIENT_TAG, VSOCIAL_API
from settings import VSOCIAL_USER


class SitesVSocial:
    """ SitesVSocial """

    def __init__(self, msid, pid=None, spid=None, **kwargs):
        """ instance initialization and VStats social manager setup """
        self.vs_user = kwargs.get('user', VSOCIAL_USER)
        if not self.vs_user:
            raise ValueError('user is required')
        self.vs_api_key = kwargs.get('api_key', VSOCIAL_API)
        if not self.vs_api_key:
            raise ValueError('api_key is required')
        self.vs_target = kwargs.get('target', settings.ENVIRONMENT_NAME.lower())
        if not self.vs_target:
            raise ValueError('target is required')
        self.msid = msid
        if not self.msid:
            raise ValueError('msid is required')
        self.pid = pid
        self._spid = spid
        self.post_manager = SocialPostManager(self.vs_user, self.vs_api_key, target=self.vs_target)
        self.profile_manager = SocialProfileManager(self.vs_user, self.vs_api_key, target=self.vs_target)
        self.service_manager = SocialServiceManager(self.vs_user, self.vs_api_key, MS_CLIENT_TAG,
                                                    target=self.vs_target)

    @property
    def spid(self):
        """ Returns spid associated with a site """
        if not self._spid:
            self._spid = self._get_spid_for_site()
            if not self._spid:
                raise SocialProfileNotFoundException(
                    f'there is no spid associated with the msid: {self.msid}')
        return self._spid

    def _get_spid_for_site(self):
        """
        Returns the spid associated with a site.
        """
        # Check model for spid
        pid = self.pid or get_namespace()
        if pid:
            ms = Microsite.get_by_msid(self.msid, pid=pid)
            if ms and ms.spid:
                return ms.spid

        # Check core for spid
        profiles = self.profile_manager.lookupSocialProfileRegistrations(self.vs_user, self.msid)
        if not profiles:
            return None

        # Each site should only have one spid. An earlier bug introduced the possibility
        # that a site could have multiple spids. Any site with more than one needs to be investigated
        # to see if it is properly tied to SM accounts
        if len(profiles) > 1:
            logging.error('More than one spid exists for this msid. msid: %s, profiles %s', self.msid, str(profiles))

        profile = profiles[0]
        return profile.get(CS_KEY.SOCIAL_PROFILE_ID)

    @require_args
    def _get_service(self, **match_criteria):
        """
        Return a service from the social profile based on the provided match criteria or None if it does not exist.
        """
        social_profile = self.lookup_social_profile()
        services_map = social_profile.get(CS_KEY.SOCIAL_PROFILE_SERVICES)
        for services in services_map.values():
            for service in services:
                for k, v in match_criteria.items():
                    if service.get(k) == v:
                        return service
        return None

    @staticmethod
    def add_url_to_existing_url(existing_url, ms_url):
        """Construct a URL to add to a Facebook about page."""
        ms_url = ms_url or ""
        existing_url = existing_url or ""
        # Cleaning the existing url from new line characters
        clean_existing_url = existing_url.replace("\r\n", " ")
        existing_urls = clean_existing_url.split(" ") if clean_existing_url else []
        if not ms_url in clean_existing_url:
            existing_urls.append(ms_url)
        return ' '.join(existing_urls)

    @staticmethod
    def _remove_url_from_existing_url(existing_url, ms_url):
        """Remove a URL from an existing Facebook about page url"""

        ms_url = ms_url or ""
        existing_url = existing_url or ""

        full_ms_url = f"http://{ms_url}"
        full_ms_url_with_slash = f"{full_ms_url}/"

        if full_ms_url_with_slash in existing_url:
            ms_url = full_ms_url_with_slash
        elif full_ms_url in existing_url:
            ms_url = full_ms_url

        modified_url = re.sub(ms_url, '', existing_url)

        # Ensure only one space between words
        tokens = modified_url.split()
        return " ".join(tokens)

    def set_social_sync(self, ssid, social_sync_flag):
        """
        Sets the social sync flag for the given social service
        """
        return self.service_manager.setSocialSyncForService(self.spid, ssid, social_sync_flag)

    def get_social_post(self, social_post_id):
        """
        Fetches the social post under the given ID from core. If spid is passed, then msid does not need to be passed.
        """
        results = self.post_manager.fetchScheduledOrPastSocialPostsAsync(social_post_id, self.spid).get_result()
        for v in results.values():
            if social_post_id in [v.get('pastPostId'), v.get('fromScheduledPostId')]:
                return v
        return {}

    def lookup_social_registrations_for_site(self, uid=None):
        """ returns all the social registrations for a given msid or spid"""
        kwargs = {}
        if self.spid:
            kwargs['spid'] = self.spid
        else:
            kwargs['uid'] = uid or self.vs_user
            kwargs['accountId'] = self.msid
        return self.profile_manager.lookupSocialProfileRegistrations(**kwargs)

    def lookup_social_profile(self):
        """ Lookup social profile information """
        return self.profile_manager.getProfile(self.spid)

    def is_valid_more_info_tab(self, fb_page_id):
        """
        validating that given facebook page id is registered under given spid and has a more info tab associated with it
        :return: boolean value that represent whether the specified facebook page id is registered under a spid
        """
        if not fb_page_id:
            raise ValueError('fb_page_id is required.')

        services = None
        try:
            results = self.profile_manager.getProfile(self.spid)
            services = results.get(CS_KEY.SOCIAL_PROFILE_SERVICES)
        except Exception:
            logging.info('Unable to retrieve social profile information for spid(%s)', self.spid, exc_info=True)

        valid_fb_pids = []
        if services:
            fb_pages = services.get(CS_KEY.FB_PAGE, [])
            valid_fb_pids = [page_info[CS_KEY.SOCIAL_SERVICE_FB_PAGE_ID] for page_info in fb_pages]

        more_info_tab_association = MicrositeFBPageAssociation.build_key(fb_page_id=fb_page_id).get()

        return (fb_page_id in valid_fb_pids) and more_info_tab_association

    def deregister_social_profile(self):
        """
        Deregisters a social profile in Core
        :return: existing social profile id
        """
        self.profile_manager.deregisterFromSocialProfile(self.spid, self.msid)

    def register_social_profile(self):
        """
        Registers a social profile in Core
        :return: existing social profile id
        """
        data = self.profile_manager.registerForSocialProfile(self.spid, self.msid)

        return data.get(CS_KEY.SOCIAL_PROFILE_ID)

    def add_facebook_page_to_social_profile(self, fb_page_id, fb_user_id=None):
        """ Add a Facebook page into a social profile
        :param spid:       required. social profile id.
        :param fb_page_id: required. Id of a Facebook page
        :param fb_user_id: Facebook user id.
                           When a master Facebook user is defined on partner, fb_user_id can be optional
        """
        if not fb_page_id:
            raise ValueError('fb_page_id is required')

        data = self.service_manager.associateFacebookPage(
            spid=self.spid, facebookPageId=fb_page_id, facebookUserId=fb_user_id, clientTag=MORE_INFO_CLIENT_TAG
        )

        return data

    def add_facebook_page_from_url_to_social_profile(self, fb_page_url):
        """
        Add a Facebook Page into a social profile.
        Returns the facebook page id associated with this URL.
        """
        if not fb_page_url:
            raise ValueError('fb_page_url is required')

        data = self.service_manager.associateFacebookPageFromUrl(
            spid=self.spid, facebookPageUrl=fb_page_url, clientTag=MORE_INFO_CLIENT_TAG
        )

        return data[CS_KEY.FACEBOOK_PAGE_ID]

    def update_facebook_about_page(self, fb_page_id, site_url=None):
        """
        Update the Information area of the facebook about page.
        """
        try:
            self.service_manager.updateInfoFacebookPage(self.spid, fb_page_id, siteUrl=str(site_url))
        except CSApiException:
            logging.error("Core Services could not connect to facebook page %s", fb_page_id)
            raise

    def add_site_to_facebook(self, **kwargs):
        """
        Add a Microsite to Facebook more info tab
        :param fb_page_id: Facebook Page ID the given site is going to be added to
                            Either fb_page_id or fb_page_url must be specified
        :param fb_user_id: Facebook user ID the who has admin access to the FB page
        :param fb_page_url: Facebook Page url that the given site is going to be added to.
                            Either fb_page_url or fb_page_id must be specified
        :param pid: required. Unique identifier for a given partner.
        :param app_id: optional. Can be specified.
                        Otherwise, use fb_app_id information from partner entity. If not set on partner entity,
                        default to Sites Facebook app id stored in environment specific settings file.
        :param name: optional.
        :return: Dict representation of the created MicrositeFBPageAssociation entity
        """

        fb_page_id = kwargs.get("fb_page_id")
        fb_page_url = kwargs.get("fb_page_url")
        if not fb_page_id and not fb_page_url:
            raise ValueError("Either fb_page_id or fb_page_url is required.")

        fb_user_id = kwargs.get("fb_user_id")
        pid = kwargs.get("pid", get_namespace())
        if not pid:
            raise ValueError('pid is required')

        partner = Partner.get(pid)
        app_id = kwargs.get('app_id', partner.fb_app_id)
        if not app_id:
            raise ValueError("app_id is required")

        assoc_dict = {}
        try:
            # Before a site can be added to a Facebook page,
            # the Facebook page need to be added to the social profile
            if fb_page_url:
                fb_page_id = self.add_facebook_page_from_url_to_social_profile(fb_page_url=fb_page_url)
            else:
                self.add_facebook_page_to_social_profile(fb_page_id=fb_page_id,
                                                         fb_user_id=fb_user_id)
        except CSApiException as e:
            logging.exception(e.args[0], exc_info=True)
            raise UnableToAddFbPageException("Unable to add facebook page or url for the given facebook user")

        try:
            # Brent Yates Mar 6, 2013 - We temporarily removed name from this because Core has problems setting it,
            # causing the add to revert (all in core)
            data = self.service_manager.addApplicationTabToFacebookPage(self.spid, fb_page_id, app_id)
            tab_id = data.get('tabId')
        except CSApiException as e:
            logging.exception(e.args[0], exc_info=True)
            raise UnableToAddTabException("Unable to add more info tab to a facebook page")

        @ndb.transactional
        def tx():
            """ transactional operations """

            key = MicrositeFBPageAssociation.build_key(fb_page_id=fb_page_id)
            association = MicrositeFBPageAssociation(
                key=key, fb_page_id=fb_page_id, msid=self.msid, pid=pid, fb_tab_id=tab_id
            )
            association.put()
            # TODO: I have a feeling this doesn't work properly because it references the object
            assoc_dict.update(association.to_dict(include=['fb_page_id', 'msid', 'pid', 'fb_tab_id']))

            defer_url = DEFERRED_ROOT + 'MicroSite/AddSiteUrlToFacebookPage/'
            deferred.defer(
                add_site_url_to_facebook_about_page, self.msid, fb_page_id, pid,
                _url=defer_url, _queue='default', _transactional=True
            )

            return assoc_dict

        if tab_id:
            return tx()

        return assoc_dict

    def remove_client_tag_from_facebook_page(self, fb_page_id):
        """
        Remove a Facebook Page from a social profile.
        """
        if not fb_page_id:
            raise ValueError('fb_page_id is required')

        profile = self.profile_manager.getProfile(self.spid)
        if not profile:
            raise ValueError('Cannot retrieve social profile %s' % self.spid)
        if not CS_KEY.SOCIAL_PROFILE_SERVICES in profile:
            raise ValueError('No services associated with profile %s' % self.spid)
        if not CS_KEY.FB_PAGE in profile[CS_KEY.SOCIAL_PROFILE_SERVICES]:
            raise ValueError('No Facebook pages associated with profile %s' % self.spid)

        # Find the ssid for the fb_page_id
        ssid = None
        for page in profile[CS_KEY.SOCIAL_PROFILE_SERVICES][CS_KEY.FB_PAGE]:
            if page[CS_KEY.FACEBOOK_PAGE_ID] == fb_page_id:
                ssid = page[CS_KEY.SOCIAL_SERVICE_ID]
                break

        if not ssid:
            raise ValueError('Cannot retrieve registered page %s' % fb_page_id)

        data = self.service_manager.removeTagFromService(self.spid, ssid, clientTag=MORE_INFO_CLIENT_TAG)

        return data

    def remove_site_from_all_facebook_pages(self, app_id=None):
        """
        Remove the More Info tab from Facebook for all sites that are registered with
        this msid.
        """
        pid = self.pid or get_namespace()
        if not pid:
            raise ValueError('pid is required')

        partner = Partner.get(pid)
        if not partner:
            raise ValueError("Cannot retrieve partner %s" % pid)

        app_id = app_id or partner.fb_app_id
        if not app_id:
            raise ValueError("app_id is required")

        fb_page_associations = self.get_facebook_pages_associated_with_site()
        for association in fb_page_associations:
            self.remove_site_from_facebook(fb_page_id=association.fb_page_id, app_id=app_id)

    def remove_site_from_facebook(self, **kwargs):
        """
        Remove the More Info tab from Facebook. Deregisters page from mobile Site
        """
        pid = self.pid or get_namespace()
        if not pid:
            raise ValueError('pid is required')

        fb_page_id = kwargs.get("fb_page_id")
        if not fb_page_id:
            raise ValueError("fb_page_id is required")

        partner = Partner.get(pid)
        app_id = kwargs.get('app_id', partner.fb_app_id)
        if not app_id:
            raise ValueError("app_id is required")

        # The URL has to be removed before removing the registration with Core
        self.remove_site_url_from_facebook_about_page(fb_page_id)

        association = MicrositeFBPageAssociation.build_key(fb_page_id=fb_page_id).get()
        tab_id = association.fb_tab_id

        try:
            self.service_manager.removeApplicationTabFromFacebookPage(self.spid, fb_page_id, tab_id)
        except CSApiException as e:
            logging.exception("Unable to remove Facebook More Info Tab %s from Facebook page %s", app_id, fb_page_id)
            if e.status != 404:
                raise
        except Exception:
            logging.exception("Unable to remove Facebook More Info Tab %s from Facebook page %s", app_id, fb_page_id)
            raise

        try:
            self.remove_client_tag_from_facebook_page(fb_page_id)
        except ValueError:
            logging.exception("Unable to remove Facebook More Info Tab %s from Facebook page %s", app_id, fb_page_id)
        except CSApiException as e:
            logging.exception("Unable to remove Facebook More Info Tab %s from Facebook page %s", app_id, fb_page_id)
            if e.status != 404:
                raise
        except Exception:
            logging.exception("Unable to remove client tag %s from Facebook page %s", self.spid, fb_page_id)
            raise

        key = MicrositeFBPageAssociation.build_key(fb_page_id=fb_page_id)
        key.delete()

    @staticmethod
    def _get_fb_page_id_to_msid_dict(fb_page_ids):
        """ Gets a dictionary of facebook page id to msid, for facebook page id's that have an msid associated """
        association_keys = [MicrositeFBPageAssociation.build_key(fb_page_id=fb_page_id) for fb_page_id in fb_page_ids]
        associations = ndb.get_multi(association_keys)
        results = {}
        for association in associations:
            if association:
                results[association.fb_page_id] = association.msid
        return results

    def get_facebook_page_information(self, facebook_page_ids):
        """
        Returns a dictionary of facebook page information keyed by facebook page id.
        """
        try:
            data = self.service_manager.getInfoFacebookPages(facebook_page_ids, spid=self.spid)
        except CSApiException as e:
            logging.exception('Could not get facebook page information')
            if e.status == 404:
                data = {}
            else:
                raise

        return data

    def get_facebook_pages_associated_with_site(self):
        """
        Returns a list of pages associated with this msid.
        """
        return MicrositeFBPageAssociation.query(MicrositeFBPageAssociation.msid == self.msid, namespace='').fetch()

    def _get_accounts_info_associate_with_facebook(self, spid, fb_pages_info, fb_user=None):
        """ Gets the account information with facebook page information """
        account = {}
        associations = self._get_fb_page_id_to_msid_dict(list(fb_pages_info.keys()))
        fb_user = fb_user or {}
        fb_pages = []
        # Build the list of facebook pages
        for fbPageId, info in fb_pages_info.items():
            fb_pages.append({
                CS_KEY.FACEBOOK_PAGE_ID: fbPageId,
                CS_KEY.SOCIAL_SERVICE_NAME: info.get(CS_KEY.SOCIAL_SERVICE_NAME) or info,
                'link': 'http://www.facebook.com/%s' % fbPageId,
                MSID: associations.get(fbPageId),
            })

        # add the pages to the account list
        account[CS_KEY.SOCIAL_PROFILE_ID] = spid
        account[CS_KEY.SOCIAL_SERVICE_FACEBOOK_USER_ID] = fb_user.get(CS_KEY.SOCIAL_SERVICE_FACEBOOK_USER_ID)
        account[CS_KEY.SOCIAL_SERVICE_NAME] = fb_user.get(CS_KEY.SOCIAL_SERVICE_NAME, 'Master Facebook User')
        account['facebookPages'] = fb_pages

        return account

    def get_facebook_accounts_associated_with_site(self):
        """
        Gets the facebook pages information associated with a user, including the microsite associations with the pages
        :param msid: The microsite ID
        :return: A list of dictionary's containing the account name, fb pages, and associations, ex:
        [{
            'spid' : 'some social profile id',
            'facebookUserId' : 'iqurbewobqup1379nbva8yf',
            'name' : 'USER-NAME',
            'facebookPages' : [
                {
                    'facebookPageId' : '123'
                    'name' : 'Page Name 01',
                    'link' : 'www.facebook.com/yourpage'
                    'msid' : None, #this is None when the fbPage doesn't have a site tab yet
                },
            ]
        }]
        """
        pid = self.pid or get_namespace()
        if not pid:
            raise ValueError('pid is required')
        ms = Microsite.get_by_msid(msid=self.msid, pid=pid)
        spid = ms.spid
        accounts = []

        if not spid:
            logging.error('spid missing from microsite entity for pid: "%s"; msid: "%s"', pid, self.msid)
            return accounts

        profile = self.profile_manager.getProfile(spid)

        if profile.get(CS_KEY.SOCIAL_PROFILE_SERVICES):
            fb_users = profile[CS_KEY.SOCIAL_PROFILE_SERVICES].get(CS_KEY.FB_USER)
            if fb_users:  # spid (get from facebook and from association table)
                for fb_user in fb_users:
                    account = {}
                    try:
                        fb_pages_info = self.service_manager.getFacebookPagesForUser(
                            spid, fb_user[CS_KEY.SOCIAL_SERVICE_FACEBOOK_USER_ID]
                        )
                    except CSApiException:
                        account['error'] = 'Sorry, an error occurred getting info for this account'
                        logging.exception(
                            'cannot get Facebook Pages from Core Services for FB User "%s"',
                            fb_user[CS_KEY.SOCIAL_SERVICE_FACEBOOK_USER_ID]
                        )
                    else:
                        account = self._get_accounts_info_associate_with_facebook(spid, fb_pages_info, fb_user)
                    accounts.append(account)

            spgid = profile.get(CS_KEY.SOCIAL_PROFILE_GROUP_ID, None)
            if spgid:
                fb_pages = profile[CS_KEY.SOCIAL_PROFILE_SERVICES].get(CS_KEY.FB_PAGE)
                if fb_pages:
                    fb_page_id_to_name_map = {}
                    for fb_page in fb_pages:
                        fb_page_id_to_name_map[fb_page.get('facebookPageId')] = fb_page.get('name')

                    account = self._get_accounts_info_associate_with_facebook(spgid, fb_page_id_to_name_map)
                    accounts.append(account)

        return accounts

    def get_fb_auth_link(self, next_url=None, client_tag=None):
        """
        Gets the facebook authorization link (from memcache if available) for adding a facebook user to a social
        profile associated with a microsite.
        """
        from app.domain import microsite
        pid = self.pid or get_namespace()
        ms = Microsite.get_by_msid(msid=self.msid, pid=pid)
        if not ms.spid:
            ms = microsite.update_microsite_entity(self.msid, pid=pid)
        auth_link = self.service_manager.getFacebookUserAssociationLink(
            spid=ms.spid, nextUrl=next_url, clientTag=client_tag
        )
        return auth_link

    def get_auth_links(self, next_url=None, client_tag=None):
        """
        Returns the authentication links core has for each service that can be authenticated
        """
        auth_links = self.service_manager.getSocialAuthorizationRedirectionLinks(self.spid, next_url, client_tag)
        return auth_links

    def register_site_as_social_service(self):
        """ Registers a site as a social service, on a social profile within Core """
        pid = self.pid or get_namespace()
        if not pid:
            raise ValueError('pid is required')
        try:
            data = self.service_manager.associateMSAccount(self.spid, self.msid, pid, LONG_FORM_POST_CLIENT_TAG)
        except CSApiException:
            logging.exception("Failed to register site as social service (msid, spid)=(%s,%s)", self.msid, self.spid)
            raise

        return data

    def deregister_site_as_social_service(self):
        """
        De-Registers a site as a social service, on a social profile within Core
        Will also remove the 'ms-longFormPost' clientTag in Core
        """
        pid = self.pid or get_namespace()
        if not pid:
            raise ValueError('pid is required')

        try:
            profile = self.profile_manager.getProfile(self.spid)
        except CSApiException as e:
            if e.status != 404:
                raise
            profile = None

        if not profile:
            raise SocialProfileNotFoundException('Social profile was not found for spid: %s', self.spid)
        if CS_KEY.SOCIAL_PROFILE_SERVICES in profile and CS_KEY.MS_PAGE in profile[CS_KEY.SOCIAL_PROFILE_SERVICES]:
            for service in profile[CS_KEY.SOCIAL_PROFILE_SERVICES][CS_KEY.MS_PAGE]:
                if service.get(MSID) == self.msid:
                    ssid = service.get(CS_KEY.SOCIAL_SERVICE_ID)
                    self.service_manager.deauthorizeService(self.spid, ssid)
                    self.service_manager.removeTagFromService(self.spid, ssid, clientTag=LONG_FORM_POST_CLIENT_TAG)

    @require_args
    def fetch_facebook_page_infos(self, fb_user_id):
        """ Fetch facebook page info for all the pages the given user has access to. """
        try:
            pages = self.service_manager.getFacebookPagesForUser(self.spid, fb_user_id)
        except CSApiException:
            logging.exception(
                'Failed to get Facebook pages for user (%s) to social profile (%s)', fb_user_id, self.spid
            )
            raise

        return pages or {}

    def fetch_paged_facebook_page_infos(self, spid, fb_user_id, cursor=None, page_size=None, search=''):
        """ Fetch facebook page info using a paged endpoint. """
        manager = FacebookPageClient(settings.VSOCIAL_USER, settings.VSOCIAL_API)

        if not search:
            return manager.getFacebookPagesForUserPagedV1(spid=spid, facebookUserId=fb_user_id, cursor=cursor,
                                                          pageSize=page_size)

        result_pages = {
            'pages': [],
            'cursor': cursor
        }
        while True:
            pages = manager.getFacebookPagesForUserPagedV1(facebookUserId=fb_user_id,
                                                           spid=spid,
                                                           cursor=result_pages['cursor'],
                                                           pageSize=page_size)
            for page in pages['pages']:
                if search.lower() in page['name'].lower():
                    result_pages['pages'].append(page)

            if pages['cursor'] == None:
                result_pages['cursor'] = None
                break
            else:
                result_pages['cursor'] = pages['cursor']
        return result_pages

    def add_facebook_pages_by_id(self, fb_user_id, fb_page_id):
        """ Add facebook page to the social profile with MS client tag. """
        try:
            client = FacebookPageClient(settings.VSOCIAL_USER, settings.VSOCIAL_API)
            result = client.createFacebookPageServiceV1(
                clientTag=MS_CLIENT_TAG, facebookPageId=fb_page_id, spid=self.spid, facebookUserId=fb_user_id
            )
        except ApiException:
            logging.exception('The Facebook Page (%s) belonging to Facebook User (%s) was not added to Social'
                              ' Profile (%s)', fb_page_id, fb_user_id, self.spid)
            raise

        return result

    @require_args
    def get_service(self, service_id):
        """
        :param service_id: Original service identifier
        :param spid: Social profile identifier
        :return: Social service info if exists else None
        """
        match_criteria = {
            CS_KEY.SERVICE_ID: service_id
        }
        return self._get_service(**match_criteria)

    def remove_service_from_social_profile(self, ssid, client_tags):
        """
        Removes the service from the social profile by removing all client tags.
        :param ssid: Identifies which service to remove client tag from.
        :return: The Social Profile.
        """
        if not isinstance(client_tags, list):
            client_tags = [client_tags]
        for tag in client_tags:
            if tag in ['SM', 'MS', 'SR_SOCIAL_DASHBOARD']:
                self.service_manager.removeTagFromService(self.spid, ssid, clientTag=tag)

    def remove_site_url_from_facebook_about_page(self, fb_page_id):
        """Remove the MS site URL from the Website URLs section of the Facebook About page."""
        pid = self.pid or get_namespace()

        fb_page = self.get_facebook_page_information(fb_page_id).get(fb_page_id)

        if fb_page:
            fb_site_url = fb_page.get(CS_KEY.SOCIAL_SERVICE_FB_WEBSITE, "")

            mapping = lookup_hostslug_msid_mapping_for_msid(pid, self.msid)[0]
            new_url = self._remove_url_from_existing_url(fb_site_url, mapping.hostslug)
            self.update_facebook_about_page(fb_page_id, site_url=new_url)

    def create_social_profile(self, spgid=None):
        """
        creates a social profile in Core
        :return: newly created social profile id
        """
        # msid and spid have a 1 to 1 link so there should not already be an spid if a social profile needs to be made
        if self._get_spid_for_site():
            raise SiteSpidExistsException(self.msid, self.spid)

        data = self.profile_manager.createSocialProfile(accountId=self.msid, spgid=spgid)
        spid = data.get(CS_KEY.SOCIAL_PROFILE_ID)
        return spid


def get_pid_msid_from_fb_page_id(fb_page_id):
    """Returns pid,msid given a facebook page"""
    if not fb_page_id:
        raise ValueError("fb_page_id is required")
    key = MicrositeFBPageAssociation.build_key(fb_page_id=fb_page_id)
    association = key.get()
    if not association:
        return None, None
    return association.pid, association.msid


def add_site_url_to_facebook_about_page(msid, fb_page_id, pid=None):
    """Add the MS site URL to the Website URLs section of the Facebook About page."""
    social = SitesVSocial(msid)
    pid = pid or get_namespace()
    fb_page = social.get_facebook_page_information(fb_page_id).get(fb_page_id)
    if fb_page:
        fb_site_url = fb_page.get(CS_KEY.SOCIAL_SERVICE_FB_WEBSITE, "")
        mapping = lookup_hostslug_msid_mapping_for_msid(pid, msid)[0]
        new_url = social.add_url_to_existing_url(fb_site_url, mapping.hostslug)
        try:
            social.update_facebook_about_page(fb_page_id, site_url=new_url)
        except CSApiException as e:
            logging.exception(e.args[0])


class SocialSync:
    """
    Module for social sync activity.
    """
    MSID = MSID
    NAME = 'name'
    CONNECT_LINK = 'connect_link'
    UPDATE_SYNC_LINK = 'update_sync_link'
    DISPLAY_NAME = 'display_name'
    LOCATION_DISPLAY_NAME = 'displayName'
    TYPE = 'type'
    DISCONNECT_SOCIAL_SERVICE_LINK = 'disconnect_social_service_link'
    OPEN_ACTIVATE_DIALOG = 'open_activate_dialog'
    SYNC = 'sync'
    ACCOUNTS = 'accounts'
    ACCOUNT_MANAGEMENT_LINK = 'account_management_link'
    SOCIAL_PROFILES = 'social_profiles'
    SERVICE = 'service'
    SYNCING_SSID = 'syncing_ssid'
    SELECTED = 'selected'
    SERVICE_USER_ID = 'service_user_id'
    NEXT_URL = 'next_url'
    MESSAGE_SYNC_UPDATE_SUCCESS = 'Service syncing updated successfully'
    MESSAGE_SOCIAL_SERVICE_DISCONNECT_SUCCESS = 'Service disconnected successfully'
    SSID = 'ssid'
    PRODUCT_NAMES = 'product_names'
    SYNC_DIALOG_SERVICE_ID = 'sync_dialog_service_id'
    FS_USER = 'FS_USER'
    GOOGLE_MY_BUSINESS_DISPLAY_NAME = 'Google My Business'
    FACEBOOK_DISPLAY_NAME = 'Facebook'
    TWITTER_DISPLAY_NAME = 'Twitter'
    INSTAGRAM_DISPLAY_NAME = 'Instagram'
    LOCATION = 'location'
    LOCATION_PATH_NAME = 'locationPathName'
    GOOGLE_USER_ID = 'google_user_id'
    GOOGLE_PLUS_USER_ID = 'googlePlusUserId'
    SYNC_SETTING_FLAG = 'syncSettingFlag'
    SECONDARY_SYNCING_SERVICES = 'secondarySyncingServices'

    SYNCING_SERVICES = [
        {
            NAME: CS_KEY.GOOGLE_PLUS,
            TYPE: CS_KEY.GP_USER,
            DISPLAY_NAME: GOOGLE_MY_BUSINESS_DISPLAY_NAME,
        },
        {
            NAME: CS_KEY.FACEBOOK,
            TYPE: CS_KEY.FB_PAGE,
            DISPLAY_NAME: FACEBOOK_DISPLAY_NAME,
            SECONDARY_SYNCING_SERVICES: [INSTAGRAM_DISPLAY_NAME],
        },
        {
            NAME: CS_KEY.TWITTER,
            TYPE: CS_KEY.TW_USER,
            DISPLAY_NAME: TWITTER_DISPLAY_NAME,
        },
    ]

    @require_args
    def __init__(self, msid, account_group_id, sync_dialog_service_id=None):
        """
        Init
        """
        self.msid = msid
        self.account_group_id = account_group_id
        self.sync_dialog_service_id = sync_dialog_service_id
        self.vsocial = SitesVSocial(self.msid)
        self._social_profile = None

    @property
    def social_profile(self):
        """
        Cache the social profile fetched from Core.
        """
        if not self._social_profile:
            self._social_profile = self.vsocial.lookup_social_profile()
        return self._social_profile

    @require_args
    def _get_default_sync_settings(self, name, service_type, auth_link):
        """
        Gets the syncing settings for a service with no accounts
        """
        name = name.capitalize()
        disconnect_link = uri_for('disconnect-social-service', msid=self.msid)
        if service_type is CS_KEY.GP_USER:
            disconnect_link =  uri_for('disconnect-gmb-location')
            name = 'Google My Business'

        return {
            self.NAME: name,
            self.CONNECT_LINK: auth_link,
            self.UPDATE_SYNC_LINK: uri_for(
                'profile-sync', account_group_id=self.account_group_id, service=service_type
            ),
            self.DISCONNECT_SOCIAL_SERVICE_LINK: disconnect_link,
            self.OPEN_ACTIVATE_DIALOG: False,
            self.SYNC: False,
            self.ACCOUNTS: [],
            self.SELECTED: None,
        }

    @require_arg_list(['name', 'service_type', 'auth_link'])
    def _get_sync_settings(self,
                           name,
                           service_type,
                           auth_link,
                           accounts,
                           sync_dialog_service_id=None):
        """
        Gets the settings for the given service type. If there are no accounts, then default sync settings are used
        """
        if not accounts and not isinstance(accounts, list):
            raise ValueError('accounts must be a list.')

        sync_settings = self._get_default_sync_settings(name, service_type, auth_link)
        if name is CS_KEY.GOOGLE_PLUS:
            accounts = self.convert_google_plus_account_to_google_my_business_location()
        # Update sync settings using any service data we got from core
        sync_settings[self.ACCOUNTS] = accounts
        for account in accounts:
            if sync_dialog_service_id and account[CS_KEY.SOCIAL_SERVICE_ID] == sync_dialog_service_id:
                sync_settings[self.SYNC] = False
                sync_settings[self.OPEN_ACTIVATE_DIALOG] = True
                sync_settings[self.SELECTED] = account[CS_KEY.SOCIAL_SERVICE_ID]
                self.set_sync_flag(service_type)
            elif account[CS_KEY.SOCIAL_SYNC]:
                sync_settings[self.SYNC] = True
                sync_settings[self.SELECTED] = account[CS_KEY.SOCIAL_SERVICE_ID]

        return sync_settings

    def convert_google_plus_account_to_google_my_business_location(self):
        """ Creates a social service account as a Google My Business location """
        accounts = []
        google_my_business_sync = GoogleMyBusinessSync(self.vsocial.spid)
        gmb_location = google_my_business_sync.get_connected_location()
        if gmb_location:
            location_path_name = gmb_location.get(self.LOCATION_PATH_NAME)
            location_id = location_path_name.split('/')[3]
            gmb_url = GoogleMyBusinessSync.LOCATION_URL_TEMPLATE.format(location_id)
            account = {
                self.NAME: gmb_location.get(self.LOCATION_DISPLAY_NAME),
                CS_KEY.SOCIAL_SYNC: gmb_location.get(self.SYNC_SETTING_FLAG),
                self.SSID: gmb_location.get(self.LOCATION_PATH_NAME),
                self.SERVICE_USER_ID: gmb_location.get(self.GOOGLE_PLUS_USER_ID),
                CS_KEY.SOCIAL_SERVICE_PROFILE_URL: gmb_url
            }
            accounts.append(account)

        return accounts

    @classmethod
    def _get_syncable_services_from_social_profile(cls, social_profile):
        """
        Gets data for services that can be synced
        """
        all_services = social_profile.get(CS_KEY.SOCIAL_PROFILE_SERVICES, {})
        syncable_service_types = [service.get(cls.TYPE) for service in cls.SYNCING_SERVICES]
        syncable_services = {}
        for service_type, accounts in all_services.items():
            if service_type in syncable_service_types:
                for account in accounts:
                    if cls._is_account_syncable(account):
                        if syncable_services.get(service_type):
                            syncable_services[service_type].append(account)
                        else:
                            syncable_services[service_type] = [account]
        return syncable_services

    @classmethod
    def _is_account_syncable(cls, account):
        """
        determines if a social account can be sync'd or not
        """
        authenticated = account.get(CS_KEY.SOCIAL_SERVICE_IS_AUTHENTICATED, False)
        disabled = account.get(CS_KEY.SOCIAL_SERVICE_IS_DISABLED, True)
        token_broken = account.get(CS_KEY.SOCIAL_TOKEN_BROKEN, True)
        relevant_tags = ['MS', 'SM', 'SR_SOCIAL_DASHBOARD']
        ms_account = any(tag in relevant_tags for tag in account.get('clientTags', []))

        return authenticated and ms_account and not disabled and not token_broken

    def set_sync_flag(self, service_type, syncing_ssid=None):
        """
        Set/unset social sync flag
        """
        # Clear sync flags

        if service_type == CS_KEY.GP_USER:
            google_my_business_sync = GoogleMyBusinessSync(self.vsocial.spid)
            google_my_business_sync.set_sync_flag(syncing_ssid)
        else:
            accounts = self.social_profile.get(CS_KEY.SOCIAL_PROFILE_SERVICES, {}).get(service_type, {})
            for account in accounts:
                ssid = account.get(CS_KEY.SOCIAL_SERVICE_ID)
                self.vsocial.set_social_sync(ssid, ssid == syncing_ssid)

    def get_social_sync_profiles(self, origin=None):
        """
        Return services for syncing.
        """
        next_url = uri_for('added-social-user', account_group_id=self.account_group_id, _full=True)
        if origin:
            next_url = next_url + f'?origin={origin}'
        auth_links = self.vsocial.get_auth_links(next_url=next_url, client_tag=MS_CLIENT_TAG)

        syncable_services = self._get_syncable_services_from_social_profile(self.social_profile)
        social_sync_profiles = []
        for service in self.SYNCING_SERVICES:
            service_name = service.get(self.NAME)
            service_type = service.get(self.TYPE)
            social_sync_profile = self._get_sync_settings(service_name,
                                                          service_type,
                                                          auth_links.get(service_name),
                                                          syncable_services.get(service_type, []),
                                                          sync_dialog_service_id=self.sync_dialog_service_id)
            social_sync_profiles.append(social_sync_profile)

        return social_sync_profiles

    def get_gmb_sync_profile(self, origin=None):
        """
        Return the Google My Business service.
        """
        social_sync_profiles = self.get_social_sync_profiles(origin=origin)
        for profile in social_sync_profiles:
            if profile['name'] == self.GOOGLE_MY_BUSINESS_DISPLAY_NAME:
                return profile

    def get_number_synced_profiles(self):
        """
        Returns the number of social profiles synced.
        """
        num_synced_profiles = 0
        for profile in self.get_social_sync_profiles():
            if profile[self.SYNC]:
                num_synced_profiles += 1
                service = next((s for s in self.SYNCING_SERVICES if s.get(self.DISPLAY_NAME) == profile[self.NAME]), {})
                num_synced_profiles += len(service.get(self.SECONDARY_SYNCING_SERVICES, []))
        return num_synced_profiles


