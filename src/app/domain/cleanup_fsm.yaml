# Machines to clean-up domain objects and entities

state_machines:

  - name: RemoveMicrosite
    namespace: app.domain.cleanup
    queue: remove-microsite-fsm
    context_types:
      microsite_key: google.appengine.ext.ndb.Key
      skip_tombstone: bool
      soft_delete: bool

    states:
      - name: RemoveSiteFBPages
        action: RemoveSiteFacebookPages
        initial: True
        final: True
        transitions:
          - event: process-fbassociations
            to: RemoveMsFBAssociation
            queue: remove-ms-fb-page-association


      - name: RemoveMsFBAssociation
        action: RemoveMicrositeFBPageAssociation
        final: True
        transitions:
          - event: process-socialservice
            to: RemoveSocialService
            queue: remove-site-as-social-service

      - name: RemoveSocialService
        action: RemoveSiteAsSocialService
        final: True
        transitions:
          - event: dereg-agid
            to: DeregisterFromAccountGroup
            queue: deregister-from-account-group

      - name: DeregisterFromAccountGroup
        action: DeregisterFromAccountGroup
        final: True
        transitions:
          - event: dereg-spid
            to: DeregisterSocialProfile
            queue: deregister-social-profile

      - name: DeregisterSocialProfile
        action: DeregisterSocialProfile
        final: True
        transitions:
          - event: process-removems
            to: RemoveMicrosite
            queue: remove-microsite

      - name: RemoveMicrosite
        action: RemoveMicrosite
        final: True
        transitions:
          - event: process-hostslugmsidmappings
            to: RemoveHostSlugMsidMappings
            queue: remove-host-slug-msid-mappings

      - name: RemoveHostSlugMsidMappings
        action: RemoveHostSlugMsidMappings
        final: True
        transitions:
          - event: process-navigation
            to: RemoveNavigation
            queue: remove-navigation

      - name: RemoveNavigation
        action: RemoveNavigation
        final: True
        transitions:
          - event: process-msidblobmappings
            to: RemoveMsidBlobMappings
            queue: remove-msid-blob-mappings

      - name: RemoveMsidBlobMappings
        action: RemoveMsidBlobMappings
        final: True
        transitions:
          - event: process-sitefromfacebook
            to: TemporaryState
            queue: temporary

      - name: TemporaryState
        action: TemporaryState
        final: True


  - name: RemoveBlob
    namespace: app.domain.cleanup
    queue: remove-blob-fsm

    states:
      - name: RemoveBlob
        action: RemoveBlob
        initial: True
        final: True

  - name: RemovePage
    namespace: app.domain.cleanup
    queue: remove-page-fsm
    context_types:
      page_key: google.appengine.ext.ndb.Key

    states:
      - name: RemovePage
        action: RemovePage
        initial: True
        final: True
        transitions:
          - event: process-images
            to: RemovePageidBlobMapping
            queue: remove-page-id-blob-mapping

      - name: RemovePageidBlobMapping
        action: RemovePageidBlobMapping
        final: True

  - name: RemoveFullPartner
    namespace: app.domain.cleanup
    queue: remove-full-partner-fsm

    states:
      - name: RemoveAnalyticsReports
        action: RemoveAnalyticsReports
        initial: True
        final: False
        transitions:
          - event: process-all-microsites
            to: RemoveAllMicrosites
            queue: remove-all-microsites

      - name: RemoveAllMicrosites
        action: RemoveAllMicrosites
        initial: False
        final: False
        transitions:
          - event: remove-partner
            to: RemovePartner
            queue: remove-partner

      - name: RemovePartner
        action: RemovePartner
        initial: False
        final: True
