""" Functions and classes related to microsite posts. """
from vautil import tinyid
from datetime import datetime

from google.appengine.ext import ndb
from google.appengine.api.namespace_manager.namespace_manager import get_namespace

from app.models.post import Post
from app.domain import exceptions
from app.domain.permalink import lookup_permalink_post_mapping, create_permalink_post_mapping, \
        lookup_permalinks_with_post_id
from app.constants import Url<PERSON>eys, MAX_POST_TITLE_LENGTH
from app.domain.constants import post_permalink_mapping_expire_signal


def generate_post_id():
    """ generate a msid """
    return 'PS-' + tinyid.TinyIDGenerator(namespace='PS', tinyid_len=12).generate_tinyid().upper()


def compute_post_slug(publish_datetime, title):
    """ Returns a post slug given a datetime and title """
    urlize_publish_date = publish_datetime.strftime(UrlKeys.DATE_URL_FORMAT)
    urlize_title = ''.join(c for c in title.lower().replace(' ', '-') if c.isalnum() or c == '-')
    return "{}/{}".format(urlize_publish_date, urlize_title)

def truncate_string_leaving_suffix(string, suffix, max_length):
    """ truncate down to max post length characters """
    overflow = len(string) + len(suffix) - max_length

    if overflow > 0:
        string = "{}{}".format(string[:-overflow], suffix)
    return string

def generate_unique_post_slug(publish_datetime, title, msid, pid=None, max_title_length=MAX_POST_TITLE_LENGTH):
    """ generate a unique post slug for this post"""

    if not isinstance(publish_datetime, datetime):
        raise ValueError('publish_datetime must be a datetime')

    pid = pid or get_namespace()
    if not pid:
        raise ValueError('pid is required')

    base_post_slug = compute_post_slug(publish_datetime, title)

    post_suffix = ""
    post_slug = "{}{}".format(base_post_slug, post_suffix)
    i = 1
    while True:
        post_slug = truncate_string_leaving_suffix(post_slug, post_suffix, max_title_length)

        if not lookup_post_by_post_slug(post_slug, msid, pid=pid):
            # If we have no match we are unique
            break

        post_suffix = "-%d" % i
        post_slug = "{}{}".format(base_post_slug, post_suffix)
        i += 1
 
    return post_slug


def get_image_content(serving_url, width=None, height=None):
    """ Return the html to create an image post with. """
    height = height or 'auto'
    width = width or '100%'
    return '<img id="image-post-image" src="{}" style="width:{}; height:{}; ' \
           'max-width: 100%; margin:0 auto; display: block;" />'.format(serving_url, width, height)


def create_post(msid, title, content, publish_datetime,
                pid=None, max_title_length=MAX_POST_TITLE_LENGTH, hidden=False, put=True):
    """ 
    Creates a new post entity. Returns a dictionary of post data of the new page.
    """
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Posts cannot be created in the default namespace.')
    if not isinstance(publish_datetime, datetime):
        raise ValueError('publish_datetime must be a datetime')

    post_id = generate_post_id()

    if isinstance(title, str):
        title = title.strip()

    slug_data = title or post_id
    post_slug = generate_unique_post_slug(publish_datetime, slug_data, msid, pid=pid, max_title_length=max_title_length)

    ppm = create_permalink_post_mapping(msid, post_id, publish_datetime, post_slug, pid=pid)

    key = Post.build_key(post_id=post_id, pid=pid)
    post = Post(key=key, post_id=post_id, msid=msid, pid=pid, title=title, content=content, 
                publish_datetime=publish_datetime, post_slug=post_slug, permalink=ppm.permalink, hidden=hidden)

    prev_post = lookup_prev_post(publish_datetime, msid, pid=pid)

    if prev_post and prev_post.next_post_id:
        next_post = Post.build_key(post_id=prev_post.next_post_id, pid=pid).get()
    else:
        next_post = lookup_next_post(publish_datetime, msid, pid=pid)

    entities_to_put = [post]
    if prev_post:
        prev_post.next_post_id = post.post_id
        post.prev_post_id = prev_post.post_id
        entities_to_put.append(prev_post)

    if next_post:
        next_post.prev_post_id = post.post_id
        post.next_post_id = next_post.post_id
        entities_to_put.append(next_post)

    if put:
        ndb.put_multi(entities_to_put)

    return post

def update_post(post_id, msid, pid=None, **kwargs):
    """ Updates an existing post entity. """

    if not post_id:
        raise ValueError('post_id is required.')
    if not msid:
        raise ValueError('msid is required.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Post must be updated in a pid context.')

    key = Post.build_key(post_id=post_id, msid=msid, pid=pid)
    post = key.get()
    if not post:
        raise exceptions.PostNotFoundException()

    for k, v in kwargs.items():
        setattr(post, k, v)

    post.put()


def lookup_post(post_id, msid, pid=None):
    """ 
    Gets a post by id. 
    """
    if not post_id:
        raise ValueError('post_id is required.')
    if not msid:
        raise ValueError('msid is required.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Cannot retrieve posts from the default namespace.')

    return Post.build_key(post_id=post_id, msid=msid, pid=pid).get()


def lookup_prev_post(publish_datetime, msid, pid=None):
    """ Returns the previous post in datetime order """
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('pid is required')

    return Post.query(namespace=pid.upper())\
        .filter(Post.msid == msid.upper())\
        .filter(Post.publish_datetime < publish_datetime)\
        .order(-Post.publish_datetime).get()


def lookup_next_post(publish_datetime, msid, pid=None):
    """ Returns the next post in datetime order """
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('pid is required')

    return Post.query(namespace=pid.upper())\
        .filter(Post.msid == msid.upper())\
        .filter(Post.publish_datetime > publish_datetime)\
        .order(Post.publish_datetime).get()


def lookup_post_by_post_slug(post_slug, msid, pid=None):
    """ 
    Gets a post by id. 
    """
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Cannot retrieve posts from the default namespace.')

    return Post.query(namespace=pid)\
            .filter(Post.msid == msid)\
            .filter(Post.post_slug == post_slug).get()


def lookup_post_by_permalink(permalink):
    """ returns a post that has matching permalink """
    if not permalink:
        raise ValueError("permalink is required")
    ppm = lookup_permalink_post_mapping(permalink)
    if not ppm:
        raise ValueError('no permalink post mapping found for (%s)' % permalink)

    post = lookup_post(ppm.post_id, ppm.msid, pid=ppm.pid)

    return post


def delete_post(post_id, msid, pid=None):
    """ 
    Deletes a post entity.
    """
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Posts cannot be deleted from the default namespace.')

    post = lookup_post(post_id=post_id, msid=msid, pid=pid)
    if not post:
        raise ValueError('No post with that ID exists.')

    prev_post = Post.build_key(post_id=post.prev_post_id, msid=msid, pid=pid).get() if post.prev_post_id else None
    next_post = Post.build_key(post_id=post.next_post_id, msid=msid, pid=pid).get() if post.next_post_id else None

    entities_to_put = []
    if prev_post:
        prev_post.next_post_id = post.next_post_id
        entities_to_put.append(prev_post)
    if next_post:
        next_post.prev_post_id = post.prev_post_id
        entities_to_put.append(next_post)

    ndb.put_multi(entities_to_put)
    post.key.delete()
    permalinks = lookup_permalinks_with_post_id(post_id, msid, pid, keys_only=True)
    ndb.delete_multi(permalinks)

    post_permalink_mapping_expire_signal.send('{}:{}:{}'.format(post_id, msid, pid))

   
