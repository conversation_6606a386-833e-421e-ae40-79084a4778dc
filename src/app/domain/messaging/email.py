""" Sends emails """

import logging
from google.appengine.api import mail
from google.appengine.api.mail_errors import InvalidSenderError
from google.appengine.ext.deferred import PermanentTaskFailure

from app.models.partner import Partner
from settings import SENDER_EMAIL

def send_email(pid, to, subject, body, mailer=None, **kwargs):
    """
    Ensure provided parameters are valid and then sends an email

    @raises ValueError if arguments are not provided or are invalid
    """
    if not pid:
        raise ValueError('pid is required.')
    pid = pid.upper()

    if not to or not mail.is_email_valid(to):
        error_msg = "Invalid to email address %s. Message will not be sent." % to
        logging.error(error_msg)
        raise ValueError(error_msg)

    if not body:
        error_msg = "Invalid email message. Message will not be sent."
        logging.error(error_msg)
        raise ValueError(error_msg)

    if not subject:
        error_msg = "Invalid email subject. Message will not be sent."
        logging.error(error_msg)
        raise ValueError(error_msg)

    if mailer is None:
        mailer = mail

    # use the pid to determine the correct from address
    from_ = SENDER_EMAIL
    p = Partner.get(pid)
    if p and p.contact_email_from:
        from_ = '"{}" <{}>'.format(p.contact_email_from, SENDER_EMAIL)

    log_email_fields(pid, from_, to, subject, body, **kwargs)

    try:
        send_mail = mailer.send_mail(from_, to, subject, body, **kwargs)
    except InvalidSenderError:
        raise PermanentTaskFailure("Tried sending an email and got an InvalidSenderError.")

    # Note: send_mail does not return anything, this is primarily for testing
    return send_mail

def log_email_fields(pid, from_, to, subject, body, **kwargs):
    """ Logs information for each of the send_mail fields. """

    cc = kwargs.get('cc', [])
    if not isinstance(cc, list):
        cc = [cc]
    bcc = kwargs.get('bcc', [])
    if not isinstance(bcc, list):
        bcc = [bcc]
    reply_to = kwargs.get('reply_to', '')
    html = kwargs.get('html', '')
    attachments = kwargs.get('attachments', [])

    logging.debug("""Sending Email:
pid: %s
from: %s
to: %s
cc: %s
bcc %s
reply to: %s
subject: %s
body length: %s
html length: %s
attachments: %s
    
body
%s""", pid, from_, to, ', '.join(cc), ', '.join(bcc),
       reply_to, subject, len(body), len(html), len(attachments), body)
