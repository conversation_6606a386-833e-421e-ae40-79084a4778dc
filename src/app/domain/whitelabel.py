""" Module for storing/accessing whitelabel data in memcache. """
import logging

from aasdk import MarketClient

import settings


def get_markets(ms_pid, include_disabled=False):
    """
    Retrieves a dictionary of markets and their associated data from partner central. Does not include disabled
    markets by default.

    :param ms_pid: partner id to get partner market data from
    :keyword include_disabled: default is False. Set to True to also retrieve disabled markets.
    :return: dictionary of requested markets with the market_id as key and value being all the data for that market
    """
    client = MarketClient(apiUser=settings.AA_API_USER, apiKey=settings.AA_API_KEY, configuration=settings.AA_CONFIG)
    markets = client.marketLookupV2(productPid=ms_pid, includeDisabledFlag=include_disabled, productId='MS')

    if include_disabled:
        return markets
    else:
        return {market: details for market, details in markets.items() if not details.get('disabledDate')}


def market_id_exists(pid, market_id):
    """
    Checks a market id parameter against the market ids that a partner has assigned
    to them.
    :param pid: partner id to get partner market data from
    :param market_id: market id to check against partner's market data
    :return: true if the market id exists in the partner's market data
    """
    market_ids = get_markets(pid)
    logging.info("Checking if marketId: %s is in this pid: %s's marketIds: %s", market_id, pid, market_ids)
    return market_id in market_ids
