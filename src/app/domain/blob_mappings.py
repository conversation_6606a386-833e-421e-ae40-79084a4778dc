""" Functions to lookup blob mappings using mappings. """

import logging
import uuid
from google.appengine.api import images, urlfetch
from google.appengine.ext import blobstore
from google.appengine.api.images import get_serving_url, BadImageError, Image
from google.appengine.ext import ndb
from google.appengine.api.namespace_manager.namespace_manager import get_namespace
from google.appengine.ext.blobstore import BlobInfo
from app.domain import exceptions
from app.domain.validation import require_args
from fantasm.fsm import startStateMachine
from app.models.microsite import Microsite as MicrositeModel
from app.models.blob_mappings import MsidBlobMapping as MsidBlobMappingModel
from app.constants import PR_PHONE, APP_ENGINE_MAX, DESKTOP_BACKGROUND, MOBILE_BACKGROUND, DIMENSION_LOGO, \
    DIMENSION_SHORTCUT, DIMENSION_DESKTOP_BG, DIMENSION_MOBILE_BG
import settings

FAVICON_CONTENT_TYPES = ["image/x-icon", "image/vnd.microsoft.icon"]

class ImageInfo:
    """ small helper struct used by get_image_info """
    def __init__(self, width, height, url):
        self.width = width
        self.height = height
        self.url = url

def get_resized_image_url(mapping, max_width=None, max_height=None):
    """ Returns the mapping serving url with resized image args.

    @returns image url, or None if a height/width cannot be determined (the latter can happen if the
             image is still being imported into the system)
    """
    if not mapping:
        return None
    max_width = max_width or mapping.width
    max_height = max_height or mapping.height
    if mapping.serving_url and mapping.width and mapping.height:
        resize_args = '=s%d' % calculate_resize(max_width, max_height, mapping.width, mapping.height)
        return '{}{}'.format(mapping.serving_url, resize_args)
    else:
        return None

def calculate_resize(target_width, target_height, source_width, source_height):
    """ calculates how a source rectangle of a certain width and height has to be resized to fit into a target
     rectangle returns the resized length of the longer side of the source rectangle"""

    wscale = float(target_width) / source_width
    hscale = float(target_height) /source_height
    scale = min([wscale, hscale])
    if scale > 1:
        logging.debug("Scaling up should be avoided resetting scale of %s to 1", scale )
        scale = 1
    else:
        logging.debug("Scaling to %s", scale)
    val = max([round(source_width * scale), round(source_height * scale)])
    if val > APP_ENGINE_MAX:
        val = APP_ENGINE_MAX
    return val

def touch_microsite_entity(msid, pid=None):
    """ Update the microsite's updated date to invalidate ms.css caching.
        This would probably be more suitable in domain.microsites however doing so would cause a circular import.
    """
    if not msid:
        raise ValueError('msid is required.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Microsite must be touched in a pid context (either from the namespace manager, or '
                         'an explicit pid kwarg).')

    ms = MicrositeModel.get_by_msid(msid, pid=pid)
    if not ms:
        raise exceptions.MicrositeNotFoundException()
    ms.put()

def get_all_blob_mappings_for_msid(msid, pid=None):
    """ Returns a list of the MsidBlobMappings for the given msid. """
    logging.debug('Getting all Blobmappings')
    theKeys = MsidBlobMappingModel.build_predictive_keys(msid, pid=pid)
    entities = ndb.get_multi(theKeys)
    return [MsidBlobMapping.from_model(entity) for entity in entities if entity]

def lookup_blobkeys_for_msid(msid):
    """ Returns a dictionary of the categories and blobkeys in a microsite. Max 1000 """
    result = MsidBlobMappingModel.lookup_by_msid(msid, count=1000)
    blobkeys = {m.category: BlobInfo(m.blobkey) for m in result.results}
    return blobkeys

def get_blobkey_for_msid_and_category(msid, category):
    """ Returns the BlobInfo Object stored with this msid and category """
    if not msid:
        raise ValueError('msid is required.')
    if not category:
        raise ValueError('category is required.')
    ms = MsidBlobMappingModel.build_key(msid, category).get()
    if not ms:
        return None
    return BlobInfo(ms.blobkey)

def get_msid_blob_mapping(msid, category, pid=None):
    """ Gets an MsidBlobMapping """
    key = MsidBlobMappingModel.build_key(msid, category, pid=pid)
    entity = key.get()
    return MsidBlobMapping.from_model(entity)

def get_image(blobinfo, limit=50000):
    """ Used to retrieve the header information of an image as described here:
        http://groups.google.com/group/google-appengine-python/browse_thread/thread/6af520f800c76a72
    """
    image_data = blobstore.fetch_data(blobinfo.key(), 0, limit)
    return images.Image(image_data=image_data)

def get_image_info(blobinfo):
    """ Returns the image information for use by the blobmapping
        all image types return full absolute urls except favicon. For favicon you just get the blob_key back
        and have to serve it with a Blobstore download handler.
    """
    try:
        img = get_image(blobinfo)

        logging.debug("Blobinfo is:" + str(blobinfo))
        logging.debug("Image filename: %s", blobinfo.filename)
        logging.debug('Image content type: %s', blobinfo.content_type)
        logging.debug("Image dimensions:   " + str(img.width) + "x"+ str(img.height))
    except BadImageError:
        logging.exception('Retrieved a bad image error, trying to load the complete image ')
        img = get_image(blobinfo, limit=blobinfo.size)
        logging.debug("Blobinfo is:" + str(blobinfo))
        logging.debug("Image filename: %s", blobinfo.filename)
        logging.debug('Image content type: %s', blobinfo.content_type)
        logging.debug("Image dimensions:   " + str(img.width) + "x"+ str(img.height))

    if blobinfo.content_type in FAVICON_CONTENT_TYPES:
        logging.debug("Detected an Icon, has to be served from Blobstore")
        serving_url = str(blobinfo.key()) #favicons can not be served via ImageAPI returning just the blobkey
    else:
        serving_url = get_serving_url(blobinfo)

    return ImageInfo(img.width, img.height, serving_url)

def add_msid_blob_mapping(msid, category, blob_key, width=None, height=None, import_info=None, pid=None,
                          uses_gcs=False):
    """ Adds an MSID to blob mapping to the dataStore.

    @param import_info an optional ImportInfo containing details about an imported image
    """
    if not msid:
        raise ValueError('msid required')
    if not category:
        raise ValueError('category required')
    if not blob_key:
        raise ValueError('blob key required')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('MsidBlobMapping cannot be created in default namespace.')

    pid = pid.upper()
    category = category.lower()
    key = MsidBlobMappingModel.build_key(msid, category, pid=pid)
    delete_blob_by_blob_mapping_key(key)
    kwargs = {
        'key': key,
        'msid': msid,
        'category': category,
        'blobkey': blob_key,
        'serving_url': get_serving_url(blob_key),
        'width': width,
        'height': height,
        'uses_gcs': uses_gcs
    }
    if import_info:
        kwargs['import_url'] = import_info.url
        kwargs['import_last_modified'] = import_info.last_modified
        kwargs['import_etag'] = import_info.etag
    new_bm = MsidBlobMappingModel(**kwargs)
    new_bm.put()

    if category in [DESKTOP_BACKGROUND, MOBILE_BACKGROUND]:
        # invalidate ms.css caching so backgrounds will be updated
        touch_microsite_entity(msid, pid=pid)

    return MsidBlobMapping.from_model(new_bm)

def delete_blob_mapping(msid, category, pid=None):
    """ Deletes a msid blob mapping specified by category """

    if not msid:
        raise ValueError('msid required')
    if not category:
        raise ValueError('category required')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('MsidBlobMapping cannot be deleted in default namespace.')

    key = MsidBlobMappingModel.build_key(msid, category, pid=pid)
    delete_blob_mapping_by_key(key)

def delete_blob_mapping_by_key(key):
    """ Deletes the blob and then the Msid mapping. """
    delete_blob_by_blob_mapping_key(key)
    key.delete()

def delete_blob_by_blob_mapping_key(key):
    """ Deletes the blob that is referenced by the key. """
    if not key:
        raise ValueError('blob_mapping key is required')
    blob_mapping = key.get()
    if blob_mapping and blob_mapping.blobkey:
        blobinfo = BlobInfo(blob_mapping.blobkey)
        bkey = blobinfo.key()
        startStateMachine('RemoveBlob', {'blobkey': bkey})
        logging.debug('Started FSM to delete blob with blobkey "%s".', bkey)
    else :
        logging.debug('No blob mapping found, all good.')

@require_args
def add_image_to_microsite_from_url(msid, pid, image_category, image_url):
    """
    Fetches the url at image_url. Uploads it to a blob and maps the blob to the msid using the category.
    image_category must be one of models.blob_mappings.CATEGORIES.keys()
    """
    response = urlfetch.fetch(image_url)
    if response.status_code != 200:
        raise exceptions.InvalidImageUrlException(image_url, response.status_code)
    validate_is_image(response.content)
    width, height = get_image_dimensions(response.content)
    # save image to GCS
    blob_key = write_to_blobstore(pid, msid, response.content)
    # get key for newly saved image
    add_msid_blob_mapping(msid, image_category, blob_key, width, height, pid=pid, uses_gcs=True)

@require_args
def validate_is_image(data):
    """ Uses google.appengine.api.images to validate that the provided data is valid Image data.
    Raises NotImageError if the provided data cannot be parsed as an image. Returns the width otherwise."""
    return Image(data).width # raises NotImageError if the data cannot be parsed as an image


def get_image_dimensions(data):
    """ Uses google.appengine.api.images to validate that the provided data is valid Image data.
    Raises NotImageError if the provided data cannot be parsed as an image. Returns the width otherwise."""
    img = Image(data)
    return img.width, img.height

@require_args
def write_to_blobstore(pid, msid, content):
    """ Writes a file to Google Cloud Storage and returns a BlobKey. """
    file_name = settings.GCS_IMAGE_BUCKET + '/' + pid + '/' + msid + '/' + uuid.uuid4().hex
    with gcs.open(file_name, 'w') as f:
        f.write(content)

    # Blobstore API requires extra /gs to distinguish against blobstore files.
    blobstore_filename = '/gs' + file_name
    return blobstore.BlobKey(blobstore.create_gs_key(blobstore_filename))

class BlobMapping:
    """ Base blob mapping domain object. Contains attributes common to Pid and Msid blob mappings as well as
        methods for retrieving urls for different categories.
    """

    def __init__(self, **kwargs):
        """ Initialize."""
        if not kwargs.get('category'):
            raise ValueError('category is required.')
        if not kwargs.get('blobkey'):
            raise ValueError('blobkey is required.')

        self.category = kwargs['category']
        self.blobkey = kwargs['blobkey']
        self.blobinfo = BlobInfo(self.blobkey)
        self.serving_url = kwargs.get('serving_url')
        if self.serving_url and self.serving_url.startswith('http://'):
            self.serving_url = self.serving_url[5:]

        self.height = kwargs.get('height')
        self.width = kwargs.get('width')
        self.uses_gcs = kwargs.get('uses_gcs', False)

    def get_url(self, **kwargs):
        """ Return the url for this mapping based on its category. """
        return getattr(self, 'get_%s_url' % self.category)(**kwargs)

    def get_logo_url(self, ua_profile=None):
        """ Returns the logo url resize according to the supplied user agent profile (phone by default). """
        max_width = 250 if ua_profile == PR_PHONE else DIMENSION_LOGO[0]
        return get_resized_image_url(self, max_width=max_width, max_height=DIMENSION_LOGO[1])

    def get_favicon_url(self):
        """ Return the url for serving a favicon from blobstore. """
        return uri_for('download-favicon', resource=self.blobkey)

    def get_shortcut_url(self):
        """ Return the shortcut url. """
        return get_resized_image_url(self, max_width=DIMENSION_SHORTCUT[0], max_height=DIMENSION_SHORTCUT[1])

    def get_desktop_background_url(self):
        """ Return the desktop background url. """
        return get_resized_image_url(self, max_width=DIMENSION_DESKTOP_BG[0], max_height=DIMENSION_DESKTOP_BG[1])

    def get_mobile_background_url(self):
        """ Return the mobile background url. """
        return get_resized_image_url(self, max_width=DIMENSION_MOBILE_BG[0], max_height=DIMENSION_MOBILE_BG[1])

class MsidBlobMapping(BlobMapping):
    """ A msid->blob mapping. """

    def __init__(self, **kwargs):
        """ Initialize."""
        if not kwargs.get('msid'):
            raise ValueError('msid is required.')

        super().__init__(**kwargs)
        self.msid = kwargs['msid']

        self.import_info = None
        if kwargs.get('import_url'):
            self.import_info = ImportInfo(kwargs['import_url'], last_modified=kwargs.get('import_last_modified'),
                                          etag=kwargs.get('import_etag'))

    @classmethod
    def from_model(cls, model):
        """ Builds the class from a model. """
        if not model:
            return None
        return cls(**model.to_dict())

class ImportInfo:
    """ Details about an imported image. """
    def __init__(self, url=None, last_modified=None, etag=None, filename=None, md5_hash=None):
        """ Initialize """
        if bool(url) == bool(md5_hash):
            raise ValueError('Must provide one of url or md5_has but not both.')
        self.url = url
        self.last_modified = last_modified
        self.etag = etag
        self.filename = filename
        self.md5_hash = md5_hash
