""" permalink related logic """
from google.appengine.ext import ndb
from google.appengine.api.namespace_manager.namespace_manager import get_namespace

from app.models.post import Post
from app.models.permalink import PermalinkPostMapping
from app.domain.url_mappings import lookup_hostslugs_for_msid

from app.constants import UrlKeys
from app.domain.constants import post_permalink_mapping_expire_signal

def generate_permalink(post_slug, msid, pid):
    """ generate and returns a permalink based on a post slug """
    site_url = lookup_hostslugs_for_msid(pid, msid)[0]
    permalink = site_url + UrlKeys.PERMALINK % post_slug
    if not permalink.startswith('http'):
        permalink = "http://" + permalink

    return permalink

def lookup_permalink_post_mapping(permalink):
    """ returns permalink """
    return PermalinkPostMapping.build_key(permalink=permalink).get()

def lookup_prev_permalink_post_mapping(publish_datetime, msid, pid):
    """ Return the first permalink before the specified date. """
    return PermalinkPostMapping.query(PermalinkPostMapping.pid==pid, namespace='').\
        filter(PermalinkPostMapping.msid==msid).\
        filter(PermalinkPostMapping.publish_datetime < publish_datetime).\
        order(-PermalinkPostMapping.publish_datetime).get()

def lookup_next_permalink_post_mapping(publish_datetime, msid, pid):
    """ Return the first permalinks after the specified date. """
    return PermalinkPostMapping.query(PermalinkPostMapping.pid==pid, namespace='').\
        filter(PermalinkPostMapping.msid==msid).\
        filter(PermalinkPostMapping.publish_datetime > publish_datetime).\
        order(PermalinkPostMapping.publish_datetime).get()

def lookup_permalinks_with_post_id(post_id, msid, pid, keys_only=False):
    """ Returns a list of permalinks associated with the post_id """
    return PermalinkPostMapping.query(PermalinkPostMapping.pid==pid, namespace='').\
        filter(PermalinkPostMapping.msid==msid).\
        filter(PermalinkPostMapping.post_id==post_id).fetch(keys_only=keys_only)

def create_permalink_post_mapping(msid, post_id, publish_datetime, post_slug, pid=None):
    """ create permalink entity and update the pre/next premalink """
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('pid is required')

    permalink = generate_permalink(post_slug, msid, pid)

    ppm_key = PermalinkPostMapping.build_key(permalink=permalink)
    ppm = PermalinkPostMapping(key=ppm_key, permalink=permalink, msid=msid, post_id=post_id, 
            publish_datetime=publish_datetime, pid=pid)

    # Always update an existing post with the latest created permalink
    existing_post = Post.build_key(post_id=post_id, msid=msid, pid=pid).get()
    entities_to_put = [ppm]
    if existing_post:
        existing_post.permalink = ppm.permalink
        entities_to_put.append(existing_post)

    ndb.put_multi(entities_to_put)

    post_permalink_mapping_expire_signal.send('{}:{}:{}'.format(post_id, msid, pid))

    return ppm

