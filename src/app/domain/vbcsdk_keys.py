"""
VAPI keys
"""

__all__ = ['API_KEY']


class API_KEY:
    """
    A set of string constant parameters of the API.
    """
    LOCAL = 'local'
    DEV = 'dev'
    TEST = 'test'
    DEMO = 'demo'
    PROD = 'prod'

    API_USER = 'apiUser'
    API_KEY = 'apiKey'

    CS_API_USER = 'CS'
    CS_API_KEY = 'HORFAXIXGHJ4GFXG8RPS'

    AA_API_USER = 'AA'
    AA_API_KEY = 'CZyd2hExGXegtSljmxhL'

    SM_API_USER = 'SM'
    SM_API_KEY = 'TnYbWcRb3JVwo5TZSjJm'

    RM_API_USER = 'RM'
    RM_API_KEY = 'c29tZSByYW5kb20gdGV4'

    MS_API_USER = 'MS'
    MS_API_KEY = 'dCwgcGxlYXNlIGVuY29kZSE'

    ARM_API_USER = 'ARM'
    ARM_API_KEY = 'OZz78w7vfoPpbYt7kLMdc031XCX91171'

    BS_API_USER = 'BS'
    BS_API_KEY = 'linsSwNr1wyU9A5w4b53asGLqkYj6R5V'

    ST_API_USER = 'ST'
    ST_API_KEY = 'JgLSbQHBFBJ3dKu0BpKuZd2dDSdfPBLe'

    NB_API_USER = 'NB'
    NB_API_KEY = 'TM1NAGPZCZYO8E23C2X5'

    VALID_PRODUCT_CREDENTIALS = [(CS_API_USER, CS_API_KEY), (AA_API_USER, AA_API_KEY), (SM_API_USER, SM_API_KEY),
                                 (RM_API_USER, RM_API_KEY), (MS_API_USER, MS_API_KEY), (ARM_API_USER, ARM_API_KEY),
                                 (BS_API_USER, BS_API_KEY), (ST_API_USER, ST_API_KEY), (NB_API_USER, NB_API_KEY)]

    URL_USER_CREATE = '/api/v2/user/create/'
    URL_USER_GET = '/api/v2/user/get/'
    URL_USER_UPDATE = '/api/v2/user/update/'
    URL_USER_DELETE = '/api/v2/user/delete/'
    URL_USER_LOOKUP_ALL = '/api/v2/user/lookupAll/'
    URL_USER_LOOKUP_MOBILE = '/internalApi/v2/user/lookupMobile/'

    URL_NOTIFICATION_CREATE_DEPRECATED = '/api/v2/notification/create/'
    URL_NOTIFICATION_CREATE = '/internalApi/v2/notification/create/'
    URL_NOTIFICATION_CREATE_INSTANT = '/internalApi/v2/notification/createInstant/'

    URL_ASSOCIATION_GET = '/api/v2/association/get/'

    URL_USER_ASSOCIATION_CREATE = '/api/v2/userAssociation/create/'
    URL_USER_ASSOCIATION_LOOKUP = '/api/v2/userAssociation/lookup/'
    URL_USER_ASSOCIATION_REMOVE = '/api/v2/userAssociation/remove/'

    URL_ACCOUNT_GROUP_ASSOCIATE_USER = '/api/v2/accountGroup/associateUser/'

    URL_PRODUCT_ASSOCIATION_REMOVE = '/api/v2/productAssociation/remove/'

    URL_ACCOUNT_GROUP_CREATE = '/internalApi/v2/accountGroup/create/'
    URL_ACCOUNT_GROUP_DELETE = '/internalApi/v2/accountGroup/delete/'
    URL_ACCOUNT_GROUP_ADD_ACCOUNT = '/internalApi/v2/accountGroup/addAccount/'
    URL_ACCOUNT_GROUP_UPDATE = '/internalApi/v2/accountGroup/update/'
    URL_ACCOUNT_GROUP_GET = '/internalApi/v2/accountGroup/get/'
    URL_ACCOUNT_GROUP_REMOVE_ACCOUNT = '/internalApi/v2/accountGroup/removeAccount/'
    URL_ACCOUNT_GROUP_SEARCH = '/internalApi/v2/accountGroup/search/'

    URL_INTERNAL_ACCOUNT_GROUP_ASSOCIATE_USER = '/internalApi/v2/accountGroup/associateUser/'
    URL_INTERNAL_SIDEBAR_GET = '/internalApi/v2/sidebar/get/'
    URL_INTERNAL_LISTING_DISTRIBUTION_PURCHASE = '/internalApi/v2/listingDistribution/purchase/'
    URL_INTERNAL_LISTING_DISTRIBUTION_GET_STATUS = '/internalApi/v2/listingDistribution/getStatus/'

    URL_INTERNAL_USER_CREATE = '/internalApi/v2/user/create/'
    URL_INTERNAL_USER_GET = '/internalApi/v2/user/get/'
    URL_INTERNAL_USER_UPDATE = '/internalApi/v2/user/update/'
    URL_INTERNAL_USER_DELETE = '/internalApi/v2/user/delete/'
    URL_INTERNAL_USER_LOOKUP_ALL = '/internalApi/v2/user/lookupAll/'

    USER_ID = 'userId'
    EMAIL = 'email'
    PASSWORD = 'password'
    CONFIRM_PASSWORD = 'confirmPassword'
    FIRST_NAME = 'firstName'
    LAST_NAME = 'lastName'
    PHONE = 'phone'
    WELCOME_MESSAGE = 'welcomeMessage'
    SEND_WELCOME_EMAIL_FLAG = 'sendWelcomeEmailFlag'

    TEST_DATA = 'testData'
    ASYNC_FLAG = 'asyncFlag'

    PID = 'pid'
    PARTNER_ID = 'partnerId'
    SPID = 'spid'
    SOCIAL_PROFILE_ID = 'socialProfileId'
    NAME = 'name'
    COMPANY_NAME = 'companyName'
    CUSTOMER_IDENTIFIER = 'customerIdentifier'
    CUSTOMER_ID = 'customerId'
    AGID = 'agid'
    ACCOUNT_GROUP_ID = 'accountGroupId'
    CREATED_DATE = 'createdDate'
    UPDATED_DATE = 'updatedDate'
    DELETED_DATE = 'deletedDate'
    CREATED_DATE_TIME = 'createdDateTime'
    UPDATED_DATE_TIME = 'updatedDateTime'
    DELETED_DATE_TIME = 'deletedDateTime'
    ORPHANED = 'orphaned'
    ORPHANED_FLAG = 'orphanedFlag'
    CREATED = 'created'
    UPDATED = 'updated'
    DELETED = 'deleted'
    SEARCH_QUERY = 'query'
    CURSOR = 'cursor'
    NEXT_QUERY_STRING = 'nextQueryString'
    ACCOUNT_ID = 'accountId'
    PRODUCT_ID = 'productId'
    PRODUCT_PID = 'productPid'
    GUID = 'guid'
    NOTIFICATION_TYPE = 'notificationType'
    MESSAGE = 'message'
    NOTIFICATION_ID = 'notificationId'
    ASSOCIATION_ID = 'associationId'
    LABEL = 'label'
    ACCOUNTS = 'accounts'
    PRODUCTS = 'products'
    NOTIFICATION_CHANNEL = 'notificationChannel'
    SHOW_DETAILS_FLAG = 'showDetailsFlag'
    DETAILS = 'details'
    COMPANY_NAME = 'companyName'
    ADDRESS = 'address'
    CITY = 'city'
    ZIP = 'zip'
    COUNTRY = 'country'
    STATE = 'state'
    LATITUDE = 'latitude'
    LONGITUDE = 'longitude'
    WORK_NUMBER = 'workNumber'
    CALL_TRACKING_NUMBER = 'callTrackingNumber'
    TAGS = 'tags'
    TRIAL = 'trial'
    BUSINESS_CATEGORY = 'businessCategory'
    SERVICE = 'service'
    SERVICE_CATEGORIES = 'serviceCategories'
    MARKET_ID = 'marketId'
    WEBSITE = 'website'
    SALES_PERSON_ID = 'salesPersonId'
    VTAX_ID_REQUEST = 'taxId'
    VTAX_ID_RESPONSE = 'taxIds'
    TAXONOMY_ID = 'taxonomyId'
    DATA = 'data'
    DATA_JSON = 'dataJson'
    ACTIVE = 'active'
    FROM_EMAIL = 'fromEmail'
    SENDER_NAME = 'senderName'
    SUBJECT_LINE = 'subjectLine'
    FACEBOOK_URL = 'facebookUrl'
    TWITTER_URL = 'twitterUrl'
    GOOGLEPLUS_URL = 'googleplusUrl'
    LINKEDIN_URL = 'linkedinUrl'
    FOURSQUARE_URL = 'foursquareUrl'
    INFERRED_ATTRIBUTE = 'inferredAttribute'

    SOCIAL_MARKETING = 'SM'
    REPUTATION_MANAGEMENT = 'RM'
    MICROSITES = 'MS'

    MSID = 'msid'
    SMID = 'smid'
    SRID = 'srid'

    PRODUCT_KEY_TO_ACCOUNT_ID_MAP = {
        SOCIAL_MARKETING: SMID,
        REPUTATION_MANAGEMENT: SRID,
        MICROSITES: MSID,
    }

    PHONE_NUMBER = 'phoneNumber'
    TYPE = 'type'
    DIGITS = 'digits'
    WORK_TYPE = 'work'
    OTHER_TYPE = 'other'
    CALL_TRACKING_TYPE = 'callTracking'

    ADMIN_NOTE = 'adminNote'
    AUTO_RENEW_FLAG = "autoRenewFlag"

    NOTIFICATION_CHANNEL_EMAIL = 'email'
    NOTIFICATION_CHANNEL_MOBILE = 'mobile'

    PID_DESCRIPTION = "A unique partner identifier."
    PARTNER_ID_DESCRIPTION = "A unique partner identifier."
    SPID_DESCRIPTION = "A unique social profile identifier"
    AGID_DESCRIPTION = "A unique account group identifier."
    NAME_DESCRIPTION = "A descriptive company name."
    ADDRESS_DESCRIPTION = "The address of the business."
    CITY_DESCRIPTION = "The city of the business."
    ZIP_DESCRIPTION = "The zip/postal code for the address."
    COUNTRY_DESCRIPTION = "The 2-letter country code."
    STATE_DESCRIPTION = "The state code."
    SERVICE_DESCRIPTION = "A list of services (up to 3)."
    WEBSITE_DESCRIPTION = "The website of the business (must start with http / https)."
    SEARCH_QUERY_DESCRIPTION = "A query to filter the search result. i.e. name:abc OR name:plumbing."
    CURSOR_DESCRIPTION = "A cursor to retrieve the next page of results."
    CUSTOMER_IDENTIFIER_DESCRIPTION = "A unique identifier."
    USER_ID_DESCRIPTION = "A unique user id."
    FIRST_NAME_DESCRIPTION = "The users first name."
    LAST_NAME_DESCRIPTION = "The users last name."
    EMAIL_DESCRIPTION = "The users correctly formatted email address."
    PASSWORD_DESCRIPTION = "The users login password."
    CONFIRM_PASSWORD_DESCRIPTION = "The users login password repeated for confirmation."
    PHONE_DESCRIPTION = "A North American phone number. Must be at least 7 digits."
    WELCOME_MESSAGE_DESCRIPTION = "A customized welcome message added to the welcome email for this user."
    SEND_WELCOME_EMAIL_FLAG_DESCRIPTION = "Defaults to True.  Supply as False if you do not want to send the user a \
                                           welcome email."
    ACCOUNT_ID_DESCRIPTION = "A unique account identifier."
    ACCOUNT_GROUP_ID_DESCRIPTION = "A unique account identifier."
    PRODUCT_ID_DESCRIPTION = "A unique product identifier."
    AGID_ADD_DESCRIPTION = "The id of the account group to add the account to."
    PRODUCT_PID_DESCRIPTION = "The partner identifier for this product."
    NOTIFICATION_TYPE_DESCRIPTION = "A classification for this notification."
    MESSAGE_DESCRIPTION = "The content of the notification."
    GUID_DESCRIPTION = "A product unique identifier for the notification."
    FROM_EMAIL_DESCRIPTION = "From email address if notification type is Email"
    SENDER_NAME_DESCRIPTION = "Email sender name if notification type is Email"
    SUBJECT_LINE_DESCRIPTION = "Email Subject Line if notification type is Email"
    DATA_JSON_DESCRIPTION = "An arbitrary JSON object."
    NOTIFICATION_CHANNEL_DESCRIPTION = "The method of delivering a notification eg. Email, Mobile, etc."
    SHOW_DETAILS_FLAG_DESCRIPTION = "A boolean flag for whether to return account details"
    SALES_PERSON_ID_DESCRIPTION = "A unique sales person identifier"
    VTAX_ID_DESCRIPTION = "Up to 3 can be supplied, must comply with vTaxonomy Ids available from Core. "
    MARKET_ID_DESCRIPTION = "A valid %s for this Partner. Must be lowercase letters, numbers or dashes." % MARKET_ID
    PHONE_NUMBER_DESCRIPTION = "A list of dictionaries of phone numbers.  Types consist of %s, %s, and %s" % \
                               (WORK_TYPE, CALL_TRACKING_TYPE, OTHER_TYPE)
    WORK_NUMBER_DESCRIPTION = "A list of work numbers"
    CALL_TRACKING_NUMBER_DESCRIPTION = "A list of call tracking numbers"

    ADMIN_NOTE_DESCRIPTION = "Abritrary metadata"
    AUTO_RENEW_FLAG_DESCRIPTION = "A boolean flag for renewing listing distribution service automatically"

    LATITUDE_DESCRIPTION = "A float on [-90.0, 90.0]. If specified, <code>longitude</code> must also be specified."
    LONGITUDE_DESCRIPTION = "A float on [-180.0, 180.0]. If specified, <code>latitude</code> must also be specified."

    FACEBOOK_URL_DESCRIPTION = 'The fully qualified URL of a Facebook page.'
    TWITTER_URL_DESCRIPTION = 'The fully qualified URL of a Twitter user.'
    GOOGLEPLUS_URL_DESCRIPTION = 'The fully qualified URL of a Google+ page.'
    LINKEDIN_URL_DESCRIPTION = 'The fully qualified URL of a LinkedIn user.'
    FOURSQUARE_URL_DESCRIPTION = 'The fully qualified URL of a FourSquare user.'
    INFERRED_ATTRIBUTE_DESCRIPTION = 'The string name of an account group property that has been inferred.'
