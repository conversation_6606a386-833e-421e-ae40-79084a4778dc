""" Functions to lookup microsites and markets using mappings. """

import logging
from urllib.parse import urlparse

from google.appengine.api import memcache
from google.appengine.ext import ndb


from app.constants import SLUG_MIN_LENGTH, SLUG_MAX_LENGTH, VALID_HOSTNAME, VALID_SLUG, RESERVED_SLUGS
from app.domain import exceptions
from app.domain.constants import host_pid_mapping_expire_signal, host_slug_msid_mapping_expire_signal
from app.domain.validation import require_args
from app.domain.vbc import get_ms_pid
from app.models.market_mapping import MarketMapping
from app.models.url_mappings import HostPidMapping as HostPidMappingModel, \
    HostSlugMsidMapping as HostSlugMsidMappingModel


def count_host_pid_mappings(pid):
    """ Returns a total count for all HostPidMappings """
    return HostPidMappingModel.lookup_by_pid_query(pid).count(limit=None)


def get_host_pid_mapping_for_request(request):
    """ Returns a pid for a given request. """
    if not request:
        raise ValueError('request is required')

    return get_host_pid_mapping(request.host)


@require_args
def get_host_pid_mapping(hostname):
    """ Returns a pid for a given hostname. """
    # strip the port, if any
    hostname = hostname.split(':')[0].lower()
    return HostPidMapping.from_model(HostPidMappingModel.build_key(hostname).get())


def lookup_hostnames_for_pid(pid, uses_slugs=None):
    """ Returns the valid hosts as a string list for a given pid max 100.

    @param uses_slugs if provided, must be a boolean, if not provided, the results will not be filtered on this field
    """
    if not pid:
        raise ValueError('pid is required')
    result = HostPidMappingModel.lookup_by_pid(pid, uses_slugs=uses_slugs, keys_only=True, count=100)
    hostnames = [(key.string_id()) for key in result.results]
    return hostnames


def lookup_host_pid_mappings_for_pid(pid):
    """ Returns all HostPidMappings for a given Pid """
    hpm = HostPidMappingModel.query(HostPidMappingModel.pid == pid).iter()
    return hpm


def lookup_host_pid_mappings(pid, host, uses_slugs):
    """ Looks up host pid mappings that tightly match the criteria, should actually only return 1 item """
    query = HostPidMappingModel.lookup_by_pid_query(pid, uses_slugs=uses_slugs)
    return query.filter(HostPidMappingModel.host == host).fetch()


def add_host_pid_mapping(hostname, pid, uses_slugs=True, is_default=False):
    """ Adds a new hostname->pid mapping. """
    if not hostname:
        raise ValueError('hostname is required.')
    if not pid:
        raise ValueError('pid is required.')

    hostname = hostname.lower()
    pid = pid.upper()
    key = HostPidMappingModel.build_key(hostname)

    @ndb.transactional
    def create_new_host_pid_mapping():
        """ Creates a unique host_pid mapping, raising exception if already exists. """
        existing_hpm = key.get()
        if existing_hpm:
            raise exceptions.HostPidMappingExistsException(existing_hpm.host, existing_hpm.pid)
        hpm = HostPidMappingModel(key=key, host=hostname, pid=pid, uses_slugs=uses_slugs, is_default=is_default)
        hpm.put()

    create_new_host_pid_mapping()


def remove_host_pid_mapping(hostname):
    """ Removes an existing hostname->pid mapping. """
    key = HostPidMappingModel.build_key(hostname)
    key.delete()
    host_pid_mapping_expire_signal.send(hostname)


def remove_host_pid_mapping_direct(host_pid_mapping):
    """ Removed a mapping when the model object is passed in """
    host = host_pid_mapping.host
    host_pid_mapping.key.delete()
    host_pid_mapping_expire_signal.send(host)


class HostPidMapping:
    """ A host->pid mapping. """
    def __init__(self, host, pid, uses_slugs):
        if not host:
            raise ValueError('host is required.')
        if not pid:
            raise ValueError('pid is required.')
        if uses_slugs is None:
            raise ValueError('uses_slugs is required.')
        self.host = host.lower()
        self.pid = pid.upper()
        self.uses_slugs = uses_slugs

    @classmethod
    def from_model(cls, model):
        """ Builds the class from a model. """
        if not model:
            return None
        hpm = cls(model.host, model.pid, model.uses_slugs)
        return hpm


def get_hostslug_msid_mapping(host, slug=None):
    """ Gets a hostslug mapping. """
    if not host:
        raise ValueError('host is required.')
    key = HostSlugMsidMappingModel.build_key(host, slug=slug)
    hsmm = key.get()
    return HostSlugMsidMapping.from_model(hsmm)


def get_hostslug_msid_mapping_multi(host_slug_tuples):
    """
    Returns hostslug mappings for a list of (host, slug) tuples.
    """
    if not isinstance(host_slug_tuples, list):
        raise ValueError('host_slug_tuples must be a list.')
    keys = []
    for host, slug in host_slug_tuples:
        key = HostSlugMsidMappingModel.build_key(host, slug=slug)
        keys.append(key)
    entities = ndb.get_multi(keys)
    return [HostSlugMsidMapping.from_model(entity) for entity in entities]


def split_host_slug(url):
    """
    Returns the host and slug for the url as a tuple (host, slug) where slug may be None.
    :param url: Url from which host and slug are separated
    :return: Host and slug in tuple (host,slug) format
    """
    if not url:
        raise ValueError('url is required.')
    url_parts = urlparse(url)
    host = url_parts.netloc
    path = url_parts.path
    if not host:
        raise ValueError('url "%s" does not have a host (it may not contain "//", which it should).' % url)

    # remove the port number from host, if any (only on localhost)
    host = host.split(':')[0].lower()

    # if it's not a custom domain, we need to get the slug from the front of the path
    slug = None
    if path:
        path_parts = path.split('/')
        slug = path_parts[1].lower() # path is '/something/here', so path_parts[0] is '' (before the first '/')

    return host, slug


def get_host_and_slug_for_url(url, uses_slugs=True):
    """
    Returns the host and slug for the url as a tuple (host, slug) where slug may be None.
    if uses_slugs is True and there is no slug then ValueError is raised

    :param url: Url from which host and slug are separated
    :param uses_slugs: To indicate the given url should always have slug in it or not
    :return: Host and slug in tuple (host,slug) format
    """
    host, slug = split_host_slug(url)
    if uses_slugs and not slug:
        raise ValueError('No slug found on domain that requires slugs.')
    if not uses_slugs and slug:
        slug = None
    return host, slug


def get_hostslug_for_url(url, uses_slugs=True):
    """ Returns the hostslug part of the url. """
    host, slug = get_host_and_slug_for_url(url, uses_slugs=uses_slugs)
    return HostSlugMsidMapping.compose_hostslug(host, slug=slug)


def get_hostslug_msid_mapping_for_request(request, uses_slugs=True):
    """ Retrieves an msid for a given request. """
    if not request:
        raise ValueError('request is required')

    return get_hostslug_msid_mapping_for_url(request.path_url, uses_slugs=uses_slugs)


def get_hostslug_msid_mapping_for_url(url, uses_slugs=True):
    """ Retrieves an msid for a given url. """
    host, slug = get_host_and_slug_for_url(url, uses_slugs=uses_slugs)
    return get_hostslug_msid_mapping(host, slug=slug)


def get_slug_tinyid():
    """
    Returns a tinyid value that can be used on its own as a slug, or appended to any other slug.
    It will be a globally unique slug value.
    """
    return tinyid.TinyIDGenerator(namespace='slug').generate_tinyid()


def get_unique_slug(host, slug, batch_size=10):
    """
    Get unique slug, if the passed slug is unique it is returned as it is otherwise, a dash and a unique number
    while be appended to the slug. e.g. if slug='test' already exists 'test-1' is returned,
    if 'test-1' already exist 'test-2' is returned and so on.
    """
    if not host:
        raise ValueError('host is required.')
    if not slug:
        raise ValueError('slug is required.')

    existing = get_hostslug_msid_mapping(host, slug=slug)
    if not existing:
        return slug

    # now try batches of 10
    batch = 0
    while True:
        # range (1, 11) gives us "-1" through "-10"
        batch_min = (batch_size * batch) + 1
        batch_max = (batch_size * batch) + batch_size + 1
        candidate_slugs = [f'{slug}-{index}' for index in range(batch_min, batch_max)]
        host_slug_tuples = [(host, candidate_slug) for candidate_slug in candidate_slugs]
        existing_entities = get_hostslug_msid_mapping_multi(host_slug_tuples)
        existing = None
        for candidate_slug, existing in zip(candidate_slugs, existing_entities):
            if not existing:
                return candidate_slug
        batch += 1


def lookup_hostslug_msid_mapping_for_pid(pid, batch=1000):
    """ Looks up all the hostslug mappings for a pid. """
    if not pid:
        raise ValueError('pid is required.')

    result = []
    qr = HostSlugMsidMappingModel.lookup_by_pid(pid, count=batch)
    while len(qr.results) == batch:
        result.extend(qr.results)
        qr = HostSlugMsidMappingModel.lookup_by_pid(pid, count=batch, cursor=qr.cursor)
    result.extend(qr.results)

    return [HostSlugMsidMapping.from_model(hsmm) for hsmm in result]

def lookup_hostslug_msid_mappings(pid):
    """ Retruns all hostslug msid mappings for a pid """
    return HostSlugMsidMappingModel.lookup_by_pid_query(pid).iter()


@ndb.tasklet
def lookup_hostslugs_for_msid_async(pid, msid):
    """
    Returns future of up to 100 host slugs for a given pid and msid.
    Msids are globally unique but the mapping datamodel could map one msid to
    multiple partners. It's highly unlikely but possible
    """
    result = yield HostSlugMsidMappingModel.lookup_by_pid_and_msid_async(pid, msid, keys_only=True, count=100)
    urls = [key.string_id() for key in result.results]
    raise ndb.Return(urls)


def lookup_hostslugs_for_msid(pid, msid):
    """ Returns up to 100 host slugs for a given pid and msid.
    Msids are globally unique but the mapping datamodel could map one msid to
    multiple partners. It's highly unlikely but possible """
    return lookup_hostslugs_for_msid_async(pid, msid).get_result()


def get_host_for_next_url_in_connect_link(pid, msid):
    """
    Gets the host specifically for use in the "next url" when connecting a social service.

    There is an implicit redirect in Listing Builder when viewing the account-group app that a host using
    <partner ID>-partner.blablabla will be redirected to <partner ID>.blablabla. This is accounted for by removing
    the `-partner` from the host. The reason we need to do this is because our sso system attaches
    a cookie containing auth information to the outgoing connect request. The domain that is used is the
    current domain the user is on. Without returning a host here that matches the domain that will be in the
    user's browser they will not be able to re-enter Listing Builder after they allow access to their Google account.
    """
    host_slugs = lookup_hostslug_msid_mapping_for_msid(pid=pid, msid=msid)
    
    if len(host_slugs) <= 0:
        return ""

    host = host_slugs[0].hostslug
    if host_slugs[0].redirect_hostslug:
        host = host_slugs[0].redirect_hostslug

    for hs in host_slugs:
        if host == hs.hostslug and hs.redirect_hostslug:
            host = hs.redirect_hostslug

    parts = host.split('.', 1)
    if len(parts) > 1:
        prefix, domain = parts[0], parts[1]
        if "-partner" in prefix:
            prefix = prefix.replace("-partner", "")
        host = prefix + '.' + domain

    parts = host.split('/', 1)
    if len(parts) > 1:
        host = parts[0]

    return host


@ndb.tasklet
def lookup_hostslug_msid_mapping_for_msid_async(pid, msid, count=100):
    """ Returns future of up to 100 HostSlugMsidMapping objects for a given pid and msid. """
    if not pid:
        raise ValueError('pid is required.')
    if not msid:
        raise ValueError('msid is required.')
    qr = yield HostSlugMsidMappingModel.lookup_by_pid_and_msid_async(pid, msid, count=count)
    raise ndb.Return([HostSlugMsidMapping.from_model(e) for e in qr.results])


def lookup_hostslug_msid_mapping_for_msid(pid, msid, count=100):
    """ Returns up to 100 HostSlugMsidMapping objects for a given pid and msid. """
    return lookup_hostslug_msid_mapping_for_msid_async(pid, msid, count=count).get_result()


def add_hostslug_msid_mapping(hostname, slug, pid, msid, transactional=True):
    """ Adds a new mapping for given hostname/slug to pid, msid. """
    if not hostname:
        raise ValueError('hostname is required.')
    if not pid:
        raise ValueError('pid is required.')
    if not msid:
        raise ValueError('msid is required.')
    if not VALID_HOSTNAME.match(hostname):
        raise exceptions.InvalidHostnameException()

    key = HostSlugMsidMappingModel.build_key(hostname, slug=slug)
    hostslug = hostname.lower()
    if slug:
        if not VALID_SLUG.match(slug):
            raise exceptions.InvalidSlugException('Slugs must contain only letters, numbers and dashes ("-"), '
                                                  'and must be %(slugmin)d to %(slugmax)d characters long.' %
                                                  {'slugmin': SLUG_MIN_LENGTH, 'slugmax': SLUG_MAX_LENGTH})
        if slug in RESERVED_SLUGS:
            raise exceptions.InvalidSlugException('The slug "%s" can not be used.' % slug)
        hostslug += '/' + slug.lower()

    def create_new_hostslug_msid_mapping():
        """ Creates a unique hostslug_msid mapping, raising exception if already exists. """
        existing_hsmm = key.get()
        if existing_hsmm and existing_hsmm.redirect_hostslug is None:
            # if there is an existing redirect, it's okay, we'll just overwrite it
            raise exceptions.HostSlugMsidMappingExistsException(hostname, slug, msid)
        hsmm = HostSlugMsidMappingModel(key=key, hostslug=hostslug, pid=pid, msid=msid)
        hsmm.put()

        return hsmm

    if transactional:
        hstmp = ndb.transaction(create_new_hostslug_msid_mapping)
    else:
        hstmp = create_new_hostslug_msid_mapping()

    logging.info("Created a host slug mapping: %s", hstmp)
    return HostSlugMsidMapping.from_model(hstmp)


def update_hostslug_msid_mapping(host, slug, pid, msid):
    """ If slug is new it will replace the old one. """
    if not host:
        raise ValueError('host is required.')
    if not msid:
        raise ValueError('msid is required.')
    if not pid:
        raise ValueError('pid is required.')

    old_mappings = HostSlugMsidMappingModel.lookup_by_pid_and_msid(pid, msid)

    if old_mappings:
        HostSlugMsidMappingModel.update_mappings(old_mappings, host, slug=slug)
    else:
        add_hostslug_msid_mapping(host, slug, pid, msid)


def set_recently_created_slug_for_msid_in_memcache(msid, slug):
    """ Temporarily adds the slug for a given MSID to memcache
    :param msid: the msid the slug is associated with
    :param slug: the slug to store in memcache
    """
    key = 'last-slug-created-for-msid-%s' % msid
    memcache.set(key, slug)


def get_recently_created_slug_for_msid_from_memcache(msid):
    """ Retrieves the slug for a given MSID from memcache
    :param msid: the msid to retrieve the slug for
    :return: the slug associated with the given MSID
    """
    key = 'last-slug-created-for-msid-%s' % msid
    slug = memcache.get(key)
    return slug


@require_args
def does_hostslug_exist(hostname, old_slug, new_slug):
    """ Returns True if hostslug already exists """
    if old_slug == new_slug:
        return False
    key = HostSlugMsidMappingModel.build_key(hostname, slug=new_slug)
    existing_hsmm = key.get()
    if existing_hsmm and existing_hsmm.redirect_hostslug is None:
        return True
    else:
        return False


@require_args
def update_hostslug(pid, msid, host, old_slug, new_slug):
    """ Update the old slug with new slug. """
    hostslugs = lookup_hostslug_msid_mapping_for_msid(pid, msid)
    if new_slug and next((hs for hs in hostslugs if hs.slug == old_slug), None) and old_slug != new_slug:
        def add_remove_hostslug():
            """ Add and remove hostslug for a site """
            try:
                remove_msid_for_hostname_slug(host, slug=old_slug, redirect_host=host, redirect_slug=new_slug)
                add_hostslug_msid_mapping(host, new_slug, pid, msid, transactional=False)
                host_slug_msid_mapping_expire_signal.send('{}:{}'.format(host, old_slug))
                host_slug_msid_mapping_expire_signal.send('{}:{}'.format(host, new_slug))
            except exceptions.HostSlugMsidMappingExistsException as e:
                e.message = 'This URL is already in use. Please enter a unique URL.'
                raise
        ndb.transaction(add_remove_hostslug, xg=True)


def lookup_host_slug_msid_mappings(pid, msid, hostslug):
    """ retrieves all HostSlugMsid Mapping Models that match the criteria should only be 1 """
    query = HostSlugMsidMappingModel.lookup_by_pid_and_msid_query(pid, msid)
    return query.filter(HostSlugMsidMappingModel.hostslug == hostslug).fetch()


def remove_msid_for_hostname_slug(hostname, slug=None, redirect_host=None, redirect_slug=None):
    """ Removes a hostname/slug -> msid mapping. """
    if not hostname:
        raise ValueError('hostname is required.')
    key = HostSlugMsidMappingModel.build_key(hostname, slug=slug)
    if not redirect_host:
        key.delete()
    else:
        hsmm = key.get()
        if not hsmm:
            return # there is nothing existing to redirect
        hsmm.redirect_hostslug = HostSlugMsidMapping.compose_hostslug(redirect_host, redirect_slug)
        hsmm.put()
    host_slug_msid_mapping_expire_signal.send('{}:{}'.format(hostname, slug is not None and slug or ''))


def remove_host_slug_msid_mapping(host_slug_msid_mapping):
    """ removes the given mapping"""
    hostslug = host_slug_msid_mapping.hostslug
    host_slug_msid_mapping.key.delete()
    host, slug = split_host_slug("http://%s" % hostslug)
    host_slug_msid_mapping_expire_signal.send('{}:{}'.format(host, slug is not None and slug or ''))


class HostSlugMsidMapping:
    """ A host/slug->pid mapping. """
    def __init__(self, hostslug, pid, msid, redirect_hostslug=None, created=None):
        if not hostslug:
            raise ValueError('hostslug is required.')
        if not pid:
            raise ValueError('pid is required.')
        if not msid:
            raise ValueError('msid is required.')
        self.hostslug = hostslug.lower()
        self.pid = pid.upper()
        self.msid = msid.upper()
        self.redirect_hostslug = redirect_hostslug
        self.created = created

    def __partition_hostslug(self):
        """ Splits the hostslug into a host and a slug, returning a tuple (host, slug). """
        hs = self.hostslug.strip('/')
        if '/' in hs:
            return hs.split('/')
        else:
            return hs, None

    @property
    def host(self):
        """ Removes leading/trailing slashes and grabs the host at the start of the hostslug. """
        return self.__partition_hostslug()[0]

    @property
    def slug(self):
        """ Removes leading/trailing slashes and grabs the slug at the end of hostslug. """
        return self.__partition_hostslug()[1]

    @classmethod
    def from_model(cls, model):
        """ Builds the class from a model. """
        if not model:
            return None
        hsmm = cls(model.hostslug, model.pid, model.msid, redirect_hostslug=model.redirect_hostslug,
                   created=model.created)
        return hsmm

    @staticmethod
    def compose_hostslug(host, slug=None):
        """ Builds a hostslug string given a host and an optional slug. """
        return HostSlugMsidMappingModel.compose_hostslug(host, slug=slug)


def get_market_mappings(pid):
    """ Returns all MarketMappings for a given Pid """
    return MarketMapping.lookup_all(pid=pid, count=500)


def add_market_mapping(pid, market_id, host):
    """ Add a new MarketMapping, or overwrite an existing one. """
    if not pid:
        raise ValueError('pid is required.')
    if not market_id:
        raise ValueError('market_id is required.')
    if not host:
        raise ValueError('host is required.')
    if not VALID_HOSTNAME.match(host):
        raise exceptions.InvalidHostnameException()

    key = MarketMapping.build_key(market_id, pid)

    kwargs = {
        'key': key,
        'pid': pid,
        'market_id': market_id,
        'host': host
    }
    mapping = MarketMapping(**kwargs)
    mapping.put()
    return mapping


def get_market_mapping(pid, market_id):
    """ Get a MarketMapping """
    if not pid:
        raise ValueError('pid is required.')
    if not market_id:
        raise ValueError('market_id is required.')

    key = MarketMapping.build_key(market_id, pid)
    mapping = key.get()
    return mapping


def delete_market_mapping(pid, market_id):
    """ Delete a MarketMapping """
    mapping = get_market_mapping(pid, market_id)
    if mapping:
        mapping.key.delete()
    return True


def get_host(partner_id, host, market_id):
    """
    Return the host name to use for a site, given the provided
    host name and market ID, both of which may or may not exist.
    """
    if host:
        return host

    partner_id = get_ms_pid(partner_id)
    if market_id:
        mapping = get_market_mapping(partner_id, market_id)
        if mapping:
            # confirm the host uses slugs
            hosts_with_slugs = lookup_host_pid_mappings(partner_id, mapping.host, uses_slugs=True)
            if len(hosts_with_slugs) != 1:
                raise exceptions.NoValidMarketHostException("Host '%s' mapped to pid '%s' market id '%s' does not use "
                                                            "slugs" % (mapping.host, partner_id, market_id))
            return mapping.host

    default_host = HostPidMappingModel.get_default_host_for_partner_id(partner_id)
    if not default_host:
        raise exceptions.NoValidHostException("No default host set for pid: '%s'" % partner_id)
    return default_host.host
