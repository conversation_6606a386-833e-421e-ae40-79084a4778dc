""" Module for retrieving and processing google analytics. """
import logging
from app.domain.utils import beautify_number
from datetime import date, timedelta
from apiclient import discovery
from app.domain.validation import require_args
from settings import PARTNER_CENTRAL_HOST
import urllib.parse

#Google Analytics CustomVariable Keys
UAPROFILE_KEY = 'ga:customVarValue1'
MSID_KEY = 'ga:customVarValue2'


class GoogleAnalyticsV3:
    """
    Responsible for fetching google analytics data.
    Tool to build queries so that you know what you are building and expecting from Google Analytics:
    Google Analytics Query Explorer 2 -> https://ga-dev-tools.appspot.com/explorer/
    """

    @require_args
    def __init__(self, msids, google_analytics_id=None, start_date=None, end_date=None):
        """ init """
        # instantiated during login
        self.service = None

        # the id of the table to query
        self._table_id = None
        # the id of the microsite to retrieve analytics for
        if isinstance(msids, list):
            self.msids = msids
        else:
            self.msids = [msids]

        # get data over 2 week interval by default
        self.end_date = end_date or date.today()
        self.start_date = start_date or self.end_date - timedelta(14)

        # the id of the web property containing analytics
        self.google_analytics_id = google_analytics_id
        # the id of the google analytics account
        self.account_id = None
        if google_analytics_id:
            self.account_id = google_analytics_id.split('-')[1]

    def login(self):
        """ Authenticate with google api. """
        self.service = discovery.build('analytics', 'v3')

    @property
    def table_id(self):
        """ The id of the table to query. """
        if not self._table_id and self.service:
            profiles = self.service.management().profiles().list(
                accountId=self.account_id,
                webPropertyId=self.google_analytics_id).execute()

            if profiles.get('items'):
                profile_id = profiles.get('items')[0].get('id')
                self._table_id = 'ga:%s' % profile_id

        return self._table_id

    def _get_filters(self):
        """ Construct filter string which is used in google analytics filter params """
        filters_want_msid = ','.join(MSID_KEY + '==%s' % msid for msid in self.msids)
        filters_skip_referral_msid = ','.join('ga:referralPath!=/edit/%s/website/' % msid for msid in self.msids)
        filters_skip_referral_admin = 'ga:referralPath!=/admin/'
        partner_central_url = urllib.parse.urlparse(PARTNER_CENTRAL_HOST)
        filters_skip_pc_url = 'ga:source!=%s' % partner_central_url.netloc
        return "{};{};{};{}".format(filters_want_msid, filters_skip_referral_msid, filters_skip_referral_admin,
                                filters_skip_pc_url)

    def _get_data(self, **kwargs):
        """ Fetch the google analytics data.
        See Google Analytics Core Reporting reference for more info:
        https://developers.google.com/analytics/devguides/reporting/core/v3/reference
        """
        if not self.service:
            raise ValueError('GoogleAnalyticsV3.login() must be called before data can be fetched.')
        kwargs['ids'] = self.table_id
        logging.debug('Data Kwargs: %s', kwargs)
        result = self.service.data().ga().get(**kwargs).execute()
        logging.debug('Data Result: %s', str(result))
        return result

    def get_traffic_data(self, use_msid_dimension=False, **kwargs):
        """ Fetch daily traffic data including number of unique visitors, visits, and page views. """
        logging.debug('get_daily_traffic_data')

        dimensions = MSID_KEY if use_msid_dimension else 'ga:date'
        params = {
            'start_date': self.start_date.isoformat(),
            'end_date': self.end_date.isoformat(),
            'dimensions': dimensions,
            'metrics': 'ga:visits,ga:visitors,ga:pageviews,ga:newvisits',
            'filters': self._get_filters()
        }
        params.update(kwargs)
        return self._get_data(**params)

    def get_event_data(self, use_msid_dimension=False, **kwargs):
        """ Fetch event data. """
        logging.debug('get_event_data')

        dimensions = MSID_KEY + ',ga:eventAction' if use_msid_dimension else 'ga:eventAction'
        params = {
            'start_date': self.start_date.isoformat(),
            'end_date': self.end_date.isoformat(),
            'dimensions': dimensions,
            'metrics': 'ga:totalEvents',
            'filters': self._get_filters()
        }
        params.update(kwargs)
        return self._get_data(**params)

    def get_source_data(self, **kwargs):
        """ Performs a query based on the traffic sources for a set of microsites for the csv report. """
        params = {
            'start_date': self.start_date.isoformat(),
            'end_date': self.end_date.isoformat(),
            'dimensions': MSID_KEY + ',ga:source',
            'metrics': 'ga:visitors',
            'filters': self._get_filters(),
            'sort': '-ga:visitors'
        }
        params.update(kwargs)
        return self._get_data(**params)

    def get_image_click_data(self, **kwargs):
        """ Performs a query to retrieve the Image Click Events filters by list of microsite ids """
        params = {
            'start_date': self.start_date.isoformat(),
            'end_date': self.end_date.isoformat(),
            'dimensions': MSID_KEY + ',ga:pageTitle',
            'metrics': 'ga:totalEvents',
            'filters': 'ga:eventAction==Image;' + self._get_filters()
        }
        params.update(kwargs)
        return self._get_data(**params)

    def get_mobile_traffic_data(self, **kwargs):
        """ Performs a query to retrieve the mobile site views.
        Filters by ua_profile phone and a list of microsite ids """
        params = {
            'start_date': self.start_date.isoformat(),
            'end_date': self.end_date.isoformat(),
            'dimensions': MSID_KEY,
            'metrics': 'ga:pageviews',
            'filters': UAPROFILE_KEY + '==phone;' + self._get_filters()
        }
        params.update(kwargs)
        return self._get_data(**params)

    def get_page_views_data(self, **kwargs):
        """ Performs a query to retrieve the page views. """
        params = {
            'start_date': self.start_date.isoformat(),
            'end_date': self.end_date.isoformat(),
            'dimensions': MSID_KEY + ',' + UAPROFILE_KEY + ',ga:pagePath,ga:pageTitle',
            'metrics': 'ga:pageviews',
            'filters': self._get_filters()
        }
        params.update(kwargs)
        return self._get_data(**params)


class GoogleAnalyticsV3GraphData:
    """ Responsible for formatting fetched google analytics data. """

    NAME_TO_LABEL_MAP = {
        'ga:visits': 'Visits',
        'ga:visitors': 'Unique Visitors',
        'ga:pageviews': 'Page Views',
        'ga:newvisits': 'New Visits'
    }
    EVENTS_NAME_TO_LABEL_MAP = {
        'image': 'View Image',
        'map': 'View Map',
        'video': 'View Video',
        'phone': 'Call',
        'contact': 'View Contact',
        'link': 'Link To Full Site',
        'hours': 'View Hours',
        'qr': 'QR Scan',
    }

    @require_args
    def __init__(self, msid, google_analytics_id=None, start_date=None, end_date=None):
        """ init """
        if not end_date:
            # excluding the today, as today has not finished, we would only get partial data for today
            end_date = date.today() - timedelta(days=1)
        if not start_date or start_date > end_date:
            start_date = end_date - timedelta(days=13)

        self.ga = GoogleAnalyticsV3(msid, google_analytics_id=google_analytics_id,
                                    start_date=start_date, end_date=end_date)

    @property
    def start_date(self):
        """ The first date to fetch analytics for. """
        return self.ga.start_date

    @property
    def end_date(self):
        """ The last date to fetch analytics for. """
        return self.ga.end_date

    def get_data(self):
        """ Returns data for graphing google analytics. """
        if not self.ga.google_analytics_id:
            # Don't fetch data if no google analytics id provided.
            return None

        self.ga.login()
        graph_data = None
        try:
            traffic_data = self._fetch_traffic_data()
            event_data = self._fetch_event_data()
            # don't add graph data unless there is some
            if traffic_data and event_data:
                graph_data = {
                    'traffic': traffic_data,
                    'events': event_data
                }
        except Exception:
            logging.exception('Unable to retrieve Google Analytics data. Data will not be displayed.')
            graph_data = None

        logging.debug('Graph Data: %s', str(graph_data))
        return graph_data

    def _generate_date_labels(self):
        """ Return a list of labels.
        e.g. ['Sep 10', 'Sep 11', 'Sep 12', 'Sep 13', 'Sep 14', 'Sep 15', 'Sep 16']
        """
        if self.start_date > self.end_date:
            raise ValueError('start_date cannot be greater than end_date.')
        num_of_days = (self.end_date - self.start_date).days
        labels = []
        for i in range(0, num_of_days + 1):
            label_date = self.start_date + timedelta(days=i)
            labels.append(label_date.strftime('%b %d'))
        return labels

    def _fetch_traffic_data(self):
        """ Returns graph ready data as a dictionary for traffic data.
        :return:
        {
            'date_labels': ['Jul 15', 'Jul 16', 'Jul 17', 'Jul 18', 'Jul 19', 'Jul 20', 'Jul 21', 'Jul 22', 'Jul 23',
                            'Jul 24', 'Jul 25', 'Jul 26', 'Jul 27', 'Jul 28'],
            'newvisits': {'trend_data': [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], 'total': 1, 'label': 'New Visits'},
            'pageviews': {'trend_data': [4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], 'total': 4, 'label': 'Page Views'},
            'visits': {'trend_data': [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], 'total': 1, 'label': 'Visits'},
            'visitors': {'trend_data': [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], 'total': 1,
                         'label': 'Unique Visitors'}
        }
        """
        logging.debug('_fetch_traffic_data')
        result = {
            'date_labels': self._generate_date_labels()
        }
        traffic_data = self.ga.get_traffic_data()
        column_names = [column_header['name'] for column_header in traffic_data.get('columnHeaders', [])]
        # iterates rows of daily data (eg. [u'20130707', u'0', u'0', u'0']) and converts to dictionary
        #   keyed by column name (eg. {'ga:visits': [0, 1, 2, 3, 4])
        daily_data = dict(list(zip(column_names, list(zip(*traffic_data.get('rows', []))))))
        totals_data = traffic_data.get('totalsForAllResults') or {}

        for column_name in column_names:
            if column_name == 'ga:date':
                continue

            column_key = str(column_name.partition(':')[2])
            # need to convert data to int since error occurs parsing unicode using js eval
            result[column_key] = {
                'trend_data': [int(d) for d in daily_data.get(column_name, [])],
                'total': int(totals_data.get(column_name, 0)),
                'label': self.NAME_TO_LABEL_MAP.get(column_name) or column_key
            }

        logging.debug('Traffic Data: %s', str(result))
        return result

    def _fetch_event_data(self):
        """ Returns graph ready data as a dictionary for event data.
        :return:
        {
            'trend_data': [0, 0, 0, 0, 0, 0, 0],
            'labels': ['View Map', 'View Image', 'View Hours', 'Call', 'View Contact',
                       'Link To Full Site', 'View Video']
        }
        """
        logging.debug('_fetch_event_data')
        labels = []
        trend_data = []
        event_data = self.ga.get_event_data()
        events = list(zip(*event_data.get('rows', [])))
        if events:
            labels, trend_data = events

        result = {
            # use mapping for label if one is available
            'labels': [self.EVENTS_NAME_TO_LABEL_MAP.get(label.lower(), label).encode('utf-8') for label in labels],
            # convert float to int incase value is decimal (which int() cannot parse)
            'trend_data': [int(float(td)) for td in trend_data]
        }

        # add any mappings with no event data
        for label in self.EVENTS_NAME_TO_LABEL_MAP.values():
            if label not in result['labels']:
                result['labels'].append(label)
                result['trend_data'].append(0)

        logging.debug('Event Data: %s', str(result))
        return result


def get_location_dashboard_data(msid, google_analytics_id, start_date=None, end_date=None):
    """
    Fetches google analytics and returns data for dashboard.
    """
    if not msid:
        raise ValueError('msid is required')

    gd = GoogleAnalyticsV3GraphData(msid, google_analytics_id=google_analytics_id,
                                    start_date=start_date, end_date=end_date)
    data = gd.get_data() or {}

    visits_data = data.get('traffic', {}).get('visits', {})
    total = visits_data.get('total', 0)
    display_value = beautify_number(total)

    return {
        'chart_data': visits_data.get('trend_data', []),
        'type': 'Number',
        'num_total': display_value
    }
