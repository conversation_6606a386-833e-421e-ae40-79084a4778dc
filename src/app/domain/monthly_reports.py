""" domain functions to genearate and serve monthly reports and related info
"""
import urllib.parse
from app.domain.utils import is_dev_appserver
from app.models.partner import PartnerBillingFile
from fantasm import startStateMachine
from settings import SECURE_HOST


def generate_monthly_reports():
    """ Manually start the FSM to fetch new tasks. """
    context = {
        'data': 'machine will not start unless you pass a context with data'
    }
    startStateMachine('GenerateMonthlyReports', context)


def get_partner_billing_report_list(pid):
    """ get a list of dictionaries containing the start of the reporting date, and a url to the file
    """
    if not pid:
        raise ValueError('Missing required parameter, pid.')

    billing_files = PartnerBillingFile.query(namespace=pid).order(-PartnerBillingFile.created).fetch()

    data_list = []
    for bill_file in billing_files:
        report_end_date = bill_file.creation_date
        if is_dev_appserver():
            scheme = 'http'
        else:
            scheme = 'https'

        # '/api/report/bill/<bill_model_key_id>/'
        use_path = f'/api/report/bill/{bill_file.key.id()}/'
        url = urllib.parse.urlunparse([scheme, SECURE_HOST, use_path, '', '', ''])

        data_list.append({'report-date': str(report_end_date.date()), 'report-url': url})

    return data_list
