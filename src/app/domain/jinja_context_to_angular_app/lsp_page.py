"""
We're dumping our entire Jinja context to the new Angular frontend,
until we get around to rewriting the backend stuff.
Until then, we need to remove some sentitive / secret info.
"""


def sanitize_jinja_context(context):
    """ See function name. """
    if 'partner' in context:
        if 'spgid' in context['partner']:
            del context['partner']['spgid']
        if 'api_key' in context['partner']:
            del context['partner']['api_key']

    service_type = context.get('service_type') or ''
    if service_type.upper() == 'UBERALL':
        context['service_type'] = 'PROVIDER_0'
    if service_type.upper() == 'YEXT':
        context['service_type'] = 'PROVIDER_1'

    return context
