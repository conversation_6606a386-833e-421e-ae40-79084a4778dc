""" Fantasm classes to drive cleanup of entities. """

import random
import logging
from google.appengine.api.blobstore.blobstore import delete as delete_blob
from google.appengine.ext import ndb, deferred

from conversation_sdk.api import ConversationServiceClient
from coresdk import API_KEY as CS_KEY
from coresdk.base import CSApiException
from fantasm.fsm import startStateMachine
from vax.utils.env import env_from_app_engine_env
from vbcsdk import ApiException
from app.domain.vbcsdk_keys import API_KEY as VBC_KEY

from app.constants import IMAGES
from app.domain.blob_mappings import delete_blob_mapping_by_key
from app.domain.constants import host_slug_msid_mapping_expire_signal
from app.domain.exceptions import SocialProfileNotFoundException
from app.domain.microsite import delete_microsite
from app.domain.page import get_pageid_blob_mappings
from app.domain.social import SitesVSocial
from app.domain.url_mappings import HostSlugMsidMapping
from app.domain.vbc import Vbc<PERSON><PERSON>unt<PERSON>roup
from app.keys import MICROSITE_KEYS
from app.models.analytics import AnalyticsReport
from app.models.blob_mappings import MsidBlobMapping
from app.models.microsite import Navigation, Microsite
from app.models.page import Page
from app.models.partner import Partner as PartnerModel
from app.models.social import MicrositeFBPageAssociation
from app.models.url_mappings import HostSlugMsidMapping as HostSlugMsidMappingModel


# W0613:Unused argument 'obj'
# Fantasm expects a particular interface
# pylint: disable=W0613
from app.models.microsite import MicrositeTombstone

# Actions for the RemoveMicrosite state machine.


def look_for_spid(microsite_key, msid):
    """ Look in various places for the SPID """
    spid = None
    pid = microsite_key.namespace()
    site = Microsite.get_by_msid(msid, pid)
    if site:
        spid = site.spid
    else:
        # If the microsite was already removed for some reason, we can still attempt to lookup the spid via
        # the social profile registration.
        logging.warning("Unable to find microsite for key: %s, attempting to lookup spid via SP registration",
                        microsite_key)
        result = SitesVSocial(msid).profile_manager.lookupSocialProfileRegistrations(uid='ms', accountId=msid)
        if isinstance(result, list):
            # There should be exactly one social profile corresponding to the msid.  At one time, there could be
            # multiple profiles, so the workaround was to guess which profile was valid and use its spid.
            # Eventually, multiple attempts to delete a microsite would choose the correct profile.  Yeah, really.
            if len(result):
                if len(result) > 1:
                    logging.error("Multiple social profiles (%d) found for MSID %s", len(result), msid)
                spid = random.choice(result).get(CS_KEY.SOCIAL_PROFILE_ID)
        else:
            spid = result.get(CS_KEY.SOCIAL_PROFILE_ID, None)

    return spid


class RemoveSiteFacebookPages:
    """ Remove the site from all facebook pages """

    def execute(self, context, obj):
        """ Remove the site from all facebook pages """
        msid = context['msid']
        pid = context['pid']

        microsite_key = context['microsite_key']
        if not microsite_key:
            logging.info("No microsite key provided.")
            return

        spid = look_for_spid(microsite_key, msid)

        if spid:
            try:
                SitesVSocial(msid, pid=pid, spid=spid).remove_site_from_all_facebook_pages()
            except ValueError as ve:
                logging.error('Unable to remove site from facebook page: %s (%s %s %s).  Continuing with site removal.',
                              ve.args[0], pid, msid, spid)
            except CSApiException as e:
                if e.status == 412:
                    # The social token is broken.
                    logging.error('The social token is broken, we cannot remove the site from all facebook pages. '
                                  'Continuing anyway since this is on site removal. (%s %s %s)', pid, msid, spid)
                else:
                    logging.error('Unable to remove site from facebook page. '
                                  '%d: %s Continuing to delete microsite (%s %s %s)',
                                  e.status, e.args[0], pid, msid, spid)
        else:
            logging.error("Failed to find a SPID matching PID %s MSID %s.  Microsite deletion will proceed where "
                          "possible but may need to be retried to fully complete.  Please look into why a SPID could "
                          "not be found.", pid, msid)

        context['spid'] = spid
        return 'process-fbassociations'


class RemoveMicrositeFBPageAssociation:
    """ Remove MicrositeFBPageAssociation """

    def execute(self, context, obj):
        """ Remove all the MicrositeFBPageAssociation entities for the given msid. """
        msid = context['msid']
        pid = context['pid']
        keys = MicrositeFBPageAssociation.query(MicrositeFBPageAssociation.msid==msid, namespace='').\
                                    filter(MicrositeFBPageAssociation.pid==pid).fetch(keys_only=True)
        ndb.delete_multi(keys)

        return 'process-socialservice'


class RemoveSiteAsSocialService:
    """ Remove the site from the social profile's social services """

    def execute(self, context, obj):
        """ Execute the removal """
        msid = context['msid']
        pid = context['pid']
        spid = context.get('spid')

        if spid:
            try:
                SitesVSocial(msid, pid=pid, spid=spid).deregister_site_as_social_service()
            except SocialProfileNotFoundException:
                logging.exception('Could not find social profile %s when attempting to remove site as social service.',
                                  spid)
            except CSApiException:
                logging.exception('Error removing site "%s" with pid "%s" and spid "%s" as social service. '
                                  'Site will not be deregistered.', msid, pid, spid)
        else:
            logging.warning("Could not remove site %s %s as a social service: no SPID", pid, msid)

        return 'dereg-agid'


class DeregisterFromAccountGroup:
    """ Deregister from the account group """

    def execute(self, context, obj):
        """ Deregister from the account group """
        msid = context['msid']
        pid = context['pid']

        microsite_key = context['microsite_key']

        if not microsite_key:
            logging.info("No microsite key provided.")
            return

        agid = None
        entity = microsite_key.get()
        if entity:
            agid = entity.agid
        else:
            try:
                registrations = SitesVSocial(msid, pid=pid).lookup_social_registrations_for_site()
                if isinstance(registrations, list) and len(registrations):
                    vbc_accounts = [x for x in registrations if x[CS_KEY.USER_ID] == 'vbc']
                    if vbc_accounts:
                        agid = vbc_accounts[0][VBC_KEY.ACCOUNT_ID]
            except SocialProfileNotFoundException as e:
                logging.warning('Social profile not found for msid "%s" and pid "%s". Original exception: %s',
                                msid, pid, e.args[0])
        if agid:
            try:
                VbcAccountGroup.remove_account_from_account_group(pid, msid, agid)
                logging.info('De-registered from account group:(%s)', agid)
            except ApiException:
                logging.warning("Couldn't deregister from agid; agid:%s, msid:%s, ms pid:%s", agid, msid, pid)
            except ValueError as e:
                logging.error(e.args[0])
        else:
            logging.info("msid: %s is not registered with an account group", msid)

        return 'dereg-spid'


class DeregisterSocialProfile:
    """ Deregister from the social profile """

    def execute(self, context, obj):
        """ Deregister from the social profile """
        msid = context['msid']
        pid = context['pid']
        spid = context.get('spid')

        if spid:
            try:
                SitesVSocial(msid, pid=pid, spid=spid).deregister_social_profile()
            except CSApiException:
                logging.exception('Could not deregister social profile: %s, %s, %s', spid, pid, msid)
        else:
            logging.info('Could not deregister social profile: %s, %s: no SPID', pid, msid)

        return 'process-removems'


class RemoveMicrosite:
    """ Removes a microsite. """

    def execute(self, context, obj):
        """ Deletes the microsite. """
        microsite_key = context['microsite_key']

        if not microsite_key:
            logging.info("No microsite key provided.")
            return

        site = Microsite.get_by_key(microsite_key)
        if not site:
            # proceed with state machine and delete any other dependent entities that exist
            return 'process-hostslugmsidmappings'

        # Add spid to context for future states
        if not context.get('spid'):
            context['spid'] = site.spid

        if context.get('skip_tombstone', False):
            logging.debug('skipping tombstoning for %s: Microsite entity %s', context['msid'], microsite_key)
        else:
            # Add Tombstone
            self.create_microsite_tombstone(microsite_key)

        if site.chat_widget_id:
            delete_chat_widget(site.chat_widget_id)

        # Delete Microsite
        microsite_key.delete()

        return 'process-hostslugmsidmappings'

    def create_microsite_tombstone(self, microsite_key):
        """
        Creates a tombstone before deleting microsite
        """
        ms = Microsite.get_by_key(microsite_key)
        if ms:
            kwargs = {
                "key": MicrositeTombstone.build_key(msid=ms.msid, pid=ms.pid),
                MICROSITE_KEYS.MSID: ms.msid,
                MICROSITE_KEYS.ACCOUNT_GROUP_ID: ms.agid,
                MICROSITE_KEYS.PID: ms.pid,
                MICROSITE_KEYS.PMSID: ms.pmsid,
                MICROSITE_KEYS.ACCOUNT_ORIGIN: ms.account_origin,
                MICROSITE_KEYS.NAME: ms.name,
                MICROSITE_KEYS.ORIGINAL_CREATED: ms.created,
                MICROSITE_KEYS.CUSTOMER_IDENTIFIER: ms.customer_identifier,
                MICROSITE_KEYS.ADDRESS1: ms.address1,
                MICROSITE_KEYS.ADDRESS2: ms.address2,
                MICROSITE_KEYS.CITY: ms.city,
                MICROSITE_KEYS.STATE: ms.state,
                MICROSITE_KEYS.ZIP_CODE: ms.zipcode,
                MICROSITE_KEYS.COUNTRY: ms.country,
                MICROSITE_KEYS.EMAIL: ms.email,
                MICROSITE_KEYS.BILLING_CODE: ms.billing_code,
                MICROSITE_KEYS.ACTIVE_DATE_TIME: ms.active_date_time,
                MICROSITE_KEYS.MARKET_ID: ms.market_id,
                MICROSITE_KEYS.LOGIN_COUNT: ms.login_count,
                MICROSITE_KEYS.LAST_LOGIN: ms.last_login,
                MICROSITE_KEYS.LOGIN_HISTORY: ms.login_history,
            }
            msts = MicrositeTombstone(**kwargs)
            msts.put()
            logging.debug("Added: MicrositeTombstone entity %s", msts.key)
            return msts
        else:
            logging.error("Error: No Microsite found with matching key %s", microsite_key)


class RemoveHostSlugMsidMappings:
    """ Removes HostSlugMsidMapping """

    def execute(self, context, obj):
        """ Remove all the HostSlugMsidMapping entities for the given msid. """
        msid = context['msid']
        pid = context['pid']

        query_result = HostSlugMsidMappingModel.lookup_by_pid_and_msid(pid, msid)
        mappings = query_result.results
        logging.debug("Deleting %s: HostSlugMsidMapping entities %s", msid, [mapping.hostslug for mapping in mappings])
        ndb.delete_multi([m.key for m in mappings])

        for mapping in mappings:
            domain_mapping = HostSlugMsidMapping.from_model(mapping)
            hostname = domain_mapping.host
            slug = domain_mapping.slug
            host_slug_msid_mapping_expire_signal.send('{}:{}'.format(hostname, slug is not None and slug or ''))

        return 'process-navigation'


class RemoveNavigation:
    """ Removes Navigation """

    def execute(self, context, obj):
        """ Remove all the Navigation entities for the given msid and schedule their Pages for deletion. """
        msid = context['msid']
        pid = context['pid']

        nav_keys = Navigation.build_predictive_keys(msid, pid=pid)
        nav_items = ndb.get_multi(nav_keys)

        # Page
        pageids = []
        page_contexts = []
        for nav_item in nav_items:
            if not nav_item:
                continue

            pageids.append(nav_item.pageid)
            page_contexts.append({
                'page_key' : Page.build_key(nav_item.pageid, msid, pid=pid)
            })
        logging.debug("Deleting %s: Scheduling Pages for deletion %s", msid, pageids)
        startStateMachine('RemovePage', page_contexts)

        # delete navigation items
        logging.debug("Deleting %s: Navigation entities %s", msid, nav_keys)
        ndb.delete_multi(nav_keys)

        return 'process-msidblobmappings'


class RemoveMsidBlobMappings:
    """ Removes MsidBlobMappings """

    def execute(self, context, obj):
        """ Remove all the MsidBlobMapping entities for the given msid. """
        msid = context['msid']
        pid = context['pid']

        logging.debug("Deleting %s: Scheduling MsidBlobMappings for deletion", msid)
        mapping_keys = MsidBlobMapping.build_predictive_keys(msid, pid=pid)
        for mapping_key in mapping_keys:
            delete_blob_mapping_by_key(mapping_key)


class TemporaryState:
    """ TEMPORARY: Remove after cleaning out default queue """

    def execute(self, context, obj):
        """ Execute the states that used to be the last two states in the FSM """
        deferred.defer(remove_ms_fb_page_association, context, obj, _queue='remove-ms-fb-page-association')


def remove_ms_fb_page_association(context, obj):
    """ First temporary method to remove ms to fb page """
    RemoveMicrositeFBPageAssociation().execute(context, obj)
    deferred.defer(remove_site_as_social_service, context, obj, _queue='remove-site-as-social-service')


def remove_site_as_social_service(context, obj):
    """ Second temporary method to remove site as social service """
    RemoveSiteAsSocialService().execute(context, obj)


# Actions for the RemoveBlob state machine.

class RemoveBlob:
    """ Removes blob. """

    def execute(self, context, obj):
        """ Deletes a blob. """
        blobkey = context['blobkey']
        delete_blob(blobkey)

class RemovePage:
    """ Removes a page and related items. """

    def execute(self, context, obj):
        """ Deletes the page. """
        page_key = context['page_key']
        entity = page_key.get()
        if not entity:
            return None

        context['msid'] = entity.msid
        context['pid'] = entity.pid

        page_key.delete()

        if entity.template == IMAGES:
            return 'process-images'

        return None


class RemovePageidBlobMapping:
    """ Removes a page blob mapping entities and queues the blob for deletion. """

    def execute(self, context, obj):
        """ Delete a PageidBlobMapping. """
        page_key = context['page_key']
        pageid = page_key.string_id()
        msid = context['msid']
        pid = context['pid']
        mappings = get_pageid_blob_mappings(pageid, msid, pid)
        for mapping in mappings:
            delete_blob_mapping_by_key(mapping.key) # will start state machines to kill the blobs

# Actions for RemoveFullPartner state machine.
class RemoveAnalyticsReports:
    """ removes the analytics reports for the partner then moves on to process all microsites """

    def execute(self, context, obj):
        """ delete all the analytics reports and their associated blobs then move on to process all the microsites
        """
        pid = context['pid']
        logging.info('Removing AnalyticsReport with pid:(%s)', pid)
        query_limit = 50
        reports = list(range(0, 50))
        while len(reports)==query_limit:
            reports = AnalyticsReport.query(namespace=pid).fetch(query_limit, keys_only=False)
            for report in reports:
                context['blobkey'] = report.blobkey
                startStateMachine('RemoveBlob', context)
                logging.info('Started FSM to remove AnalyticsReport blob with blob_key:(%s)', report.blobkey)
                report.key.delete()

        return 'process-all-microsites'

class RemoveAllMicrosites:
    """ Removes all the microsites and their TinyIDs """

    def execute(self, context, obj):
        """ start the RemoveMicrosite machine """
        pid = context['pid']
        query_limit = 50
        keys_to_delete = [i for i in range(0, 50)]
        while len(keys_to_delete)==query_limit:
            keys_to_delete = Microsite.query(namespace=pid).fetch(query_limit, keys_only=True)
            for ms_key in keys_to_delete:
                logging.info('Removing Microsite with pid:(%s) and ms_key:(%s)', pid, ms_key)
                delete_microsite(ms_key.string_id(), pid=pid, skip_tombstone=True) # starts the RemoveMicrosite machine

        return 'remove-partner'

class RemovePartner:
    """ Removes the partner object """
    def execute(self, context, obj):
        """ deletes:
                - partner model
                - host pid mappings
                - login
                - microsite tombstones
                - sessions
                - user tokens
            then moves on to remove pidblobmappings and their blobs
        """
        pid = context['pid']
        logging.info('Removing partner with pid:(%s)', pid)

        delete_all_model_entities_in_namespace('HostPidMapping', namespace='', property_name="pid", value=pid)
        delete_all_model_entities_in_namespace('Login', namespace=pid)
        delete_all_model_entities_in_namespace('MicrositeTombstone', namespace=pid)
        delete_all_model_entities_in_namespace('UserToken', namespace=pid)
        delete_all_model_entities_in_namespace('Session', namespace=pid)

        partner_key = PartnerModel.build_key(pid)
        partner_key.delete()


def delete_all_model_entities_in_namespace(model_kind, namespace='', property_name='', value=''):
    """ page through all the entities found with this query and delete them all
    """
    logging.info('Deleting %s in namespace:(%s) where %s==%s', model_kind, namespace, property_name, value)
    query_limit = 50
    keys_to_delete = list(range(0, query_limit))
    query = ndb.Query(kind=model_kind, namespace=namespace)
    if property_name:
        query = query.filter(ndb.GenericProperty(property_name) == value)
    while len(keys_to_delete) == query_limit:
        keys_to_delete = query.fetch(limit=query_limit, keys_only=True)
        ndb.delete_multi(keys_to_delete)


def delete_chat_widget(widget_id):
    """
    deletes a chat widget
    Returns:
        None
    """
    ConversationServiceClient.set_environment(env_from_app_engine_env())
    ConversationServiceClient.delete_widget(
        widget_id=widget_id,
    )
    return
