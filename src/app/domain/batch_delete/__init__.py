""" The code for the batch delete state machine """

import logging
import datetime
from app.models.batch_delete import BatchDeleteMeta as BatchDeleteMetaModel
from app.domain.microsite import get_microsite_for_pmsid, delete_microsite
from app.domain.url_mappings import lookup_hostnames_for_pid

# W0613:Unused argument 'obj'
# Fantasm expects a particular interface
# pylint: disable=W0613

def create_batch_delete_meta_entity(pid, filename=None, key=None):
    """ Creates a new batch delete meta entity. """
    if not pid:
        raise ValueError('pid is required')
    pid = pid.upper()

    if not key:
        key = BatchDeleteMetaModel.generate_new_key(pid)
    else:
        key = BatchDeleteMetaModel.build_key(key_name=key)
    delete_id = key.string_id()
    entity = BatchDeleteMetaModel(key=key, pid=pid, delete_id=delete_id, filename=filename)
    entity.put()
    return delete_id


def lookup_recent_delete_jobs(count=25):
    """ Returns the most recent delete jobs. """
    qr = BatchDeleteMetaModel.lookup_most_recent(count=count)
    return [BatchDeleteMeta.from_model(e) for e in qr.results]


class BatchDeleteMeta:
    """ A domain object for a BatchDeleteMeta entity """

    def __init__(self, **kwargs):
        """ Initialize """
        if not kwargs.get('delete_id'):
            raise ValueError('delete_id is required.')

        self.delete_id = kwargs.get('delete_id')
        self.pid = kwargs.get('pid')
        self.filename = kwargs.get('filename')
        self.created = kwargs.get('created')

    @property
    def created_cst(self):
        """ The created datetime as CST. """
        return self.created - datetime.timedelta(hours=6)

    @classmethod
    def from_model(cls, model):
        """ Creates a domain object given a model object. """
        if not model:
            return None
        return cls(**model.to_dict())


class Initialize:
    """ Initialize the state machine """
    
    def execute(self, context, obj):
        """ Check that the required variables are provided. """

        bad_input = False
        if not context.get('gcs_file_name'):
            logging.error('gcs_file_name is required.')
            bad_input = True

        if not context.get('pid'):
            logging.error('pid is required.')
            bad_input = True

        if not context.get('filename'):
            logging.error('filename is required.')
            bad_input = True

        if 'pid' in context:
            hosts = lookup_hostnames_for_pid(context['pid'])
            if not len(hosts) > 0:
                logging.error('No hosts found for pid "%s".', context['pid'])
                bad_input = True

        if bad_input:
            return None

        context['host'] = hosts[0]
        logging.info('Using host "%s" for import for pid "%s".', context['host'], context['pid'])

        return 'ok'

class CreateBatchDeleteMetaEntity:
    """ Creates a BatchDeleteMeta entity for this batch """

    def execute(self, context, obj):
        """ Creates a BatchDeleteMeta entity """
        pid = context['pid']
        filename = context['filename']
        meta_key = None
        if 'meta_key' in context:
            meta_key = context['meta_key']

        context['delete_id'] = create_batch_delete_meta_entity(pid, filename=filename, key=meta_key)

        return 'ok'

class IterateLines:
    """ Iterate through each line of the batch import file and process the data. """

    def continuation(self, context, obj, token=0):
        """ Read the next line from the batch import line or exit state machine if we are finished. """

        linenum = context.get('linenum', 0)

        cloudreader = cloudstorage.read_file(context['gcs_file_name'])
        logging.info('Seeking to (%s) %s in file %s', type(token), token or 0, context['gcs_file_name'])
        cloudreader.seek(int(token) if token else 0)

        obj['line'] = cloudreader.readline()

        if not obj['line']:

            context['linenum'] = 0
            return None

        else:

            context['linenum'] = linenum + 1
            # return the current state of the cloud reader for the next time
            return cloudreader.tell()

    def execute(self, context, obj):
        """ Process the current line of data. """

        if not obj.get('line'):
            return

        pid = context['pid']
        linenum = context['linenum']
        line = obj['line']

        # add a "counter" task to the batch-import-counter pull queue
        logging.debug('Processing line (%d) %s', linenum, line)

        pmsid = line.strip()
        try:
            microsite = get_microsite_for_pmsid(pmsid, pid=pid)
        except ValueError:
            microsite = None
            
        if not microsite:
            logging.info("Unable to find microsite for Site Id: %s", pmsid)
            return None
        
        delete_microsite(microsite.msid, pid=pid)
