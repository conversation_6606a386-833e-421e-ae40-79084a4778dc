""" VBC Integration Domain Logic """
import logging

import vconfig
from vconfig.sdk import ApiException

from vbcsdk import AccountGroupClient, BLANK_ARG
from app.domain.vbcsdk_keys import API_KEY as VBC_KEY

from app.country_state import get_country_code, get_state_code
from app.domain.utils import convert_unicode_to_string
from app.domain.validation import require_args
from app.keys import MICR<PERSON>ITE_KEYS
from settings import VBC_USER, VBC_API_KEY, VBC_CONFIG, MICROSITE_PARTNER_PID_MAP, ENVIRONMENT_NAME

PARTNER_MICROSITE_PID_MAP = {v: k for k, v in MICROSITE_PARTNER_PID_MAP.items()}


class InvalidActivationResponseException(Exception):
    """ Raised when we get an invalid response from marketplace """


class VbcAccountGroup:
    """ Class containing wrappers for VBC Account Group apis. """

    client = AccountGroupClient(VBC_USER, VBC_API_KEY, configuration=VBC_CONFIG)

    @classmethod
    def _get_vbc_phone_numbers(cls, work_numbers, call_tracking_numbers):
        """
        Convert our lists of work and call tracking numbers to the format expected by VBC.
        """
        numbers = []
        for n in work_numbers:
            numbers.append({VBC_KEY.TYPE: VBC_KEY.WORK_TYPE, VBC_KEY.DIGITS: n})
        for n in call_tracking_numbers:
            numbers.append({VBC_KEY.TYPE: VBC_KEY.CALL_TRACKING_TYPE, VBC_KEY.DIGITS: n})
        return numbers

    V3_MS_TO_VBC_ARG_MAP = {
        MICROSITE_KEYS.PID: 'partnerId',
        MICROSITE_KEYS.AGID: 'accountGroupId',
        MICROSITE_KEYS.CUSTOMER_IDENTIFIER: 'customerIdentifier',
        MICROSITE_KEYS.NAME: 'companyName',
        MICROSITE_KEYS.CITY: 'city',
        MICROSITE_KEYS.ZIP_CODE: 'zip',
        MICROSITE_KEYS.COUNTRY: 'country',
        MICROSITE_KEYS.STATE: 'state',
        MICROSITE_KEYS.ADDRESS1: 'address',
        MICROSITE_KEYS.MARKET_ID: 'marketId',
        MICROSITE_KEYS.WEBSITE: 'website',
        MICROSITE_KEYS.PHONE_WORK: 'workNumber',
        MICROSITE_KEYS.PHONE_CALL_TRACKING: 'callTrackingNumber',
        MICROSITE_KEYS.EMAIL: 'email',
        MICROSITE_KEYS.PLACE: 'place',
        MICROSITE_KEYS.FACEBOOK_URL: 'facebookUrl',
        MICROSITE_KEYS.FOURSQUARE_URL: 'foursquareUrl',
        MICROSITE_KEYS.GOOGLEPLUS_URL: 'googleplusUrl',
        MICROSITE_KEYS.INSTAGRAM_URL: 'instagramUrl',
        MICROSITE_KEYS.PINTEREST_URL: 'pinterestUrl',
        MICROSITE_KEYS.LINKEDIN_URL: 'linkedinUrl',
        MICROSITE_KEYS.RSS_URL: 'rssUrl',
        MICROSITE_KEYS.TWITTER_URL: 'twitterUrl',
        MICROSITE_KEYS.YOUTUBE_URL: 'youtubeUrl',
        MICROSITE_KEYS.CATEGORIES: 'SEOCategory',
        MICROSITE_KEYS.BLURB: 'tagline',
        MICROSITE_KEYS.TAX_ID: 'taxonomyId',
        MICROSITE_KEYS.HOURS_OF_OPERATION: 'hoursOfOperationJson'
    }

    @classmethod
    def convert_to_vbc_args(cls, data):
        """ Convert microsite args to vbc api compatible args
        """
        data = data or {}

        # country and state may contain anything at this point. This switches them to standard two character codes
        data[MICROSITE_KEYS.COUNTRY] = get_country_code(data.get(MICROSITE_KEYS.COUNTRY))
        data[MICROSITE_KEYS.STATE] = get_state_code(data[MICROSITE_KEYS.COUNTRY], data.get(MICROSITE_KEYS.STATE))

        kwargs = {}
        for k, v in data.items():
            if k in cls.V3_MS_TO_VBC_ARG_MAP:
                if v is None:
                    # on VBC, company name cannot be empty, so don't try to tell them to clear it.
                    if k != MICROSITE_KEYS.NAME:
                        kwargs[cls.V3_MS_TO_VBC_ARG_MAP[k]] = BLANK_ARG
                else:
                    if k in [MICROSITE_KEYS.NAME, MICROSITE_KEYS.CITY, MICROSITE_KEYS.COUNTRY, MICROSITE_KEYS.ADDRESS1]:
                        # Converts unicode values to strings so they can be url encoded
                        kwargs[cls.V3_MS_TO_VBC_ARG_MAP[k]] = convert_unicode_to_string(v)
                    else:
                        kwargs[cls.V3_MS_TO_VBC_ARG_MAP[k]] = v

        if data.get(MICROSITE_KEYS.GEO):
            kwargs[VBC_KEY.LATITUDE] = data.get(MICROSITE_KEYS.GEO).lat
            kwargs[VBC_KEY.LONGITUDE] = data.get(MICROSITE_KEYS.GEO).lon
        else:
            kwargs[VBC_KEY.LATITUDE] = BLANK_ARG
            kwargs[VBC_KEY.LONGITUDE] = BLANK_ARG

        return kwargs

    @classmethod
    @require_args
    def create_account_group(cls, ms_pid, account_name, **kwargs):
        """ Create a new account group. """
        vbc_pid = get_vbc_pid(ms_pid)
        param = cls.convert_to_vbc_args(kwargs)
        return cls.client.createAccountGroupV3(partnerId=vbc_pid, companyName=account_name,
                                               addPresenceBuilderFlag=False, **param)

    @classmethod
    @require_args
    def get_account_group(cls, ms_pid, agid, show_details=False):
        """ Get the Account Group  """
        return cls.get_account_group_async(ms_pid, agid, show_details=show_details).get_result()

    @classmethod
    @require_args
    def get_account_group_async(cls, ms_pid, agid, show_details=False):
        """ Get the Account Group  """
        vbc_pid = get_vbc_pid(ms_pid)
        return cls.client.getAccountGroupV2Async(agid=agid, pid=vbc_pid, showDetailsFlag=show_details)

    @classmethod
    @require_args
    def add_account_to_account_group(cls, ms_pid, msid, agid):
        """ Add a microsite (account) to an existing account group. """
        vbc_pid = get_vbc_pid(ms_pid)
        return cls.client.addAccountV2(agid=agid, productId=VBC_KEY.MS_API_USER, pid=vbc_pid, accountId=msid)

    @classmethod
    @require_args
    def remove_account_from_account_group(cls, ms_pid, msid, agid):
        """ Remove a microsite (account) from an existing account group. """
        vbc_pid = get_vbc_pid(ms_pid)
        return cls.client.removeAccountV2(productId=VBC_KEY.MS_API_USER, pid=vbc_pid, accountId=msid, agid=agid)

    @classmethod
    @require_args
    def update_account_group(cls, ms_pid, agid, **kwargs):
        """ Update an account group """
        vbc_pid = get_vbc_pid(ms_pid)
        params = cls.convert_to_vbc_args(kwargs)
        logging.info("Sending account group update for account group %s on vbc partner %s "
                     "with data %s", agid, vbc_pid, params)
        # temporary, (currently a blocker). Plan to maybe update vbcsdk.
        if agid and "customerIdentifier" in params:
            params.pop("customerIdentifier")
        return cls.client.updateAccountGroupV3(partnerId=vbc_pid, accountGroupId=agid, **params)


def get_vbc_pid(ms_pid):
    """ Returns a VBC pid for a given MS pid. """
    return MICROSITE_PARTNER_PID_MAP.get(ms_pid, ms_pid)


def get_ms_pid(vbc_pid):
    """ Returns a MS pid for a given VBC pid """
    return PARTNER_MICROSITE_PID_MAP.get(vbc_pid, vbc_pid)


def does_partner_have_vbc(partner_id):
    """ returns whether or not the partner has Business Center checked off at the superadmin level """
    try:
        config_client = vconfig.ConfigurationClient(ENVIRONMENT_NAME.lower())
        config = config_client.get_config(partner_id)
        return config.has_vbc
    except ApiException:
        return False