""" Utility functions
"""
import os
import re
import math
import unicodedata
import colorsys
import struct

def slugify(value, max_length=None, default=None):
    """ Copied from tipfy.utils.py
    Converts a string to slug format (all lowercase, words separated by
    dashes).

    :param value:
        The string to be slugified.
    :param max_length:
        An integer to restrict the resulting string to a maximum length.
        Words are not broken when restricting length.
    :param default:
        A default value in case the resulting string is empty.
    :returns:
        A slugified string.
    """
    value = _unicode(value)
    s = unicodedata.normalize('NFKD', value).encode('ascii', 'ignore').lower()
    s = re.sub('-+', '-', re.sub('[^a-zA-Z0-9-]+', '-', s)).strip('-')
    if not s:
        return default

    if max_length:
        # Restrict length without breaking words.
        while len(s) > max_length:
            if s.find('-') == -1:
                s = s[:max_length]
            else:
                s = s.rsplit('-', 1)[0]

    return s

def _unicode(value):
    """ Copied from tipfy.utils.py
    Encodes a string value to unicode if not yet decoded.

    :param value:
        Value to be decoded.
    :returns:
        A decoded string.
    """
    if isinstance(value, str):
        return value.decode("utf-8")

    assert isinstance(value, str)
    return value


def is_dev_appserver():
    """ Returns True if running on dev_appserver. """
    return 'development' in os.environ.get('SERVER_SOFTWARE', '').lower()


def convert_to_24_hour_format(value):
    """
    return: convert value to 24 hour format
    """
    if value:

        low_val = value.lower()
        am_pm = None
        if low_val.endswith('am'):
            am_pm = 'am'
        elif low_val.endswith('pm'):
            am_pm = 'pm'
        low_val = low_val.rstrip('am').rstrip('pm').strip()

        if ':' in low_val:
            hours, minutes = low_val.split(':')
        else:
            hours = low_val
            minutes = '00'

        if len(hours) == 1 and (am_pm == 'am' or am_pm is None):
            hours = "0%s" % hours  # ensures hours is always 2 digits

        # handling special case 12:00am -> 00:00
        hours = '00' if am_pm == 'am' and hours == '12' else hours

        if am_pm == 'pm':
            if len(hours) > 1 and hours.startswith('0'):
                hours = hours[1:]  # convert hours into int
            hours = int(hours)
            if hours < 12:
                hours += 12

        return "{}:{}".format(hours, minutes)

    return None


def get_dark_shade(color, factor=0.1):
    """ Gets an RGB hex a shade darker than the color provided
    :param color: An RGB color string in the format '#123456'
    :param factor: The percentage to darken the shade by, in decimal
    """
    if not color[0] == '#':
        raise ValueError('Color must start with #')
    color = color[1:]
    rgb = struct.unpack('BBB', color.decode('hex'))
    normalized = [x/255.0 for x in rgb]
    list_hsv = list(colorsys.rgb_to_hsv(*normalized))
    list_hsv[2] = list_hsv[2] * (1 - factor)
    denormalize = [x*255 for x in colorsys.hsv_to_rgb(*list_hsv)]
    return '#' + struct.pack('BBB', *denormalize ).encode('hex').upper()

def get_contrasting_color(color):
    """
    returns the hex color for black or white for which ever one contrasts more with the hex color
    """
    color = color[1:]
    rgb = struct.unpack('BBB', color.decode('hex'))
    text_color = '#FFFFFF'
    threshold = 127
    luminance = (0.2126 * rgb[0] + 0.7152 * rgb[1] + 0.0722 * rgb[2])

    if luminance > threshold:
        text_color = '#000000'
    return text_color

def convert_unicode_to_string(unicode_val):
    """ Converts a unicode value to a string.
    :param unicode_val: A unicode string.
    :returns: A string value for the given unicode value. Eg. 'Emporio Brazilian Caf\xef\xbf\xbd'
    """
    return str(unicode_val).encode('utf-8')

def beautify_number(n, threshold=1000):
    """
    Return a number as a string formatted in a more beautiful and concise way.
    :param n: The number to beautify.
    :param threshold: Only beautify numbers greater than this value.
    Example:
        0 => "0"
        100 => "100"
        10000 => "10K"
        1000000 => "1M"
        100000000 => "100M"
    """
    base = 0
    suffixes = 'KMBT'

    if not isinstance(n, (int, float)):
        raise TypeError('number must be an int or float.')

    if n and n >= threshold:
        base = int(math.floor(math.log(math.fabs(n))/math.log(1000)))
    suffix = suffixes[base-1] if base > 0 else ''
    # pylint: disable=non-unicode-string-format
    return '%d%s' % (round(n/math.pow(1000, base)), suffix)
