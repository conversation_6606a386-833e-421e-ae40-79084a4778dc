""" A file encompassing methods for using the google search api by manipulating documents.
"""
import json
import logging
from google.appengine.api.search import OperationResult
from google.appengine.ext import deferred
from google.appengine.api import search
from google.appengine.api.namespace_manager.namespace_manager import get_namespace

from app.domain.exceptions import DomainException
from app.models.microsite import Microsite
from app.models.partner import Partner
from app.domain.url_mappings import lookup_hostslug_msid_mapping_for_msid
from app.constants import DEFERRED_ROOT, LIST_RESULTS, EXTERNALLY_RESTRICTED


def add_microsite_search_document(microsite):
    """
    Adds a search document to the appropriate index for the given mobile site entity.
    """

    logging.info('Creating search document for msid: %s', microsite.msid)

    microsite_categories = json.dumps(microsite.categories)
    created = str(microsite.created)
    updated = str(microsite.updated)
    host_mappings = lookup_hostslug_msid_mapping_for_msid(microsite.pid, microsite.msid)
    urls_to_json = ["http://" + host.hostslug for host in host_mappings]
    urls = json.dumps(urls_to_json)

    # If you are adding something to these documents, make sure it doesn't
    # need to be added to the EXTERNALLY_RESTRICTED or LIST_RESULTS lists
    # in app/constants.py
    fields = [search.TextField(name='name', value=microsite.name),
              search.TextField(name='address1', value=microsite.address1),
              search.TextField(name='address2', value=microsite.address2),
              search.TextField(name='city', value=microsite.city),
              search.TextField(name='state', value=microsite.state),
              search.TextField(name='country', value=microsite.country),
              search.TextField(name='zipcode', value=microsite.zipcode),
              search.TextField(name='phone', value=microsite.phone),
              search.TextField(name='place', value=microsite.place),
              search.TextField(name='email', value=microsite.email),
              search.TextField(name='spid', value=microsite.spid),
              search.TextField(name='marketId', value=microsite.market_id),
              search.TextField(name='msid', value=microsite.msid),
              search.TextField(name='website', value=microsite.website),
              search.TextField(
                  name='useWebsiteForFullSiteLink',
                  value="true" if microsite.use_website_for_full_site_link else "false"
              ),
              search.TextField(
                  name='isLiteFlag',
                  value="true" if microsite.is_lite else "false"
              ),
              search.TextField(name='pid', value=microsite.pid),
              search.TextField(name='customerIdentifier', value=microsite.customer_identifier),
              search.TextField(name='identifier', value=microsite.pmsid),
              search.TextField(name='serviceCategories', value=microsite_categories),
              search.TextField(name='blurb', value=microsite.blurb),
              search.TextField(name='accountOrigin', value=microsite.account_origin),
              search.TextField(name='created', value=created),
              search.TextField(name='updated', value=updated),
              search.TextField(name='color', value=microsite.color),
              search.TextField(name='mappings', value=urls),
              search.TextField(name='workNumber', value=json.dumps(microsite.phone_work)),
              search.TextField(name='callTrackingNumber', value=json.dumps(microsite.phone_call_tracking)),
              search.TextField(name='taxId', value=json.dumps(microsite.tax_id)),
              search.TextField(name='ssoToken', value=microsite.sso_token)
              ]

    document = search.Document(
        doc_id=microsite.msid,
        fields=fields)

    try:
        logging.info("Adding micro site document: %s", document)
        results = get_site_index(microsite.pid).put(document)
        if not results[0].code == OperationResult.OK:
            logging.error('Search document add was not ok: %s', results)
            raise DomainException("Search document add was not ok: %s" % results)
        else:
            logging.info("The results %s", results)
            return document
    except search.Error:
        logging.exception('An error occurred adding MobileSite MSID:(%s) search document. '
                          'Will attempt to retry.', microsite.msid)
        raise


def remove_microsite_search_document(msid, pid):
    """ Removes a search document for an Account entity. """
    get_site_index(pid).delete(msid)


def find_microsite_documents(query_string, pid=None, limit=100, ids_only=True, offset=None, web_safe_cursor=None):
    """
    :param query_string: Optional query string to search accounts with.
    :param pid: The id of the partner of the accounts to search for.
    :param limit: The maximum number of results to return.
    :param offset: A number to use to retrieve the next set of results.
    :param ids_only: Indicates only the ids of the documents are to be retrieved.
    :return: A list of search documents corresponding to accounts matching the query, return.cursor.web_safe_string
        will be the cursor to the next results.
    """
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('pid is required.')

    if offset and web_safe_cursor:
        raise ValueError('Supply only offset or cursor, not both')

    try:
        name_desc = search.SortExpression(
            expression='name',
            direction=search.SortExpression.DESCENDING,
            default_value='')

        # Sort up to 1000 matching results by name in descending order
        sort_options = search.SortOptions(expressions=[name_desc], limit=1000)

        if offset:
            options = search.QueryOptions(
                limit=limit,
                offset=offset,
                sort_options=sort_options,
                ids_only=ids_only)
        else:
            if web_safe_cursor:
                cursor = search.Cursor(web_safe_string=web_safe_cursor)
            else:
                cursor = search.Cursor()
            options = search.QueryOptions(
                limit=limit,
                cursor=cursor,
                sort_options=sort_options,
                ids_only=ids_only)

        query = search.Query(query_string=query_string, options=options)

        index = get_site_index(pid)

        results = index.search(query)
        return results

    except search.Error:
        logging.exception(
            'An error occurred performing search with query "%s". No results will be returned.', query_string
        )

    return None


def search_accounts_for_query_json(query_string, pid=None, limit=100, offset=None):
    """
    :param query_string: The optional query string to search with.
    :param pid: The id of the partner of accounts to search for.
    :param limit: The maximum number of accounts to return.
    :param offset: The offset to use to retrieve the next set of results
    :return: {
            cursor: a websafe cursor
            data: [{account_dictionary},
                   {account_dictionary},
                   ...
                  ]
            }
            In json
    """
    return json.dumps(search_accounts_for_query(query_string, pid, limit, offset))


def search_accounts_for_query(query_string, pid=None, limit=100, offset=0):
    """
    :param query_string: The optional query string to search with.
        To search for a microsite with 'Value' in any field: query_string='Value'
        To further filter for microsites with name containing Jason, query_string= 'Value name:Jason'
        To further filter for microsites with city containing 'City', query_string='Value name:Jason city:City'
    :param pid: The id of the partner of accounts to search for.
    :param limit: The maximum number of accounts to return.
    :param offset: The offset to use to retrieve the next set of results
    :return: {
            cursor: an offset to use as cursor to retrieve the next set of results
            data: [{account_dictionary},
                   {account_dictionary},
                   ...
                  ]
            }
    """
    results = find_microsite_documents(query_string, pid=pid, limit=limit, offset=offset)
    if not results or not results.number_found:
        return {'offset': 0,
                'data': [],
                'more': False,
                'total_results': 0}

    account_ids = [result.doc_id for result in results]
    from app.domain.microsite import get_json_ready_microsite_models
    if account_ids:
        accounts = get_json_ready_microsite_models(account_ids, pid)
    else:
        accounts = []

    total_results = int(results.number_found)
    if not len(accounts) == len(account_ids):
        raise LookupError('Search documents with no associated mobile site were found')
    more = offset + limit < results.number_found
    to_return = {
        'offset': offset,
        'data': accounts,
        'more': more,
        'total_results': total_results
    }
    return to_return


def get_site_index(pid):
    """ Returns the index to be used for search, this will always return an index in the default namespace
    in the form microsite:<PID> """
    if not pid:
        raise ValueError("pid is required")
    index_name = "mobilesite:%s" % pid
    return search.Index(name=index_name, namespace='')


def convert_search_doc_to_dict(doc, external=False):
    """
    Takes in a SearchDocument and turns it into a python dictionary.
    If external is True then the function will strip out some fields which are
    restricted from the external search API.
    """
    doc_dict = {}

    for field in doc.fields:
        if external and field.name in EXTERNALLY_RESTRICTED:
            # If we're getting search doc information for an external user and
            # the field is on the list of externally restricted docs, don't include it
            continue
        if field.name in LIST_RESULTS:
            doc_dict[field.name] = json.loads(field.value)
        else:
            doc_dict[field.name] = field.value

    return doc_dict


def get_all_search_documents(pid, batch_size=1000):
    """
        Iteratively get's all search document ids directly from the index.
    """
    msids = []
    doc_index = get_site_index(pid)
    total = 0
    startid = None
    while True:
        # Get a list of documents populating only the doc_id field and extract the ids.
        docidx = doc_index.get_range(
            ids_only=True, limit=batch_size, include_start_object=False, start_id=startid
        )
        document_ids = [document.doc_id for document in docidx]
        if not document_ids:
            break
        msids.extend(document_ids)
        startid = document_ids[-1]
        amount = len(document_ids)
        logging.info("Bringing total from %s up by %s to %s", total, amount, total+amount)
        total += amount
    return msids


def fix_microsite_search_documents(dry_run=False):
    """
        Checks that all msids have a search document, and all search documents have a corresponding microsite.
    """
    logging.info("Looking Up All Partners")
    partners = Partner.lookup_all_partners()
    logging.info("Collecting Pids")
    pids = [partner.pid for partner in partners]
    logging.info("Found %s Pids", len(pids))
    for pid in pids:
        logging.info("Deferring task for pid: '%s'", pid)

        defer_url = DEFERRED_ROOT + 'fix-search-document-for-pid/%s/' % pid
        deferred.defer(fix_microsite_search_documents_for_pid, pid, dry_run, _url=defer_url, _queue='documents')
    logging.info("Done")


def fix_microsite_search_documents_for_pid(pid, dry_run=False):
    """
        Deletes all the search documents that don't have microsites and creates docs for microsites.
        If dry_run = True it wont do the fixing and do a dry run
    """
    logging.info("Working on PID: '%s'", pid)

    # Get all MSIDS under pid

    msids = get_all_msids(pid)
    logging.info("Found %s Microsites", len(msids))

    # Get All Microsite Search Docs under pid
    doc_ids = get_all_search_documents(pid)
    logging.info("Found %s Search Documents", len(doc_ids))

    # Find Search Docs That Don't Correspond to an MSID
    docs_without_msid = [doc for doc in doc_ids if not doc in msids]
    logging.info("Found %s Orphaned Search Docs", len(docs_without_msid))
    if docs_without_msid:
        logging.error("There shouldn't be orphaned search docs, but the docs with these msids were: %s",
                      docs_without_msid)

    # Find Microsites That Don't Have a Corresponding Search Document
    msids_without_doc = [msid for msid in msids if not msid in doc_ids]
    logging.info("Found %s Orphaned MSIDS", len(msids_without_doc))
    if msids_without_doc:
        logging.error("There shouldn't be msids without search docs, but these msids have no docs: %s",
                      msids_without_doc)

    if not dry_run:
        # Kill Orphaned Search Docs
        for doc in docs_without_msid:
            defer_url = DEFERRED_ROOT + 'MicroSite/RemoveSearchDocument/'
            deferred.defer(remove_microsite_search_document, doc, pid, _url=defer_url, _queue='documents')

        # Give Search Doc To Childless Microsites
        for msid in msids_without_doc:
            defer_url = DEFERRED_ROOT + 'MicroSite/AddSearchDocument/'  # deferred tasks makes this script real fast
            deferred.defer(add_microsite_search_document, msid, pid, _url=defer_url, _queue='documents')
    logging.info("Done working on PID: '%s'", pid)


def delete_orphaned_search_documents():
    """
    Deletes all the search documents.
    """
    logging.info("Looking Up All Partners")
    partners = Partner.lookup_all_partners()
    logging.info("Collecting Pids")
    pids = [partner.pid for partner in partners]
    logging.info("Found %s Pids", len(pids))
    for pid in pids:
        logging.info("*"*80)
        logging.info("Working on PID: '%s'", pid)
        logging.info("\tLooking Up Search Documents")
        doc_ids = get_all_search_documents(pid, 100)
        logging.info("\tFound %s documents", len(doc_ids))
        if doc_ids:
            logging.info("\tLooking up corresponding sites")
            msids = get_all_msids(pid)
            logging.info("\tFound %s sites", len(msids))
            docs_without_sites = list(set(doc_ids) - set(msids))
            logging.info("\tFound %s documents without sites", len(docs_without_sites))
            for doc_id in docs_without_sites:
                logging.info("\t\tRemoving document: '%s'", doc_id)
                defer_url = DEFERRED_ROOT + 'MicroSite/RemoveSearchDocument/'
                deferred.defer(remove_microsite_search_document, doc_id, pid, _url=defer_url, _queue='documents')
        logging.info("Done working on PID: '%s'", pid)
    logging.info("Done")


def get_all_msids(pid):
    """
        Returns all the msids for the microsites under the given pid.
    """
    msids = [key.string_id() for key in Microsite.lookup_all_query(pid=pid).iter(batch_size=1000, keys_only=True)]
    return msids
