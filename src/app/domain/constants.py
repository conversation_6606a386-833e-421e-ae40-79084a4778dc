""" Some domain-layer constants. """


class Keys:
    """ Common keys. """
    DEFER_URL_ROOT = '/_ah/queue/deferred/'
    DEFAULT_TASK_RETRY = 3

    QUEUE_BLOB_CLEANUP = 'blob-cleanup'
    QUEUE_FETCH_POST_ID = 'fetch-post-id'

VENDASTA_FTP_URL = 'http://customerftp.vendasta.com:8080'
VENDASTA_FTP_POST_USERNAME = 'report_user'
VENDASTA_FTP_POST_PASSWORD = 'report_password'

BLINKER_EXPIRE_PARTNER = 'expire-partner-cache'
BLINKER_EXPIRE_HOST_PID_MAPPING = 'expire-host-pid-mapping-cache'
BLINKER_EXPIRE_HOST_SLUG_MSID_MAPPING = 'expire-host-slug-msid-mapping-cache'
BLINKER_EXPIRE_POST_PERMALINK_MAPPING = 'expire-post-permalink-mapping-cache'


class RudimentarySignaller:
    """ RudimentarySignaller is a replacement for blinker's NamedSignal; the latter has some
        weirdness when appstats is attempting to create a stacktrace.

    Important! This signaller makes no attempt to keep only weak references to its listeners. Thus, it is
    really important to connect only static listeners, or you will have some bizarre effects, and memory
    leaks.
    """

    def __init__(self, name):
        """ Initialize. """
        self.name = name
        self.__listeners = []

    def connect(self, fn):
        """ Connect a function to receive signals. """
        self.__listeners.append(fn)

    def send(self, message):
        """ Send a message to the listeners. """
        for listener in self.__listeners:
            listener(message)

# partner_expire_signal = NamedSignal(BLINKER_EXPIRE_PARTNER) # pylint: disable=C0103
partner_expire_signal = RudimentarySignaller(BLINKER_EXPIRE_PARTNER) # pylint: disable=C0103
host_pid_mapping_expire_signal = RudimentarySignaller(BLINKER_EXPIRE_HOST_PID_MAPPING) # pylint: disable=C0103
host_slug_msid_mapping_expire_signal = \
    RudimentarySignaller(BLINKER_EXPIRE_HOST_SLUG_MSID_MAPPING) # pylint: disable=C0103
post_permalink_mapping_expire_signal = \
    RudimentarySignaller(BLINKER_EXPIRE_POST_PERMALINK_MAPPING) # pylint: disable=C0103
