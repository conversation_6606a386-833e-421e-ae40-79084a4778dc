""" Coresdk v2 code for Listing Syndication """
import settings
from coresdk_v2.base import ListingClient
from settings import VSOCIAL_API, VSOCIAL_USER


def _get_listing_client():
    """
    :return: The ListingClient with the appropriate configuration
    """
    return ListingClient(
        VSOCIAL_USER,
        VSOCIAL_API,
        configuration=settings.ENVIRONMENT_NAME.lower()
    )


def lookup_listings(agid, source_id=None, include_good_listings=None, include_poor_listings=None,
                    include_not_done_listing=None, include_anchor_data_matches=None, listing_client=None):
    """
    Returns Listings based on Account Group ID

    :param agid: Account Group ID to search
    :param source_id: sourceId that correspond to data provider (should be int)
    :param include_good_listings: flag to include listings that the system or the user have determined to be a
                                best match
    :param include_poor_listings: flag to include listings that the system or the user have determined not to be a
                                match
    :param include_not_done_listing: flag to include listings that haven't been processed fully by the system
    :param include_anchor_data_matches: indicates whether to include anchor data match details
    """
    client = listing_client or _get_listing_client()
    return client.lookupListingsV2(agid=agid, sourceId=source_id,
                                   includeGoodListingsFlag=include_good_listings,
                                   includeNotDoneListingsFlag=include_not_done_listing,
                                   includePoorListingsFlag=include_poor_listings,
                                   includeAnchorDataMatchesFlag=include_anchor_data_matches)


def lookup_listing_history(agid, lid, fromDate=None, thruDate=None, pageSize=None, listing_client=None):
    """
    Returns listing history based on agid and lid

    :param agid: Account Group ID
    :param lid: Listing ID
    :param pageSize: Number of items to be returned at a time
    :param fromDate: The listing history will only include changes after this date. (format: '2012-12-13')
    :param thruDate: The listing history will only include changes before this date. (format: '2012-12-13')
    """
    client = listing_client or _get_listing_client()
    return client.listingHistoryV2Async(agid=agid, lid=lid, fromDate=fromDate, thruDate=thruDate, pageSize=pageSize)

def lookup_listing_history_deltas(lid, fromDate=None, thruDate=None):
    """
    Returns listing history changes for each field for a given listing id.

    :param lid: Listing ID
    :param fromDate: The listing history will only include changes on or after this date. (format: '2012-12-13')
    :param thruDate: The listing history will only include changes on or before this date. (format: '2012-12-13')
    """
    client = _get_listing_client()
    return client.listingHistoryDeltasV2(lid=lid, fromDate=fromDate, thruDate=thruDate)
