"""
Module to get Listing Sync Pro information from Core Services.
"""
import datetime
import logging

from google.appengine.api.taskqueue import TaskAlreadyExistsError, TombstonedTaskError
from google.appengine.ext.deferred import deferred

from app.domain.constants import Keys
from app.models.microsite import AccountGroupModel
from coresdk_v2.base import ListingSyncProClient, NotFound404Error
import settings
import statsd
from listing_sync_pro_sdk._internal.api import ServiceProvider
from listing_sync_pro_sdk.api import ListingSyncProServiceClient
from vax.errors import TransportException
from vax.utils.env import env_from_app_engine_env
from vobject.objects.account_group import ListingSyncProServiceProviders


def __get_listing_sync_pro_client():
    """
    Create the Listing Sync Pro api client.
    """
    return ListingSyncProClient(
        settings.VSOCIAL_USER,
        settings.VSOCIAL_API,
        configuration=settings.ENVIRONMENT_NAME.lower()
    )


def get_listing_sync_pro_directories(account_group_id):
    """
    Get Listing Sync Pro directory information for the provided account group's location.
    """
    client = __get_listing_sync_pro_client()
    try:
        return client.searchForLocationV2(agid=account_group_id)
    except NotFound404Error:
        logging.warning('Uberall directory data not found for account group ' + account_group_id + '.')
        return {}


def get_listing_sync_pro_directory_statuses_for_activated_lsp(account_group_id):
    """
    Get Listing Sync Pro directory statuses for the provided account group assuming LSP is activated
    """
    client = __get_listing_sync_pro_client()
    try:
        return client.getDirectoryStatusesV2(accountGroupId=account_group_id)
    except NotFound404Error:
        logging.warning('Listing Sync Pro directory data not found for account group %s.', account_group_id)
        return {}


def get_listing_sync_statuses_from_microservice(account_group_id):
    """
    Get Listing Sync Pro statuses for an account group from the listing-sync-pro microservice
    """
    function = 'get_listing_sync_statuses_from_microservice'
    lsp_client = ListingSyncProServiceClient(env_from_app_engine_env())
    try:
        response = lsp_client.list_statuses_by_business(account_group_id)
    except TransportException as e:
        logging.warning('An error getting listing sync statues occurred')
        _send_datadog_tick(False, function, 'failed_to_call_lsp_microservice')
        raise e

    logging.debug('Statuses from LSP microservice:\n%s', response)
    task_hour = datetime.datetime.utcnow().strftime('%Y%m%dT%H')
    task_name = f'trigger-stats-collection-{account_group_id}-{task_hour}'
    try:
        deferred.defer(_trigger_lsp_stats_collection_if_necessary, account_group_id, response.source_statuses,
                       _queue='default', _url=Keys.DEFER_URL_ROOT + 'trigger-stats-collection/',
                       _name=task_name)
    except (TaskAlreadyExistsError, TombstonedTaskError):
        logging.info("Task with name '%s' cannot be enqueued. Not fetching new stats.", task_name, exc_info=True)
        _send_datadog_tick(False, function, 'could_not_defer_task')

    _send_datadog_tick(True, function, 'it_worked')
    return response.to_dict(to_json_str=True)

def _trigger_lsp_stats_collection_if_necessary(account_group_id, statuses):
    """
    If the stats we are getting for an account are older than 12 hours, trigger a new stats collection
    :param account_group_id: account to get stats for
    :param statuses: List of listing_sync_pro_sdk._internal.api.SourceStatus objects.
    """
    # pylint: disable=too-many-return-statements
    function = 'trigger_stats_collection'
    if len(statuses) < 1:
        logging.info('No statuses to check the date for.')
        _send_datadog_tick(False, function, 'no_status_records')
        return

    logging.info('statuses[0]: %s', statuses[0])
    if statuses[0].created == None:
        logging.warning('Bug around none created times still exists. Not collecting stats.')
        _send_datadog_tick(False, function, 'created_date_is_none')
        return

    if (datetime.datetime.utcnow() - statuses[0].created) < datetime.timedelta(hours=12):
        _send_datadog_tick(True, function, 'collection_not_triggered')
        return

    account_group = AccountGroupModel.get(account_group_id)
    if not account_group:
        logging.warning('Account group %s not found trying to fetch new LSP stats.')
        _send_datadog_tick(False, function, 'account_group_not_found')
        return
    if not account_group.listing_sync_pro:
        logging.warning('Account group %s does not have an LSP record.')
        _send_datadog_tick(False, function, 'account_group_no_lsp')
        return
    if not account_group.listing_sync_pro.is_active:
        logging.warning('Account group %s does not have an LSP record.')
        _send_datadog_tick(False, function, 'account_group_lsp_inactive')
        return
    if account_group.listing_sync_pro.sync_service_provider == ListingSyncProServiceProviders.UBERALL:
        service_provider = ServiceProvider.SERVICE_PROVIDER_PROVIDER_0.value
    elif account_group.listing_sync_pro.sync_service_provider == ListingSyncProServiceProviders.YEXT:
        service_provider = ServiceProvider.SERVICE_PROVIDER_PROVIDER_1.value
    else:
        logging.warning('Unknown LSP service provider on account group: %s',
                        account_group.listing_sync_pro.sync_service_provider)
        _send_datadog_tick(False, function, 'account_group_bad_lsp_provider')
        return

    lsp_client = ListingSyncProServiceClient(env_from_app_engine_env())
    try:
        lsp_client.trigger_stats_collection(account_group_id, account_group.partner_id, service_provider,
                                            account_group.service_area_business_flag)
    except TransportException as e:
        logging.error('Failed to call to listing sync pro microservice to collect stats. %s', e)
        _send_datadog_tick(False, function, 'failed_to_call_lsp_microservice')
    else:
        _send_datadog_tick(True, function, 'it_worked')


def _send_datadog_tick(successful, function, reason):
    """
    Send an lsp_microservice datadog tick
    :param successful: boolean, whether the action being performed was successful.
    :param function: The name of the function that was being run.
    :param reason: The reason the function failed.
    """
    result = 'result:success' if successful else 'result:error'
    reason_tag = f'reason:{reason}'
    function_tag = f'function:{function}'
    metric = statsd.StatsDMetric('lsp_microservice', tags=[result, function_tag, reason_tag])
    statsd.tick_metric(metric)
