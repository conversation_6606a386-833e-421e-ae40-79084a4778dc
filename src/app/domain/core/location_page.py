"""core My Listing domain"""
import settings
from app.models.microsite import Microsite
from coresdk_v2.base import LocationPageClient
from settings import VSOCIAL_USER, VSOCIAL_API


class MicroSiteNotInDataStoreError(Exception):
    """ MicroSite is not found in datastore """
    pass


def remap_location_page_url(pid, msid, url):
    """
    Remap My Listing url in core
    """
    site = Microsite.get_by_msid(msid, pid=pid)
    if site:
        core_client = LocationPageClient(VSOCIAL_USER, VSOCIAL_API, settings.ENVIRONMENT_NAME.lower())
        core_client.remapLocationPageUrlV2(agid=site.agid, locationPageUrl=url)
    else:
        raise MicroSiteNotInDataStoreError(f"Missing MicroSite for {pid!r}, {msid!r}.")
