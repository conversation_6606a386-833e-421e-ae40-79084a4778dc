""" MS wrapper to access vTaxonomy core services. """

from coresdk import VTaxManager
from coresdk.keys import API_KEY

import settings


BUSINESS_CATEGORY_TO_TAX_ID_MAPPING = {
    'AGRI': 'mineag',
    'ARTS': 'arts',
    'AUTO': 'auto',
    'CARE': 'beautysvc',
    'CON': 'homeservices',
    'EDU': 'education',
    'FIN': 'financialservices',
    'FOOD': 'restaurants',
    'GOV': 'publicservicesgovt',
    'HOME': 'homeservices',
    'INDS': 'industgoodsmanu',
    'LEGAL': 'professional',
    'MED': 'health',
    'OTH': 'other',
    'PRO': 'professional',
    'REAL': 'realestate',
    'REC': 'active',
    'RET': 'shopping',
    'TRANS': 'transportation',
    'TRAVEL': 'hotelstravel'
}


def _get_vtax_manager():
    """
    Obtain the VTax Manager.
    """
    return VTaxManager(
        settings.VSOCIAL_USER,
        settings.VSOCIAL_API,
        target=settings.ENVIRONMENT_NAME.lower()
    )


def lookup_full_taxonomy(vt_manager=None):
    """
    Look up the complete taxonomy.
    :param vt_manager: the VTaxManager.
    :return: the entire taxonomy.
    """
    manager = vt_manager or _get_vtax_manager()
    taxonomies = manager.getFullTaxonomy()
    return sorted(taxonomies, key=lambda x: x[API_KEY.TAX_NAME])


def lookup_root_taxonomy():
    """
    Lookup the root nodes
    """
    root_nodes = []
    taxonomy = lookup_full_taxonomy()
    for node in taxonomy:
        root_nodes.append({
            API_KEY.TAX_NAME: node[API_KEY.TAX_NAME],
            API_KEY.TAX_ID: node[API_KEY.TAX_ID]
        })
    return root_nodes


def lookup_taxonomy_matching_legacy_category(category,
                                             vt_manager=None):
    """
    Look up the taxonomy node for the given legacy business category.
    :param category: the legacy business category (eg "AGRI").
    :param vt_manager: the VTaxManager.
    :return: taxonomy node matching the legacy category.
    """
    manager = vt_manager or _get_vtax_manager()
    taxonomy = manager.lookupByLegacyCategoryId(category)
    return taxonomy


def lookup_taxonomy_node(taxonomy_id, vt_manager=None):
    """
    Look up the taxonomy node for the given taxonomy id.
    :param taxonomy_id: the taxonomy ID (eg "beautysvc:hair:blowoutservices").
    :param vt_manager: the VTaxManager.
    :return: the matching taxonomy node.
    """
    manager = vt_manager or _get_vtax_manager()
    taxonomy = manager.getTaxonomyNode(taxonomy_id)
    return taxonomy