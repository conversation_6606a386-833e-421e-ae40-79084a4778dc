"""
Helpers to notify teams in Slack channels.
"""

from google.appengine.ext import deferred
import logging

from vax.utils.env import env_from_app_engine_env
from vax.environment import Environment


def notify_listings_team(message):
    """
    Args:
        message (string): Plain text message to send.
    """
    eclectic_alerts_channel = "*****************************************************************************"

    environment = env_from_app_engine_env()
    if environment != Environment.PROD:
        logging.info('Would have Slacked: %s', message)
        return

    vslack_client = VSlack(eclectic_alerts_channel)
    deferred.defer(vslack_client.notify_slack, message=message)
