""" Unified Billing domain module. """
import time
from datetime import datetime
from app.domain.validation import require_args
from app.views.api.v3.keys import API_KEY
from settings import ENVIRONMENT_NAME


@require_args
def check_backup_complete(report_month):
    """ Ensure the backup data is up to date for the given report month.
    :param report_month: A year and month to lookup backup for for. Eg. '2013-08'
    :return: True if the backup is up to date for the given report_month otherwise False
    """
    # Imports need to be done after app initializes so it can find the app id
    from vbackup.models import BACKUP_COMPLETED
    from vbackup.status import BackupStatus

    report_date = datetime.fromtimestamp(time.mktime(time.strptime(report_month, API_KEY.REPORT_MONTH_FORMAT)))
    if report_date.month == 12:
        report_date = datetime(report_date.year + 1, 1, 1, 6)
    else:
        report_date = datetime(report_date.year, report_date.month + 1, 1, 6)

    recent_backup = BackupStatus.lookup_most_recent_backup()
    return recent_backup and recent_backup.started >= report_date and recent_backup.status == BACKUP_COMPLETED


@require_args
#disable=WV001
def compile_query(report_month, query_deleted=False):
    """ Compile a query for looking up billable accounts for the given report date.
    :param report_month: A year and month to query accounts for. Eg. '2013-08'
    :param query_deleted: Indicates whether to query for just deleted accounts or not.
    :return: A query for looking up billable accounts.
    """
    # need to include environment to facilitate cross-project querying
    env_name = 'microsite-%s' % ENVIRONMENT_NAME.lower()
    table_name = 'datastore.MicrositeTombstone' if query_deleted else 'datastore.MicrositeModel'
    kwargs = {
        'report_month': report_month,
        'created_name': 'original_created' if query_deleted else 'created',
        'month_start': """TIMESTAMP('%s-02 00:00:00')""" % report_month,
        'month_end': """DATE_ADD(TIMESTAMP('%s-01 00:00:00'), 1, 'MONTH')""" % report_month,
        'deleted_date': 'STRFTIME_UTC_USEC(created, \'%Y-%m-%dT%H:%M:%SZ\')'
            if query_deleted else "''",
        'model_name': '{}:{}'.format(env_name, table_name),
        'billable_logic': 'is_lite = true',
        'is_pb_lite': 'is_lite = true',
        'agid': "''" if query_deleted else 'agid'
    }

    # Note about params
    # productConfigurationId: product configuration is different for Hearst
    # customerFax: we don't track a fax number
    # accountGroupId: account group customer identifier column is here for future functionality, probably just empty
    #                 strings for now
    query = """SELECT
            STRFTIME_UTC_USEC(DATE_ADD(USEC_TO_TIMESTAMP(NOW()), -20, 'DAY'), '%Y-%m') as [reportMonth],
            'MS' as [productId],
            pid as [productPartnerId],
            CASE
                WHEN pid = 'HMSI' THEN 'landing-page-only'
                ELSE 'standard'
            END as [productConfigurationId],
            msid as [accountId],
            customer_identifier as [customerIdentifier],
            billing_code as [billingCode],
            market_id as [marketId],
            IF({billable_logic}, 'N', 'Y') as [billable],
            STRFTIME_UTC_USEC({created_name}, '%Y-%m-%dT%H:%M:%SZ') as [accountCreationDate],
            {deleted_date} as [accountDeletionDate],
            name as [customerName],
            CASE
                WHEN address2 IS NOT NULL THEN CONCAT(CONCAT(address1, ', '), address2)
                ELSE address1
            END as [customerAddress],
            city as [customerCity],
            state as [customerState],
            zipcode as [customerZipcode],
            country as [customerCountry],
            email as [customerEmail],
            phone as [customerPhone],
            '' as [customerFax],
            {agid} as [accountGroupId],
            '' as [accountGroupCustomerIdentifier],
            account_origin as [accountOrigin],
            IF({is_pb_lite}, 'Y', 'N') as [isPbLite]
        FROM
            [{model_name}]
        WHERE
            {created_name} <= {month_end}"""
    if query_deleted:
        query += """ AND
            created > {month_start}"""

    # TODO: Remove this part of the query when doing MS-1232
    query += """ AND
            __key__.namespace NOT LIKE 'backup-%'"""

    return query.format(**kwargs).replace("\n", "")


@require_args
def get_billable_accounts_queries(report_month):
    """ Return a query for the billable accounts. """
    query = compile_query(report_month)
    query_deleted = compile_query(report_month, query_deleted=True)

    return [
        {'query': query},
        {'query': query_deleted}
    ]


@require_args
def get_current_accounts_queries(report_month):
    """ Return a query for current accounts. """
    query = compile_query(report_month)
    return [
        {'query': query}
    ]

def get_product_configurations():
    """ Return a list of product configurations. """
    return [
        {'productConfigurationId': 'standard'},
        {'productConfigurationId': 'social-posting'},
        {'productConfigurationId': 'landing-page-only'}
    ]
