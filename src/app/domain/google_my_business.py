""" Domain logic for Google My Business """
import calendar
import datetime
import logging

from google.appengine.ext import ndb

import settings
from coresdk_v2.base import GoogleMyBusinessClient, InternalServer500Error
from app.execreporthelpers import \
    get_most_recent_report_date, build_call_to_action_payload, \
    build_number_with_change_payload, ReportFrequencies
from settings import VSOCIAL_USER, VSOCIAL_API


class AbstractGoogleMyBusinessClient:
    """ Helper for managing Core GoogleMyBusinessClient connections """
    def __init__(self, spid):
        self.vs_user = VSOCIAL_USER
        self.vs_api_key = VSOCIAL_API
        self.spid = spid
        self.gmb_client = GoogleMyBusinessClient(self.vs_user, self.vs_api_key,
                                                 configuration=settings.ENVIRONMENT_NAME.lower())

    def get_connected_location(self):
        """ Get the connected GMB location """
        return self.get_connected_location_async().get_result()

    @ndb.tasklet
    def get_connected_location_async(self):
        """ Get the connected GMB location """
        location = None
        try:
            location = yield self.gmb_client.getGoogleMyBusinessLocationConnectionV2Async(
                self.spid,
                refreshFromGoogleFlag=True
            )
        except InternalServer500Error:
            logging.exception("Failed to get a location from Google api.")
        except Exception:
            # We are currently seeing some auth issues, want to get a patch in to allow microsites to avoid 500
            # error in that case
            logging.exception("Unexpected exception trying to get a location from google my business")
        raise ndb.Return(location)

    def has_gmb_authed(self):
        """Try to retrieve the GMB location for an account. Returns True if found, False otherwise"""
        return self.get_connected_location() is not None


class GoogleMyBusinessSync(AbstractGoogleMyBusinessClient):
    """ Sync with Google My Business """

    # This is unofficially supported by Google, and subject to change:
    # https://www.en.advertisercommunity.com/t5/Google-My-Business-API/HTTP-Link-URL-to-GMB-Dashboard-for-a-specific-location/td-p/1803227
    LOCATION_URL_TEMPLATE = 'https://business.google.com/edit/l/u{0}'

    def get_accounts_associated_with_google_user(self, google_user_id, cursor=None, page_size=None):
        """ Get a list of the user's GMB accounts from Core """
        return self.gmb_client.getGoogleMyBusinessPagedAccountsFromGoogleV2(
            googleUserId=google_user_id,
            socialProfileId=self.spid,
            cursor=cursor,
            pageSize=page_size
        )

    def get_locations_associated_with_account(self, google_user_id, account_path_name, cursor=None, page_size=None):
        """ Get all of the GMB account's locations from Core """
        return self.gmb_client.getGoogleMyBusinessPagedLocationsFromGoogleV2(
            socialProfileId=self.spid,
            googleUserId=google_user_id,
            accountPathName=account_path_name,
            cursor=cursor,
            pageSize=page_size
        )

    def connect_location_to_social_profile(self, location_path_name, google_user_id):
        """ Create a GMB location in Core """
        return self.gmb_client.connectGoogleMyBusinessLocationV2(
            locationPathName=location_path_name,
            googleUserId=google_user_id,
            socialProfileId=self.spid
        )

    def disconnect_location_from_social_profile(self, location_path_name, google_user_id):
        """ Disconnect a GMB location by removing it from Core """
        self.gmb_client.disconnectGoogleMyBusinessLocationV2(
            googleUserId=google_user_id,
            locationPathName=location_path_name,
            socialProfileId=self.spid
        )

    def set_sync_flag(self, syncing_location):
        """
        Sets the social sync flag for Google My Business
        """
        location = self.gmb_client.getGoogleMyBusinessLocationConnectionV2(self.spid)
        location_path_name = location.get('locationPathName')
        return self.gmb_client.enableDisableSyncGoogleMyBusinessLocationV2(
            googleUserId=location.get('googlePlusUserId'),
            locationPathName=location_path_name,
            socialProfileId=self.spid,
            syncSettingFlag=location_path_name == syncing_location
        )


class GoogleMyBusinessInsightsData(AbstractGoogleMyBusinessClient):
    """
    Collect Insights data gathered by Core
    """

    # Descriptions taken from
    # https://docs.google.com/document/d/1Hj6EyMKH8qUAofQxYvNDrbj9UKgyt4jOhEPjq6EX1bo/edit#
    GMB_INSIGHTS_EXECUTIVE_REPORT_STATS = {
        'views_maps': {
            'title': 'Map Views',
            'description': 'The number of times your listing was viewed on Google.'
        },
        'queries_indirect': {
            'title': 'Discovery Searches',
            'description':
                'The number of customers who found your listing searching for a category, product or service.'
        },
        'actions_website': {
            'title': 'Website Clicks',
            'description': 'The number of times customers clicked on your website from your listing.'
        },
        'actions_phone': {
            'title': 'Phone Calls',
            'description': 'The number of times customers called your business.'
        },
        'views_search': {
            'title': 'Searches',
            'description': 'The number of times your listing was viewed on Google Search.'
        },
        'actions_driving_directions': {
            'title': 'Driving Directions Requests',
            'description': 'The number of times customers requested driving directions to your business.'
        },
        'queries_direct': {
            'title': 'Direct Searches',
            'description':
                'The number of customers who found your listing searching for your business name or address.'
        },
    }

    def __init__(self, spid):
        """ init """
        super().__init__(spid)
        self.frequency = None
        self.executive_report_entry = None
        self.formatted_exec_report_entry = None
        self.insights_data = None
        self.report_date = None
        self.account_group_id = None

    def get_data_for_chart(self, start_date, end_date):
        """
        Call Core for insights data
        """
        return self.gmb_client.getInsightsGraphDataTimePeriodV2(
            socialProfileId=self.spid,
            startDateDateTime=start_date,
            endDateDateTime=end_date
        )

    def get_stats_start_date(self):
        """
        Call Core to get the earliest date for which we have stats
        """
        return self.gmb_client.getInsightsStartDateV2(self.spid)

    def gather_for_date(self, report_date):
        """
        Gather data from Core using the GoogleMyBusinessClient.
        """
        if self.frequency is None:
            raise ValueError('Call for_frequency and set a frequency before calling this method')
        if not report_date:
            report_date = get_most_recent_report_date(self.frequency)
        elif not isinstance(report_date, datetime.date):
            raise ValueError('report_date must be a date')
        report_date = datetime.datetime.combine(report_date, datetime.datetime.min.time())
        self.report_date = report_date

        # For now we have to modify the end date due to Google taking three days to gather stats. Hopefully one day
        # this line can be removed.
        end_date = report_date - datetime.timedelta(days=4)
        start_date = report_date - datetime.timedelta(days=10)
        if self.frequency == 'monthly':
            _, days_in_month = calendar.monthrange(report_date.year, report_date.month)
            start_date = report_date - datetime.timedelta(days=days_in_month + 3)

        self.insights_data = self.gmb_client.calculateInsightsByTimePeriodV2(
            socialProfileId=self.spid,
            startDateTime=start_date,
            endDateTime=end_date
        )
        self.account_group_id = self.insights_data['account_group_id']

        return self

    def format_for_exec_report(self):
        """
        Format and return the insights data retrieved from Core.
        Currently this data has a template type of "NUMBER_WITH_CHANGE".
        :return: Tuple of account group id and insights data, formatted for exec report
        """
        self.formatted_exec_report_entry = build_gmb_insights_exec_report_entry(
            self.account_group_id,
            self.insights_data
        )

        return self

    def for_frequency(self, frequency):
        """
        Set the frequency for the request
        :param frequency: string, one of 'weekly' or 'monthly'
        :return: self, for chaining
        """
        if frequency not in ReportFrequencies.all():
            raise ValueError('frequency must be one of {}'.format(','.join(ReportFrequencies.all())))
        self.frequency = frequency
        return self


def get_insights_data_for_social_profile_frequency_and_date(social_profile_id, frequency, report_date):
    """
    Helper method to do the chaining for the view layer to call.
    :param social_profile_id: Social profile to gather stats on
    :param frequency: Frequency for the report. 'weekly' or 'monthly'. Will raise an exception if it isn't one of those.
    :param report_date: Date to gather stats for. End of the week for weekly stats, beginning of the month for monthly
    :return: A GoogleMyBusinessInsightsData object
    """
    return (
        GoogleMyBusinessInsightsData(social_profile_id)
        .for_frequency(frequency)
        .gather_for_date(report_date)
        .format_for_exec_report()
    )


@ndb.tasklet
def get_gmb_insights(account_group_id, social_profile_id, _, frequency, report_date):
    """
    Get Google My Business Insights data gathered by CS. Send the CTA if they don't have GMB
    authed, otherwise send their GMB insights data.
    """
    gmb_location = yield GoogleMyBusinessSync(social_profile_id).get_connected_location_async()

    if gmb_location:
        insights_data = get_insights_data_for_social_profile_frequency_and_date(
            social_profile_id,
            frequency,
            report_date
        )
        raise ndb.Return(insights_data.formatted_exec_report_entry)

    cta_data = build_gmb_call_to_action_exec_report_entry(account_group_id)

    raise ndb.Return(cta_data)


def build_gmb_insights_exec_report_entry(account_group_id, data):
    """
    Build gmb insights data in format VDC endpoint expects for Executive Report
    """
    report_data = []
    for stat in data['gmb_stats']:
        if stat['name'] not in GoogleMyBusinessInsightsData.GMB_INSIGHTS_EXECUTIVE_REPORT_STATS:
            continue

        payload = build_number_with_change_payload(
            stat['name'],
            GoogleMyBusinessInsightsData.GMB_INSIGHTS_EXECUTIVE_REPORT_STATS[stat['name']]['title'],
            str(stat['current_value']),
            str(stat['change']),
            'HALF',
            description=GoogleMyBusinessInsightsData.GMB_INSIGHTS_EXECUTIVE_REPORT_STATS[stat['name']]['description'],
            next_url=f'/edit/account/{account_group_id}/google-insights/',
        )
        report_data.append(payload)

    return report_data


def build_gmb_call_to_action_exec_report_entry(account_group_id):
    """
    Build gmb insights data in format VDC endpoint expects for Executive Report
    """
    report_data = []

    next_url = f'/edit/account/{account_group_id}/google-insights/'

    payload = build_call_to_action_payload(
        'gmbInsightsCallToAction',
        'EXECUTIVE_REPORT.GMB.TITLE',
        'EXECUTIVE_REPORT.GMB.PRESCRIPTION',
        next_url,
        'COMMON.ACTION_LABELS.CONNECT_NOW'
    )

    report_data.append(payload)

    return report_data
