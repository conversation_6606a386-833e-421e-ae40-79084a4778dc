""" Cleans up any images, mappings, and posts associated with failed scheduled posts """
import datetime
import logging
from google.appengine.api.blobstore.blobstore import delete as delete_blob
from app.domain.exceptions import SocialProfileNotFoundException

from coresdk import API_KEY
from fantasm.action import FSMAction

import settings
from app.constants import ImageCleanup as Const
from app.domain.post import delete_post
from app.domain.social import SitesVSocial
from app.models.post import PostidBlobMapping, Post


class CleanupFailedPostData(FSMAction):
    """
    Cleans up data for a partner
    """

    def execute(self, context, obj):
        """ Do it """
        pid = context[Const.PID_FSM_KEY].string_id()
        logging.info("image cleanup for %s", pid)

        # Get all the entities with a scheduled date that has passed. No scheduled date means a complete mapping.
        incomplete_mappings = PostidBlobMapping.lookup_by_passed_scheduled_date(pid=pid)

        # Check each mapping
        for mapping in incomplete_mappings:
            logging.info('Checking mapping: %s', mapping.social_post_id)
            longform_post_id = None
            best_status = None
            # Check if any social posts succeeded
            manager = SitesVSocial(
                mapping.msid,
                user=settings.VSOCIAL_USER, api_key=settings.VSOCIAL_API, target=settings.ENVIRONMENT_NAME.lower()
            )

            try:
                post = manager.get_social_post(mapping.social_post_id)
            except SocialProfileNotFoundException as e:
                logging.warning('Social Profile not found for social post "%s" for pid "%s". Original message: %s',
                                mapping.social_post_id, pid, e.args[0])
                post = None

            logging.info('post data: %s', post)
            if post:
                post_status = post.get(API_KEY.POST_STATUS, {})
                for key, value in post_status.items():
                    status = value.get(Const.POST_STATUS_KEY, 'could not get status')
                    if status == Const.SOCIAL_POST_SUCCESS:
                        best_status = status
                        # Get the long form post id if a post was made to listing builder
                        if key.startswith(Const.MICROSITE_POST_TAG):
                            longform_post_id = value.get(API_KEY.SOCIAL_POST_ID)
                            break
                    elif status == Const.SOCIAL_POST_PENDING:
                        if best_status != Const.SOCIAL_POST_SUCCESS:
                            best_status = status
                    else:
                        if best_status not in [Const.SOCIAL_POST_SUCCESS, Const.SOCIAL_POST_PENDING]:
                            best_status = status
            else:
                best_status = Const.SOCIAL_POST_FAILURE

            # Successful, so remove the scheduled date and make the post visible
            if best_status == 'success':
                logging.info('Post was successful')
                mapping.scheduled_date = None
                mapping.post_id = longform_post_id or mapping.post_id
                mapping.put()

                post_with_images = Post.build_key(post_id=mapping.post_id, pid=pid).get()
                post_with_images.hidden = False
                post_with_images.put()

            # Still trying to post, so check back later
            elif best_status == Const.SOCIAL_POST_PENDING:
                logging.info('Post is pending')
                mapping.scheduled_date = datetime.datetime.utcnow() + datetime.timedelta(days=1)
                mapping.put()

            # Failed to post, so remove images, image to post mappings, and image posts
            elif best_status in [Const.SOCIAL_POST_FAILED, Const.SOCIAL_POST_FAILURE, Const.SOCIAL_POST_DELETED]:
                logging.info('Post failed')
                # Remove actual blob
                delete_blob(mapping.blobkey)
                # If there is a post id, then also remove the post associated with this image
                if mapping.post_id:
                    delete_post(mapping.post_id, mapping.msid, pid=pid)
                # Remove mapping
                mapping.key.delete()
            elif best_status == Const.SOCIAL_POST_DELETE_FAILED:
                pass
            else:
                logging.warning("unidentified post status '%s' for social post id '%s'",
                                best_status, mapping.social_post_id)
