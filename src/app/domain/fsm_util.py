""" Utility states for Fantasm machines. """

from fantasm.action import NDBDatastoreContinuationFSMAction
from app.models.partner import Partner as PartnerModel
from app.models.page import Page as PageModel
from app.models.microsite import Microsite as MicrositeModel

class IteratePartners(NDBDatastoreContinuationFSMAction):
    """ Iterates through all the current partners, adding the pid key object to the context on 'partner_key'. """
    
    def getQuery(self, context, obj):
        """ Returns the query. """
        return PartnerModel.lookup_all_query()

    def getKeysOnly(self, context, obj):
        """ Whether the query should only return keys. """
        return True
        
    def execute(self, context, obj):
        """ Adds pid to the context. """
        key = obj.get('result')
        if not key:
            return
        context['partner_key'] = key
        return 'ok'
        
class IteratePages(NDBDatastoreContinuationFSMAction):
    """ Iterates through all the pages for a given 'pid_key', adding the page key to the context on 'page_key'. """
    
    def getQuery(self, context, obj):
        """ Returns the query. """
        partner_key = context['partner_key']
        return PageModel.lookup_all_query(pid=partner_key.string_id())

    def getKeysOnly(self, context, obj):
        """ Whether the query should only return keys. """
        return True

    def execute(self, context, obj):
        """ Adds the pageid to the context. """
        key = obj.get('result')
        if not key:
            return
        context['page_key'] = key
        return 'ok'

class IterateSites(NDBDatastoreContinuationFSMAction):
    """ Iterates through all the sites for given partner, adding the sites key object to the context on 'site_key' """

    def getQuery(self, context, obj):
        """ return the query """
        pid = context['partner_key'].string_id()
        return MicrositeModel.lookup_all_query(pid=pid)

    def getKeysOnly(self, context, obj):
        """ Whether the query should only return keys. """
        return True

    def execute(self, context, obj):
        """ adds microsite key to context  """
        key = obj.get('result')
        if not key:
            return
        context['site_key'] = key

        return 'ok'