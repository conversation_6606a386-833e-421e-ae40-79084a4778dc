""" Domain logic for mapping between post and blob """
import logging
from google.appengine.ext import ndb
from google.appengine.ext.blobstore import BlobInfo

from app.models.post import PostidBlobMapping as PostidBlobMappingModel
from app.domain.validation import require_args


@require_args
def add_postid_blob_mapping(pid, msid, blobkey, serving_url, post_id=None, social_post_id=None, scheduled_date=None):
    """
    Adds a new post id blob mapping

    @param pid: A partner identifier
    @param msid: A microsite identifier
    @param blobkey: Blob key which needs to be mapped
    @param serving_url: Url of the blob where it can be accessed
    @param post_id: Post id.  Either this or social_post_id must be provided.
    @param social_post_id: Social post id from the core.  Either this or post_id must be provided.
    """
    if isinstance(blobkey, str):
        blobkey = ndb.BlobKey(blobkey)

    blobinfo = BlobInfo.get(blobkey)
    if not blobinfo:
        logging.warning("Blob does not exist with key: %s", blobkey)
        return

    old_mappings = PostidBlobMappingModel.lookup_by_blobkey(blobkey, pid=pid)
    for old_mapping in old_mappings:
        if old_mapping and (old_mapping.post_id == post_id or old_mapping.social_post_id == social_post_id):
            logging.error("Blob already mapped to post id: %s and social post id: %s",
                          old_mapping.post_id, old_mapping.social_post_id)
            return

    key = PostidBlobMappingModel.build_key(pid=pid)

    kwargs = {}
    if post_id:
        kwargs['post_id'] = post_id
    if social_post_id:
        kwargs['social_post_id'] = social_post_id
    if not len(kwargs):
        raise ValueError('Either post_id or social_post_id must be defined.')

    mapping = PostidBlobMappingModel(
        key=key, msid=msid, blobkey=blobinfo.key(), serving_url=serving_url, scheduled_date=scheduled_date, **kwargs
    )
    mapping.put()

    return PostidBlobMapping.from_model(mapping)


@require_args
def update_scheduled_date(pid, post_id, new_scheduled_date, social_post_id=None):
    """
    Updates the mapping completing check date for all mappings with the given social post id

    @param pid: A partner identifier
    @param msid: A microsite identifier
    @param social_post_id: Social post id from the core. Not changed if not passed.
    """
    mappings = PostidBlobMappingModel.lookup_by_post_id(post_id, pid=pid) or []
    for mapping in mappings:
        mapping.scheduled_date = new_scheduled_date
        mapping.social_post_id = social_post_id or mapping.social_post_id
    ndb.put_multi(mappings)


@require_args
def get_postid_blob_mapping(key):
    """ Retrieves a PostidBlobMapping for a given key. """
    entity = key.get()
    return PostidBlobMapping.from_model(entity)


@require_args
def process_mappings(pid, msid, social_post_id, blobs_info, scheduled_date):
    """
    Adds mapping between post identified by social_post_id with list in blob_info

    @param pid: A partner identifier
    @param msid: A microsite identifier
    @param social_post_id: Social post id from the core
    @param blobs_info: List of blob info in following format
                    [{
                        "blobkey": "wOIoDlo4ZlUwMrbGPSBiLg==",
                        "serving_url": "http://www.example.com/wOIoDlo4ZlUwMrbGPSBiLg=="
                    },
                    {
                        "blobkey": "a0koDlo4ZlUwMrbGPSBiLg==",
                        "serving_url": "http://www.example.com/a0koDlo4ZlUwMrbGPSBiLg=="
                    }]
    @param scheduled_date: The utc date after which the blob mappings are deleted if post was unsuccessful.
    """
    mappings = [mapping for mapping in (add_postid_blob_mapping(pid, msid, info.get('blobkey'), info.get('serving_url'),
                                                                social_post_id=social_post_id,
                                                                scheduled_date=scheduled_date) for info in blobs_info)
                if mapping]
    return mappings


class PostidBlobMapping:
    """ A post and blob mapping. """

    def __init__(self, **kwargs):
        """ Initialize."""
        if not kwargs.get('social_post_id') and not kwargs.get('post_id'):
            raise ValueError('One of post_id or social_post_id is required.')

        self.key = kwargs.get('key')
        self.msid = kwargs.get('msid')
        self.social_post_id = kwargs.get('social_post_id')
        self.post_id = kwargs.get('post_id')
        self.blobkey = kwargs.get('blobkey')
        self.serving_url = kwargs.get('serving_url')
        self.scheduled_date = kwargs.get('scheduled_date')

    @classmethod
    def from_model(cls, model):
        """ Builds the class from a model. """
        if not model:
            return None
        return cls(**model.to_dict())