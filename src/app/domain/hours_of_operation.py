""" This file contains the HoursOfOperation and Day classes """

import json
from app.domain.utils import convert_to_24_hour_format


class HoursOfOperation:
    """ Encapsulates a set of hours of operation. """

    WEEK_DAYS = ('monday', 'tuesday', 'wednesday', 'thursday', 'friday')
    WEEKEND_DAYS = ('saturday', 'sunday')
    DAYS = WEEK_DAYS + WEEKEND_DAYS
    NORMAL_CATEGORIES = ('open', 'close')
    SPLIT_CATEGORIES = ('open2', 'close2')
    CATEGORIES = NORMAL_CATEGORIES + SPLIT_CATEGORIES

    NOTE = 'note'
    HOLIDAY = 'holiday'
    OTHER = 'other'
    OPEN24 = 'open24'
    ADDITIONAL_HOURS = (HOLIDAY, OTHER, OPEN24)

    holiday = None
    other = None
    open24 = False

    def __init__(self, values_json=None, values_dict=None):
        """ Inflates an hours of operation object given a JSON string, or a dictionary. """

        for day in self.DAYS:
            setattr(self, day, Day())

        data = None
        if values_json:
            data = json.loads(values_json)
        elif values_dict:
            data = values_dict

        if data:
            # set additional hours
            for add_hour in self.ADDITIONAL_HOURS:
                if data.get(add_hour):
                    setattr(self, add_hour, data[add_hour])

            for day in self.DAYS:
                if data.get(day) is not None:
                    day_attr = getattr(self, day)
                    day_dict = data.get(day, {})

                    # set the note for the given day
                    if day_dict.get(self.NOTE):
                        day_attr.note = day_dict[self.NOTE]

                    # set category values
                    for cat in self.CATEGORIES:
                        value = day_dict.get(cat, None)
                        setattr(day_attr, cat, value)
                else:
                    day_attr = getattr(self, day)
                    day_attr.is_shown = False

    @property
    def has_entries(self):
        """ Returns a flag indicating if there are entries or not. """
        if self.has_day_entries:
            return True

        for cat in (self.HOLIDAY, self.OTHER):
            if getattr(self, cat) is not None:
                return True
        return False

    @property
    def has_day_entries(self):
        """ Returns a flag indicating if there are entries or not, but just for days. """
        for day in self.DAYS:
            day_attr = getattr(self, day)
            if day_attr.is_shown:
                if day_attr.note:
                    return True
                for cat in self.CATEGORIES:
                    if getattr(day_attr, cat) is not None:
                        return True
        return False

    @property
    def has_split_hours(self):
        """ Returns a flag indicating if there are split entries or not. """
        for day in self.DAYS:
            day_attr = getattr(self, day)
            for cat in self.SPLIT_CATEGORIES:
                if getattr(day_attr, cat) is not None:
                    return True
        return False

    def to_dict(self):
        """ Returns object as a dict. """
        d = {}

        if self.holiday:
            d[self.HOLIDAY] = self.holiday
        if self.other:
            d[self.OTHER] = self.other
        if self.open24 and (self.open24 == True or self.open24 == 1):
            d[self.OPEN24] = True

        for day in self.DAYS:
            day_attr = getattr(self, day)
            if day_attr.is_shown:
                d[day] = {}
                if day_attr.note:
                    d[day][self.NOTE] = day_attr.note

                for cat in self.CATEGORIES:
                    day_str = getattr(day_attr, cat)
                    if day_str:
                        d[day][cat] = day_str
        return d

    @property
    def json_value(self):
        """ Returns a JSON representation.

        Example 1: No entries

            {
              "monday": {}, "tuesday": {}, "wednesday": {}, "thursday": {}, "friday": {}, "saturday": {}, "sunday": {}
            }

        Example 2: Open during week, closed during weekend

            {
                "monday": {"open": "09:00", "close": "17:00"},
                "tuesday": {"open": "09:00", "close": "17:00"},
                "wednesday": {"open": "09:00", "close": "17:00"},
                "thursday": {"open": "09:00", "close": "17:00"},
                "friday": {"open": "09:00", "close": "17:00"},
                "saturday": {},
                "sunday": {},
            }

        Example 3: Split hours

            {
                "monday": {"open": "09:00", "close": "12:00", "open2": "13:00", "close2": "17:00"},
                ...
            }

        """
        json_obj = self.to_dict()
        return json.dumps(json_obj)

    def to_account_group_json(self):
        """ Returns HoO dict in account group HoO format """
        vbc_format = []

        for day in self.DAYS:
            day_attr = getattr(self, day)
            if day_attr.is_shown:
                opens = getattr(day_attr, 'open')
                closes = getattr(day_attr, 'close')
                opens2 = getattr(day_attr, 'open2')
                closes2 = getattr(day_attr, 'close2')
                note = day_attr.note
                day_title = day.title()
                if opens and closes:
                    vbc_format.append({'dayOfWeek': day_title, 'opens': opens, 'closes': closes, 'description': note})
                if opens2 and closes2:
                    vbc_format.append({'dayOfWeek': day_title, 'opens': opens2, 'closes': closes2, 'description': note})
                if note and not (opens and closes) and not (opens2 and closes2):
                    vbc_format.append({'dayOfWeek': day_title, 'description': note})

        if self.holiday:
            vbc_format.append({'dayOfWeek': ['PublicHolidays'],
                               'description': self.holiday})

        open24 = self.open24 or self.open24 == 1
        if self.other and not open24:
            vbc_format.append({'dayOfWeek': [],
                               'description': self.other})

        if open24:
            open24dict = {"dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
                          "opens": "00:00",
                          "closes": "00:00"}
            if self.other:
                open24dict["description"] = self.other

            vbc_format.append(open24dict)

        return json.dumps(vbc_format) if vbc_format else None

    def __str__(self):
        """ Returns the JSON representation. """
        return self.json_value


class Day:
    """ A single day for the hours of operation. """

    def __init__(self):
        """ Initialize. """
        self.note = None
        self.is_shown = True
        self.__attributes = {}
        for cat in HoursOfOperation.CATEGORIES:
            self.__attributes[cat] = None

    def _get_is_closed(self):
        """ Flag indicating if the business is closed on the given day. """
        if self.note:
            return False
        for cat in HoursOfOperation.CATEGORIES:
            if self.__attributes[cat] is not None:
                return False
        return True

    def _set_is_closed(self, value):
        """ Sets the is_closed flag, clearing any other open/close data."""
        if value:
            # setting is_closed to True; clear any existing values
            for cat in HoursOfOperation.CATEGORIES:
                self.__attributes[cat] = None
        else:
            # setting is_closed to False; ensure there is at least one existing value
            has_entry = False
            for cat in HoursOfOperation.CATEGORIES:
                if self.__attributes[cat]:
                    has_entry = True
                    break
            if not has_entry:
                raise ValueError('Open/Close data required to set is_closed to False.')

    def __getattr__(self, name):
        """ Retrieves the attribute. """
        if name == 'is_closed':
            return self._get_is_closed()
        if name in HoursOfOperation.CATEGORIES:
            #Ensures that the hours of operation is properly converted to 24-hour format
            return convert_to_24_hour_format(self.__attributes[name])
        return object.__getattr__(self, name)

    def __setattr__(self, name, value):
        """ Sets an attribute. """
        if name == 'is_closed':
            self._set_is_closed(value)
            return
        if name in HoursOfOperation.CATEGORIES:
            self.__attributes[name] = value and value or None
            return
        object.__setattr__(self, name, value)
