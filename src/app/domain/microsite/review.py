"""
microsite review domain
"""
from google.appengine.ext.deferred import deferred, PermanentTaskFailure
import logging
from urllib.parse import urlencode
import urllib.parse

import elastic
from elastic.documents.review import ReviewDocument
from coresdk import SourceManager
from coresdk_v2.base import ReviewClient
from cvsdk import AccountClient, Forbidden403Error, ReviewsClient, NotFound404Error
from enum import Enum
from srsdk import ReviewGenerationConfigurationClient as ReviewGenConfigClient
from vautil.http import prepend_http_scheme_to_url
import vconfig

from app.constants import DEFERRED_ROOT
from app.domain.core import listing
from app.domain.url_mappings import lookup_hostslug_msid_mapping_for_msid_async, lookup_hostslug_msid_mapping_for_msid
from app.domain.microsite.schema_type import get_schema_types_by_taxonomy_ids
from app.models.microsite import Microsite, AccountGroupModel
import settings
import re

LOCATION_PAGE_REVIEW_SOURCE = 12000
GOOGLE_SOURCE = 10010
YELP_SOURCE = 10000
FACEBOOK_SOURCE = 10050

FACEBOOK_IOS_APP_LINK_TEMPLATE = "fb://page/?id={pageid}"
FACEBOOK_ANDROID_APP_LINK_TEMPLATE = \
    "intent://{url}#Intent;package=com.facebook.katana;scheme=https;S.browser_fallback_url={fallback_url};end"
GOOGLE_MAPS_IOS_APP_LINK_SCHEME = "comgooglemapsurl"
GOOGLE_MAPS_ANDROID_APP_LINK_TEMPLATE = "https://www.google.com/maps/search/?api=1&{query}&query_place_id={external_id}"


class MobilePlatform(str, Enum):
    """ Mobile platforms supported """
    iphone = 'iphone'
    ipad = 'ipad'
    android = 'android'


MOBILE_PLATFORMS = [MobilePlatform.ipad, MobilePlatform.iphone, MobilePlatform.android]


def _get_review_client():
    """
    Returns the core services review client.
    """
    return ReviewClient(settings.VSOCIAL_USER, settings.VSOCIAL_API, configuration=settings.ENVIRONMENT_NAME.lower())


def _send_review_share_request_email(review_id, account_group_id):
    """
    Send the email to request that a positive My Listing review be shared to a 3rd party source
    """
    customer_voice_client = ReviewsClient(settings.CV_API_USER, settings.CV_API_KEY,
                                          configuration=settings.ENVIRONMENT_NAME.lower())
    try:
        customer_voice_client.sendShareReviewRequestEmailV1(account_group_id, review_id)
    except Forbidden403Error as e:
        logging.info('Account %s does not have a CV account, not sending email', account_group_id)
        raise PermanentTaskFailure(e.message)
    except NotFound404Error as e:
        logging.info('Account %s not found', account_group_id)
        raise PermanentTaskFailure(e.message)


def create_core_review_for_account(msid, pid, rating, name, email, title=None, content=None, send_follow_up=True):
    """
    hit core services to add a new review
    """
    host_slugs_future = lookup_hostslug_msid_mapping_for_msid_async(pid, msid)
    site = Microsite.get_by_msid(msid, pid=pid)
    if not site:
        raise ValueError('Invalid msid/pid combination.')

    customer_voice_client = AccountClient(settings.CV_API_USER, settings.CV_API_KEY,
                                          configuration=settings.ENVIRONMENT_NAME.lower())
    has_active_customer_voice_account_future = customer_voice_client.hasCustomerVoiceActiveV1Async(
        accountGroupId=site.agid
    )

    url = build_review_url_from_slug(host_slugs_future.get_result(), '%s')

    client = _get_review_client()
    review = client.createReviewV2(agid=site.agid, reviewerEmail=email, reviewerName=name, content=content,
                                   originalStars=rating, title=title, url=url)
    config = vconfig.ConfigurationClient(settings.ENVIRONMENT_NAME).get_config(pid, market_id=site.market_id)

    if config.send_followup_email_flag and rating >= 4.0 and content and \
            has_active_customer_voice_account_future.get_result() and send_follow_up:
        logging.info('Deferring task to send a positive review follow up email request.')
        defer_url = DEFERRED_ROOT + 'microsite/send-request-share-review-email'
        deferred.defer(_send_review_share_request_email, review.get('rid'), site.agid,
                       _url=defer_url,
                       _queue='send-review-share-request',
                       _countdown=2 * 60 * 60)

    return review


def comment_on_core_review_for_account(rid, msid, pid, name, email, content):
    """
    Post a comment to an existing review.
    """
    site = Microsite.get_by_msid(msid, pid=pid)
    if not site:
        raise ValueError('Invalid msid/pid combination.')

    client = _get_review_client()
    return client.postReviewCommentV2(agid=site.agid, commenterEmail=email, commenterName=name,
                                      commentText=content, rid=rid)


def get_location_page_review(review_id, account_group_id):
    """ Get review from elastic search, returns None if no reviews """
    review = ReviewDocument.get(review_id, routing_id=account_group_id)
    return review if review.source_id == LOCATION_PAGE_REVIEW_SOURCE else None


def _filter_out_redirect_slugs(slugs):
    """
    given a list of slug mappings, return those that have no redirect slug
    """
    return [slug for slug in slugs if slug.redirect_hostslug is None]


def build_add_review_url_from_slug(host_slugs, use_https):
    """
    Builds review urls from the first host slug in the list
    :param host_slugs: list of host slugs
    :param use_https: boolean for whether or not to use https in URL
    :return: list of review add urls based on host slugs, empty string if no slugs
    """
    host_slugs = _filter_out_redirect_slugs(host_slugs)
    if len(host_slugs) > 0:
        if use_https:
            return urllib.parse.urlunparse(['https', host_slugs[0].hostslug, '/review/add/', '', '', ''])
        else:
            return urllib.parse.urlunparse(['http', host_slugs[0].hostslug, '/review/add/', '', '', ''])
    else:
        return ''


def build_review_url_from_slug(host_slugs, review_id):
    """
    Builds the URL to a specific URL given msid, pid and a review id
    """
    host_slugs = _filter_out_redirect_slugs(host_slugs)
    if len(host_slugs) > 0:
        return urllib.parse.urlunparse(['http', host_slugs[0].hostslug, '/review/%s/' % review_id, '', '', ''])
    else:
        return ''


def get_review_generation_configuration(account_group_id, partner_id):
    """
    Return the list of listing source ids configured for given account group.
    If Account group does not have configuration return default listings.
    """
    if not account_group_id:
        raise ValueError('account_group_id is required to find listing source ids.')
    if not partner_id:
        raise ValueError('partner_id is required')

    review_config_client = ReviewGenConfigClient(apiUser=settings.RI_API_USER,
                                                 apiKey=settings.RI_API_KEY,
                                                 configuration=settings.ENVIRONMENT_NAME.lower(),
                                                 allowVObjectResponse=True)
    review_generation_configuration = review_config_client.getReviewGenerationConfigurationV2(
        accountGroupId=account_group_id, partnerId=partner_id)
    return review_generation_configuration


def get_google_maps_review_url(company_name, google_maps_url):
    """ Get Google Maps review URL. """

    if not company_name or not google_maps_url:
        return None

    parse_result = urllib.parse.urlparse(google_maps_url)
    if not parse_result or not parse_result.query:
        return None

    parse_result = urllib.parse.parse_qs(parse_result.query.lower())
    cid = parse_result.get('cid')

    if not cid or len(cid) < 1 or not cid[0].isdigit():
        return None
    cid = cid[0]

    if isinstance(company_name, str):
        company_name = company_name.encode('utf-8')

    review_url_template = "https://www.google.com/search?{query}#lrd=0x0:0x{hex_cid},1"
    review_url = review_url_template.format(
        query=urlencode({'q': company_name, 'ludocid': cid}),
        hex_cid=format(int(cid), 'x')
    )

    return review_url


def get_google_maps_review_url_by_place_id(place_id):
    """ Get Google Maps review URL. """

    if not place_id:
        return None

    review_url_template = "http://search.google.com/local/writereview?placeid={place_id}"
    review_url = review_url_template.format(
        place_id=place_id
    )

    return review_url


def get_sorted_listings_from_core(agid, listing_source_ids):
    """
    Retrieves listing source URLs from CORE and it sorts everything based on the score of listing.
    """
    listings = listing.lookup_listings(agid,
                                       source_id=[source_id for source_id in listing_source_ids],
                                       include_good_listings=True,
                                       include_poor_listings=False,
                                       include_not_done_listing=False,
                                       include_anchor_data_matches=False)

    # sort good listings by verifiedFlag and finalScore to find best listing
    sorted_listings_by_source = {}
    for source in listings:
        sorted_listings = sorted(source.get('goodListings', []),
                                 key=lambda l: (-l.get('verifiedFlag'), -l.get('scoreDetails', {}).get('finalScore')))
        if sorted_listings:
            source_id = source['sourceId']
            url = sorted_listings[0]['url']

            sorted_listings_by_source[source_id] = {
                'source_name': source['userFriendlySourceName'],
                'url': url,
                'external_id': sorted_listings[0]['externalId'],
                'company_name': sorted_listings[0]['anchorData']['companyName']
            }

            if source_id == GOOGLE_SOURCE:
                external_id = sorted_listings[0]['externalId']
                if not external_id:
                    del sorted_listings_by_source[source_id]
                    logging.warn('No external ID for listing %s', sorted_listings[0].get('lid'))
                    continue

                potential_place_id = place_id_from_external_id(external_id)
                if potential_place_id.isdigit() or sorted_listings[0]['anchorData']['country'] == 'SG':
                    company_name = sorted_listings[0]['anchorData']['companyName']
                    review_url = get_google_maps_review_url(company_name, url)
                else:
                    review_url = get_google_maps_review_url_by_place_id(potential_place_id)
                sorted_listings_by_source[source_id]['review_url'] = review_url

    return sorted_listings_by_source


def place_id_from_external_id(external_id):
    """
    Return the place id from an external id string.
    The external id can contain just the place id or the source and place id separated by a colon
    """
    return external_id.split(':')[1] if ':' in external_id else external_id


def prepare_listings_for_rendering(listing_source_ids, sorted_listings_by_source, overridden_source, platform=None):
    """
    Returns list in the form: [{'sourceId': 10000, 'url: 'http://yelp.com/foo', 'name': 'Yelp'}, {...}]
    """
    result = []
    for source_id in listing_source_ids:

        overridden_url = None

        for overwrite in overridden_source:
            if source_id == overwrite.listing_source_id:
                overridden_url = overwrite.review_url
                source_listing = build_listing_info_dict_from_overridden_info(overwrite)

        if not overridden_url:
            source_listing = sorted_listings_by_source.get(source_id)
            if source_listing:
                source_listing['name'] = source_listing['source_name'].title()
        else:
            overridden_url = prepend_http_scheme_to_url(overridden_url)

        if source_listing and source_listing['url']:
            listing_info = {
                'sourceId': source_id,
                'url': overridden_url or _get_review_deep_link(source_listing['url']),
                'name': source_listing['name']
            }

            if source_id == GOOGLE_SOURCE:
                listing_info['reviewUrl'] = overridden_url or source_listing.get('review_url')
            if platform in MOBILE_PLATFORMS:
                listing_info['reviewUrl'] = _get_mobile_native_app_url(source_id, source_listing, platform)
            result.append(listing_info)

    return result


def _get_mobile_native_app_url(source_id, listing_info, platform):
    """
    Returns the native app deep link on Android/iOS for Facebook/Google
    """
    external_id = listing_info.get('external_id')
    listing_url = listing_info.get('url')
    company_name = listing_info.get('company_name')

    if source_id == FACEBOOK_SOURCE:
        if external_id and platform in [MobilePlatform.ipad, MobilePlatform.iphone]:
            return FACEBOOK_IOS_APP_LINK_TEMPLATE.format(pageid=external_id.split(':')[1])
        elif platform in [MobilePlatform.android]:
            return FACEBOOK_ANDROID_APP_LINK_TEMPLATE.format(url=listing_url, fallback_url=listing_url)
    elif source_id == GOOGLE_SOURCE:
        if platform in [MobilePlatform.ipad, MobilePlatform.iphone]:
            scheme, netloc, path, params, query, fragment = urllib.parse.urlparse(listing_url)
            scheme = GOOGLE_MAPS_IOS_APP_LINK_SCHEME
            return urllib.parse.urlunparse([scheme, netloc, path, params, query, fragment])
        elif external_id and platform in [MobilePlatform.android]:
            return GOOGLE_MAPS_ANDROID_APP_LINK_TEMPLATE.format(
                query=urlencode({'query': company_name}),
                external_id=external_id.split(':')[1]
            )
    return listing_url


def _get_review_deep_link(url):
    """
    Returns the deep link url for a given url
    eg. 'facebook.com/business' -> 'facebook.com/business?sk=reviews'
    """
    sanitized_url = prepend_http_scheme_to_url(url)
    scheme, netloc, path, params, query, fragment = urllib.parse.urlparse(sanitized_url)

    if 'facebook' in netloc:
        path = re.sub('/reviews/$', '', path)
        path = re.sub('/reviews$', '', path)
        if 'sk=reviews' not in query:
            query += '&sk=reviews' if query else 'sk=reviews'
    elif 'plus.google' in netloc:
        if '/about' not in path:
            path += ('/' if not path.endswith('/') else '') + 'about'
        query += ('&' if query else '') + 'review=1'

    return urllib.parse.urlunparse([scheme, netloc, path, params, query, fragment])


def get_review_generation_dict(account_group_id, partner_id, platform=None):
    """
    Gets relevant listings from core and returns the URL for each
    Return dict in the form {'listings': {prepared_listings}, 'positive_review_message': '', negative ...}
    """
    review_gen_config = get_review_generation_configuration(account_group_id, partner_id)

    non_overridden_source_ids = get_non_overridden_source_ids(review_gen_config)

    sorted_listings_by_source = get_sorted_listings_from_core(agid=account_group_id,
                                                              listing_source_ids=non_overridden_source_ids)
    prepared_listings = prepare_listings_for_rendering(
        listing_source_ids=review_gen_config.listing_source_ids,
        sorted_listings_by_source=sorted_listings_by_source,
        overridden_source=review_gen_config.listing_sources_url_overrides,
        platform=platform
    )
    return {
        'listings': prepared_listings,
        'positive_review_message': review_gen_config.positive_review_message,
        'negative_review_message': review_gen_config.negative_review_message,
        'negative_feedback_title': review_gen_config.negative_feedback_title,
        'negative_feedback_message': review_gen_config.negative_feedback_message
    }


def get_non_overridden_source_ids(review_gen_config):
    """
    Returns a list of preferred sites source ids that have not had their URLs overwritten in SR settings
    """
    selected_source_ids = review_gen_config.listing_source_ids[:]
    for source in review_gen_config.listing_sources_url_overrides:
        if source.listing_source_id in selected_source_ids:
            selected_source_ids.remove(source.listing_source_id)
    return selected_source_ids


def build_listing_info_dict_from_overridden_info(overridden_source):
    """
    Building the listing dict MS expected in the front end, from overridden objects set in Customer Voice and call
    to CS SDK for the name of the source from the id
    """
    source_manager_client = SourceManager(
        apiUser=settings.VSOCIAL_USER,
        apiKey=settings.VSOCIAL_API,
        target=settings.ENVIRONMENT_NAME
    )
    source = source_manager_client.lookupGlobalBySourceId(overridden_source.listing_source_id)

    return {
        'sourceId': overridden_source.listing_source_id,
        'url': overridden_source.review_url,
        'name': source['name']
    }


def get_item_reviewed_types_from_tax_ids(tax_ids):
    """
    Gets and formats a list of schema types from a list of tax ids
    "http://schema.org/Thing" format is required for rich snippets
    """
    schema_types = get_schema_types_by_taxonomy_ids(tax_ids)
    return ["http://schema.org/%s" % schema_type for schema_type in schema_types]


def get_reviews_by_source(agid, sources, offset=0, sort_published=False, limit=10):
    """
    Get reviews from elastic search by params:
    :param agid: Account Group ID
    :param sources: source or list of sources
    :param offset: for paging
    :param sort_published: sort by date published descending
    :param limit: how many docs to grab, default 10
    :return: reviews based on filters and sorting above
    """
    sources = sources if isinstance(sources, list) else [sources]
    search = ReviewDocument.search(offset=offset, routing=agid, limit=limit)
    search.add_filter(elastic.FilterBuilder.term(ReviewDocument.account_group_id, agid))
    search.add_filter(elastic.FilterBuilder.term(ReviewDocument.is_published, True))
    search.add_filter(elastic.FilterBuilder.terms(ReviewDocument.source_id, sources))
    if sort_published:
        search.add_sort(elastic.SortBuilder.field(ReviewDocument.published, order=elastic.SortOrder.DESC))

    return search.execute()


def format_first_name_last_initial_for_display(reviewer_name):
    """ Format first name and last initial. Enforce capitalization.
        Ex. john cena -> John C.
    """
    split_names = [name for name in reviewer_name.split(' ') if name]
    split_names.append(None)  # Ensure the ternary check below does not throw an error
    first_name = split_names[0].capitalize()
    last_initial = "%s." % split_names[1][0].capitalize() if split_names[1] else ''
    return "{} {}".format(first_name, last_initial)


def get_review_url(account_group_id, review_id):
    """
    Return the URL for a Review
    """
    account_group = AccountGroupModel.build_key(account_group_id=account_group_id).get()
    url_mappings = lookup_hostslug_msid_mapping_for_msid(account_group.partner_id, account_group.msid)
    for mapping in url_mappings:
        if not mapping.redirect_hostslug:
            return f"http://{mapping.hostslug}/review/{review_id}"


def get_review_redirect_url(account_group_id, source_id, use_review_url=False, sorted_listings=None):
    """
    Get the redirect url for the actual source the user wishes to share to.

    Args:
        account_group_id: The account group id
        source_id: The source id
        use_review_url: Whether or not to get the review url for the source_id
        sorted_listings: A potential list of sorted listings to use rather than fetching them manually

    Returns:
        A link to the sources review page
    """
    source_id = int(source_id)
    if not sorted_listings:
        sorted_listings_by_source = get_sorted_listings_from_core(agid=account_group_id,
                                                                  listing_source_ids=[source_id])
    else:
        sorted_listings_by_source = sorted_listings

    source_information = sorted_listings_by_source.get(source_id) if source_id in sorted_listings_by_source else None

    if not source_information or 'url' not in source_information or 'external_id' not in source_information:
        account_group = AccountGroupModel.build_key(account_group_id=account_group_id).get()
        url_mappings = lookup_hostslug_msid_mapping_for_msid(account_group.partner_id, account_group.msid)

        for mapping in url_mappings:
            if not mapping.redirect_hostslug:
                return f"http://{mapping.hostslug}"

        # No mapping found at all
        return "/"

    source_url = 'review_url' if use_review_url else 'url'
    url = _get_review_deep_link(source_information[source_url])
    return str(url)  # Without str, InvalidResponseError: header values must be str, got 'unicode' was thrown.


def get_default_review_share_listings(account_group_id):
    """
    Returns default review sources listing data
    """
    if not account_group_id:
        raise ValueError("account group id is required")

    sorted_listings = get_sorted_listings_from_core(
        agid=account_group_id,
        listing_source_ids=[YELP_SOURCE, FACEBOOK_SOURCE, GOOGLE_SOURCE]
    )

    return [
        {
            'sourceId': YELP_SOURCE,
            'url': get_review_redirect_url(account_group_id, YELP_SOURCE, sorted_listings=sorted_listings),
            'name': 'Yelp'
        },
        {
            'sourceId': FACEBOOK_SOURCE,
            'url': get_review_redirect_url(account_group_id, FACEBOOK_SOURCE, sorted_listings=sorted_listings),
            'name': 'Facebook'
        },
        {
            'sourceId': GOOGLE_SOURCE,
            'url': get_review_redirect_url(account_group_id, GOOGLE_SOURCE, sorted_listings=sorted_listings),
            'name': 'Google',
            'reviewUrl': get_review_redirect_url(
                account_group_id,
                GOOGLE_SOURCE,
                use_review_url=True,
                sorted_listings=sorted_listings
            )
        },
    ]


def set_review_shared(account_group_id, review_id):
    """
    Set the review to be shared.

    Args:
        account_group_id: The account group id
        review_id: The review to mark as shared.

    Returns:

    """
    client = ReviewClient(settings.VSOCIAL_USER, settings.VSOCIAL_API, settings.ENVIRONMENT_NAME.lower())
    return client.updateReviewV2(accountGroupId=account_group_id, rid=review_id, isSharedByReviewerFlag=True)


def get_total_review_aggregate(agid):
    """ Get total review aggregate from elastic search by agid. """

    search = ReviewDocument.search(routing=agid, limit=0)
    search.add_filter(elastic.FilterBuilder.term(ReviewDocument.account_group_id, agid))
    search.add_filter(elastic.FilterBuilder.term(ReviewDocument.is_published, True))
    search.add_filter(elastic.FilterBuilder.term(ReviewDocument.source_id, 12000))

    avg_aggregation = elastic.AggregationBuilder.average(ReviewDocument.original_stars)
    search.add_aggregation(avg_aggregation)
    result = search.execute()

    average_rating = result.aggregations.average(ReviewDocument.original_stars).value
    review_count = result.hits.total

    return review_count, average_rating
