""" Microsite domain objects. """
import urllib.request, urllib.parse, urllib.error

from google.appengine.ext import ndb

from listing_products_sdk._internal.listing_products.v1 import DayOfWeek
from listing_products_sdk._internal.listing_products.v1 import ListingProfile
from settings import ORIGIN_UNKNOWN
from vautil import phonenumber
from fantasm import startStateMachine

from app.constants import VALID_NAVIGATION_SLUG, DEFAULT_COLOR, MAX_NAVIGATION
from app.domain import exceptions
from app.domain.blob_mappings import MsidBlobMapping
from app.keys import MICROSITE_KEYS
from app.models.page import Page as PageModel, CUSTOM
from app.models.microsite import Navigation as NavigationModel
from account_group_sdk.api import AccountGroupAPI, ProjectionFilter
from vax.errors import UninitializedException
from vax.utils.env import env_from_app_engine_env
from listing_products_sdk.api import ListingProductsClient
import urllib.request, urllib.error, urllib.parse
import json


def extract_regular_hours(lp):
    """
    Extracts the regular hours from the listing profile response into a format that can be used by microsites.
    """

    rh = []
    if lp:
        business_hours = lp.business_hours
        for hours in business_hours:
            if hours.hours_type_id == 'GENERAL' and \
                    hours.regular_hours and len(hours.regular_hours) > 0:
                rh = hours.regular_hours
    return rh


extract_regular_hours.__annotations__ = {'lp': ListingProfile, 'return': list}


def convert_regular_hours_to_template(regular_hours):
    """
    converts regular hours spans to the format used in the My Listing template
    """
    days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    hours_of_operations = {}
    for day in days:
        times = []
        for rh in regular_hours:
            span_day = str.title(day_of_week_to_string(rh.open_day))
            if day == span_day:
                open_time = _extractStringTimeFromTimeFormat(rh.open_time)
                close_time = _extractStringTimeFromTimeFormat(rh.close_time)
                time = {'closes': close_time, 'opens': open_time}
                times.append(time)

        if len(times) == 0:
            times = [{}]
        hours_of_operations[day] = {
            'times': times,
        }

    return hours_of_operations

def day_of_week_to_string(day):
    """
    Converts a DayOfWeek enum to its string equivalent
    """
    days = {
        DayOfWeek.SUNDAY: 'SUNDAY',
        DayOfWeek.MONDAY: 'MONDAY',
        DayOfWeek.TUESDAY: 'TUESDAY',
        DayOfWeek.WEDNESDAY: 'WEDNESDAY',
        DayOfWeek.THURSDAY: 'THURSDAY',
        DayOfWeek.FRIDAY: 'FRIDAY',
        DayOfWeek.SATURDAY: 'SATURDAY'
    }
    return days.get(day, None)

def wrap_opener_and_read(opener, request):
    """ wrap the http calls so it is easy to mock """
    try:
        res = opener.open(request, timeout=30)
    except urllib.error.HTTPError as e:
        return json.loads(e.read())
    return res.read()


def wrap_get_access_token(client_connection):
    """ wrap the http calls so it is easy to mock """
    return client_connection._credentials.get_access_token()


# pylint:disable=too-many-instance-attributes


def _extractStringTimeFromTimeFormat(time):
    """
    This function converts the regular hours Time format to string based formatting needed for microsites
    """
    # input = {"hours" : 8, "minutes" : 00 , "seconds": 00}
    # output = formattedStringTime = 8:00:00
    if time.hours:
        hours = str(time.hours).zfill(2)
    else:
        hours = '00'
    if time.minutes:
        minutes = str(time.minutes).zfill(2)
    else:
        minutes = '00:00'
    formattedStringTime = hours + ':' + minutes
    return formattedStringTime


class Microsite:
    """ Represents a microsite. """
    AVAILABLE_MULTI_DESCRIPTION_TAGS = ['*', '+', 'x']

    # pylint:disable=too-many-statements
    def __init__(self, **kwargs):
        """ Initialize """
        if not kwargs.get(MICROSITE_KEYS.MSID):
            raise ValueError('msid is required.')
        if not kwargs.get(MICROSITE_KEYS.PID):
            raise ValueError('pid is required.')
        self.agid = kwargs.get(MICROSITE_KEYS.AGID)
        account_group = None
        self.listing_profile = None

        if self.agid:
            AccountGroupAPI.set_environment(env_from_app_engine_env())
            account_group = AccountGroupAPI.get_multi([self.agid],
                                                      projection_filter=ProjectionFilter(nap_data=True))[0]
            env = env_from_app_engine_env()
            try:
                ListingProductsClient.get_environment()
            except UninitializedException:
                ListingProductsClient.set_environment(env)

            listing_profile_response = ListingProductsClient.get_listing_profile(self.agid)
            for profile in listing_profile_response.listing_profiles:
                if profile:
                    self.listing_profile = profile.listing_profile
                    break

        if account_group and account_group.nap_data and account_group.nap_data.service_area_business:
            service_area_business = True
        else:
            service_area_business = False

        self.service_area_business = service_area_business

        self.msid = kwargs[MICROSITE_KEYS.MSID].upper()
        self.pid = kwargs[MICROSITE_KEYS.PID].upper()
        self.pmsid = kwargs.get(MICROSITE_KEYS.PMSID)  # do not change case of partner identifier
        self.spid = kwargs.get(MICROSITE_KEYS.SPID)
        self.sso_token = kwargs.get(MICROSITE_KEYS.SSO_TOKEN)
        self.account_origin = kwargs.get(MICROSITE_KEYS.ACCOUNT_ORIGIN, ORIGIN_UNKNOWN)
        self.product_edition = kwargs.get(MICROSITE_KEYS.PRODUCT_EDITION)
        self.welcomed_flag = kwargs.get(MICROSITE_KEYS.WELCOMED_FLAG)

        self.created = kwargs.get(MICROSITE_KEYS.CREATED)
        self.updated = kwargs.get(MICROSITE_KEYS.UPDATED)

        self.name = kwargs.get(MICROSITE_KEYS.NAME)
        self.blurb = kwargs.get(MICROSITE_KEYS.BLURB)

        self.phone = kwargs.get(MICROSITE_KEYS.PHONE)
        self.phone_work = kwargs.get(MICROSITE_KEYS.PHONE_WORK, [])

        self.phone_call_tracking = kwargs.get(MICROSITE_KEYS.PHONE_CALL_TRACKING)
        self.email = kwargs.get(MICROSITE_KEYS.EMAIL)
        self.website = kwargs.get(MICROSITE_KEYS.WEBSITE)
        self.website_no_redirect = self.website
        self.use_website_for_full_site_link = kwargs.get(MICROSITE_KEYS.USE_WEBSITE_FOR_FULL_SITE_LINK, True)

        self.address1 = kwargs.get(MICROSITE_KEYS.ADDRESS1)
        self.address2 = kwargs.get(MICROSITE_KEYS.ADDRESS2)
        self.city = kwargs.get(MICROSITE_KEYS.CITY)
        self.state = kwargs.get(MICROSITE_KEYS.STATE)
        self.country = kwargs.get(MICROSITE_KEYS.COUNTRY)
        self.zipcode = kwargs.get(MICROSITE_KEYS.ZIP_CODE)
        self.place = kwargs.get(MICROSITE_KEYS.PLACE)
        self.geo = kwargs.get(MICROSITE_KEYS.GEO)

        self.facebook_url = kwargs.get(MICROSITE_KEYS.FACEBOOK_URL)
        self.twitter_url = kwargs.get(MICROSITE_KEYS.TWITTER_URL)
        self.rss_url = kwargs.get(MICROSITE_KEYS.RSS_URL)
        self.youtube_url = kwargs.get(MICROSITE_KEYS.YOUTUBE_URL)
        self.linkedin_url = kwargs.get(MICROSITE_KEYS.LINKEDIN_URL)
        self.instagram_url = kwargs.get(MICROSITE_KEYS.INSTAGRAM_URL)
        self.pinterest_url = kwargs.get(MICROSITE_KEYS.PINTEREST_URL)
        self.foursquare_url = kwargs.get(MICROSITE_KEYS.FOURSQUARE_URL)
        self.googleplus_url = kwargs.get(MICROSITE_KEYS.GOOGLEPLUS_URL)

        self.color = kwargs.get(MICROSITE_KEYS.COLOR, DEFAULT_COLOR)
        self.customer_identifier = kwargs.get(MICROSITE_KEYS.CUSTOMER_IDENTIFIER)
        self.announcement = kwargs.get(MICROSITE_KEYS.ANNOUNCEMENT)
        self.categories = None
        if self.listing_profile and self.listing_profile.rich_data:
            self.categories = self.listing_profile.rich_data.syncing_seo_keywords

        self.billing_code = kwargs.get(MICROSITE_KEYS.BILLING_CODE)
        self.market_id = kwargs.get(MICROSITE_KEYS.MARKET_ID)
        self.tax_id = kwargs.get(MICROSITE_KEYS.TAX_ID)

        self.phone_theme = kwargs.get(MICROSITE_KEYS.PHONE_THEME)
        self.desktop_theme = kwargs.get(MICROSITE_KEYS.DESKTOP_THEME)
        self.layout = kwargs.get(MICROSITE_KEYS.LAYOUT)

        self.show_address = kwargs.get('show_address')
        self.show_city = kwargs.get('show_city')
        self.show_region = kwargs.get('show_region')
        self.show_zip = kwargs.get('show_zip')
        self.show_phone_number = kwargs.get('show_phone_number')
        self.full_screen = kwargs.get('full_screen')

        self.login_count = kwargs.get(MICROSITE_KEYS.LOGIN_COUNT)
        self.last_login = kwargs.get(MICROSITE_KEYS.LAST_LOGIN)
        self.login_history = kwargs.get(MICROSITE_KEYS.LOGIN_HISTORY)

        self.strict_map = kwargs.get(MICROSITE_KEYS.STRICT_MAP, False)
        self.__navigation_items = []
        self.blob_mapping = {}
        account_group = kwargs.get('account_group_object')

        self.hide_from_robots = kwargs.get(MICROSITE_KEYS.HIDE_FROM_ROBOTS)
        self.chat_widget_id = kwargs.get(MICROSITE_KEYS.CHAT_WIDGET_ID)

    @property
    def display_business_hours_template(self):
        """ Generates a comprehensive template to display the hours of operation.  """
        regular_hours = extract_regular_hours(self.listing_profile)
        return convert_regular_hours_to_template(regular_hours)

    @staticmethod
    def _get_multi_description_tag(offset):
        """
        :param offset: The offset of which symbol to choose
        :return: The description tag
        """
        num_times = 1 + int(offset / len(Microsite.AVAILABLE_MULTI_DESCRIPTION_TAGS))
        tag = Microsite.AVAILABLE_MULTI_DESCRIPTION_TAGS[offset % len(Microsite.AVAILABLE_MULTI_DESCRIPTION_TAGS)]
        return tag * num_times

    @property
    def formatted_phone(self):
        """ Returns the formatted phone number. """
        return phonenumber.format_phone_number_html(self.phone, self.country)

    @classmethod
    def from_model(cls, model):
        """ Creates a domain microsite given a model microsite. """
        if not model:
            return None

        return cls(account_group_object=model.account_group, **model.to_dict())

    @classmethod
    def from_model_and_populate(cls, ms_entity, partner=None, nav_entities=None, blob_mapping_entities=None):
        """ convert to a domain object, and populate with partner info, navigation and blob mapping entities """
        ms = cls.from_model(ms_entity)
        if ms:
            if partner:
                ms.populate_from_partner(partner)
            if nav_entities:
                ms.populate_navigation_items_from_entities(nav_entities)
            if blob_mapping_entities:
                ms.populate_blob_mappings_from_entities(blob_mapping_entities)
        return ms

    @property
    def main_navigation(self):
        """ Returns a list of NavigationItem objects. """
        return self.__navigation_items

    def get_navigation(self, navigation_slug):
        """ Retrieves the navigation item identified by navigation slug. """
        for item in self.__navigation_items:
            if item.navigation_slug == navigation_slug:
                return item
        return None

    def get_navigation_for_pageid(self, pageid):
        """ Retrieves the navigation item identified by navigation pageid. """
        for item in self.__navigation_items:
            if item.pageid == pageid:
                return item
        return None

    def populate_from_partner(self, partner):
        """ Populates fields based on partner. """
        if partner:
            self.strict_map = partner.strict_map

    def populate_navigation_items_from_entities(self, entities):
        """ Takes a list of Navigation entities and set the local store. """
        if entities is None:
            raise ValueError('entities is required, or must be an empty list.')

        entities = [entity for entity in entities if entity is not None]

        entities.sort(key=lambda entity: entity.order)
        self.__navigation_items = [NavigationItem(entity.name, entity.navigation_slug, entity.pageid, entity.icon)
                                   for entity in entities]

    def __get_navigation_items_from_datastore(self):
        """ Retrieves all the navigation items, using predictive keys. """
        keys = NavigationModel.build_predictive_keys(self.msid, self.pid)
        items = ndb.get_multi(keys)
        items = [item for item in items if item is not None]
        items.sort(key=lambda entity: entity.order)
        return items

    def populate_blob_mappings_from_entities(self, entities):
        """ Take a list of MsidBlobMapping entities and set the local store. """
        for entity in entities:
            if entity:
                blob_mapping = MsidBlobMapping.from_model(entity)
                self.blob_mapping[blob_mapping.category] = blob_mapping

    def update_navigation(self, navigation_slug, name=None):
        """ Updates the details for an item identified by navigation_slug.

        @raises UnknownNavigationSlugException if the slug does not exist
        """
        if not navigation_slug:
            raise ValueError('navigation_slug is required.')
        entities = self.__get_navigation_items_from_datastore()
        item = None
        for entity in entities:
            if entity.navigation_slug == navigation_slug:
                item = entity
                break
        if not item:
            raise exceptions.UnknownNavigationSlugException()

        if name:
            item.name = name
        item.put()

        # rebuild this object
        entities = self.__get_navigation_items_from_datastore()
        self.populate_navigation_items_from_entities(entities)

    def update_navigation_slug(self, pageid, navigation_slug):
        """ Updates the slug for the navigation item identified by pageid.

        @raises NavigationSlugExistsException if the navigation_slug already exists
        @raises UnknownNavigationPageidException if the pageid does not exist
        """

        if not pageid:
            raise ValueError('pageid is required.')
        if not navigation_slug:
            raise ValueError('navigation_slug is required.')

        entities = self.__get_navigation_items_from_datastore()

        item = None
        for entity in entities:
            if entity.pageid == pageid:
                item = entity
                break
        if not item:
            raise exceptions.UnknownNavigationPageidException()

        if item.navigation_slug == navigation_slug:
            # nothing to update
            return

        if navigation_slug in [entity.navigation_slug for entity in entities]:
            raise exceptions.NavigationSlugExistsException(
                'The navigation slug "%s" already exists. They must be unique.' % navigation_slug)

        item.navigation_slug = navigation_slug
        item.put()

        # rebuild this object
        entities = self.__get_navigation_items_from_datastore()
        self.populate_navigation_items_from_entities(entities)

    def add_navigation(self, navigation_slug, pageid, name, order=2 ^ 15, icon=CUSTOM):
        """ Adds a navigation item.

        @param navigation_slug - The url portion e.g., http://sites.partner.com/microsite-slug/[navigation-slug]/
                                 Should not lead or trail with '/'. For default page, use '' (empty string).
                                 navigation_slug must be unique within a given microsite.
        @param pageid - The key_name of the page that this navigation item points to.
        @param name - The name that appears in the on-screen navigation (e.g., the tab name)
        @param order - The order that this item appears relative to other items, starting at 1. Adding an
                       item in between other items will cause all the following items to shift down. If not
                       provided, the item will be added at the end of the list.
        @param icon - An icon for the navigation item the icon will map to a static file with the name
                      ic_mn_<icon>.png all lowercase. It will default to "Custom"
        """
        if not navigation_slug:
            raise ValueError('navigation_slug is required.')
        if not pageid:
            raise ValueError('pageid is required.')
        if not name:
            raise ValueError('name is required.')
        if order:
            order = int(order)
        if not VALID_NAVIGATION_SLUG.match(navigation_slug):
            raise exceptions.InvalidNavigationSlugException(
                'Navigation slug can only be lowercase letters, numbers, and dashes.')

        @ndb.transactional(xg=True)  # pylint: disable=E1120
        def tx(navigation_slug, pageid, name, order, icon):
            """ Updates all the navigation items in a transaction to facilitate re-ordering. """
            items = self.__get_navigation_items_from_datastore()

            if len(items) == MAX_NAVIGATION:
                raise exceptions.TooManyNavigationItemsException()

            for item in items:
                if item.navigation_slug == navigation_slug:
                    raise exceptions.NavigationSlugExistsException(
                        'The navigation slug "%s" already exists. They must be unique.' % navigation_slug)

            # find a slot_num
            existing_key_names = {item.key.string_id() for item in items}
            for n in range(1, MAX_NAVIGATION + 1):
                new_item_key = NavigationModel.build_key(self.msid, n, self.pid)
                if new_item_key.string_id() not in existing_key_names:
                    break

            new_item = NavigationModel(key=new_item_key, navigation_slug=navigation_slug, pageid=pageid,
                                       msid=self.msid, pid=self.pid, name=name,
                                       order=0, icon=icon)  # order is irrelevant, we'll insert and renumber below

            # insert the new item in the list
            items.insert(order - 1, new_item)

            # re-index the items, so that there is a nice, increasing order
            new_order = 0  # bizarre love triangle
            for item in items:
                new_order += 1
                item.order = new_order

            # now, store them all!
            ndb.put_multi(items)
            return items

        new_items = tx(navigation_slug, pageid, name, order, icon)
        self.populate_navigation_items_from_entities(new_items)

    def remove_navigation(self, navigation_slug):
        """ Removes the navigation item identified by navigation_slug. """
        if not navigation_slug:
            raise ValueError('navigation_slug is required.')
        entities = self.__get_navigation_items_from_datastore()
        for entity in entities:
            if entity.navigation_slug == navigation_slug:
                entities.remove(entity)
                entity.key.delete()
                self.populate_navigation_items_from_entities(entities)
                return
        raise exceptions.UnknownNavigationSlugException(
            'Could not find navigation slug named "%s".' % navigation_slug)

    def match_navigation(self, navigation_slugs):
        """ Sets the navigation items to correspond with the incoming list of navigation slugs,
            including deletion (and cascading Page deletion) and re-ordering.
        """
        if navigation_slugs is None:
            raise ValueError('navigation_slugs is required.')
        if not isinstance(navigation_slugs, list):
            raise ValueError('navigation_slugs must be a list.')

        # check that there is no blank slug in the list
        for slug in navigation_slugs:
            if not slug:
                raise exceptions.NavigationSlugIsRequiredException('The navigation slug cannot be an empty string.')

        # check that the list contains unique slugs
        if sorted(navigation_slugs) != sorted(list(set(navigation_slugs))):
            raise ValueError('The navigation slugs must be unique.')

        # check that all the mentioned navigation slugs are in the current set
        entities = self.__get_navigation_items_from_datastore()
        current_slugs = {entity.navigation_slug for entity in entities}
        for slug in navigation_slugs:
            if slug not in current_slugs:
                raise ValueError('Unable to find slug "%s" in current set.' % slug)

        # delete the appropriate NavigationItem entities
        slugs_to_delete = current_slugs - set(navigation_slugs)
        keys_to_delete = []
        pageids_to_clean = []
        for slug in slugs_to_delete:
            for entity in entities:
                if slug == entity.navigation_slug:
                    keys_to_delete.append(entity.key)
                    pageids_to_clean.append(entity.pageid)
        ndb.delete_multi(keys_to_delete)

        # kick off machines to clean up the Page entities that correspond to the removed NavigationItem entities
        cleaning_contexts = []
        for pageid in pageids_to_clean:
            cleaning_contexts.append(
                {'page_key': PageModel.build_key(pageid, self.msid, pid=self.pid)}
            )
        startStateMachine('RemovePage', cleaning_contexts)

        # reorder the remaining set
        order = 0
        entities_to_put = []
        for slug in navigation_slugs:
            order += 1
            for entity in entities:
                if slug == entity.navigation_slug and entity.order != order:
                    entity.order = order
                    entities_to_put.append(entity)
        ndb.put_multi(entities_to_put)

        # rebuild this microsite object
        entities = self.__get_navigation_items_from_datastore()
        self.populate_navigation_items_from_entities(entities)

    def google_maps_url(self):
        """ Returns a link to a google map for this microsite or
        None if address and city/zipcode not provided or geo pt not provided.
        If strict_map is True will only return map if lat/lon are provided.

        eg. http://maps.google.com/maps?q=922++43rd+St+E+%2C+Saskatoon%2C+SK+S7K6H2&sll=52.160145%2C-106.648818
        """

        maps_url = None
        map_args = ''

        # if strict_map then don't use address unless geo point is provided
        if not self.strict_map and (self.address1 or self.address2) and (self.city or self.zipcode):
            map_data = {
                MICROSITE_KEYS.ADDRESS1: self.address1 or "",
                MICROSITE_KEYS.ADDRESS2: self.address2 or "",
                MICROSITE_KEYS.CITY: self.city or "",
                MICROSITE_KEYS.STATE: self.state or "",
                MICROSITE_KEYS.ZIP_CODE: self.zipcode or "",
                MICROSITE_KEYS.COUNTRY: self.country or ""
            }
            map_args += "%(address1)s %(address2)s, %(city)s, %(state)s %(zipcode)s, %(country)s" % map_data
            map_args = map_args.strip(', ')
            map_args = map_args.encode('utf-8')
            map_args = 'q=%s' % urllib.parse.quote_plus(map_args)

        if self.geo and not map_args:
            map_args = 'q=%s' % urllib.parse.quote_plus(str(self.geo).encode('utf-8'))

        if map_args:
            maps_url = "http://maps.google.com/maps?%s" % map_args

        return maps_url

    def long_address(self):
        """ Returns a long form address with each field comma delimited. """
        address = ''

        if self.name:
            address = self.name + ', '

        if self.address1:
            address += self.address1
            if self.address2:
                address += ' - '
            else:
                address += ', '

        if self.address2:
            address += self.address2 + ', '

        if self.city:
            address += self.city + ', '

        if self.state:
            address += self.state + ', '

        if self.country:
            address += self.country

        return address.strip(', ')

    def get_mapping_url(self, category):
        """ Return the mapping url for a given category if the mapping exists otherwise None. """
        mapping = self.blob_mapping.get(category)
        return mapping and mapping.get_url()

    def get_common_data(self):
        """ Return common data in microsite. """
        common_data = {
            MICROSITE_KEYS.NAME: self.name,
            MICROSITE_KEYS.CITY: self.city,
            MICROSITE_KEYS.PHONE_WORK: self.phone_work,
            MICROSITE_KEYS.PHONE_CALL_TRACKING: self.phone_call_tracking,
            MICROSITE_KEYS.ZIP_CODE: self.zipcode,
            MICROSITE_KEYS.COUNTRY: self.country,
            MICROSITE_KEYS.STATE: self.state,
            MICROSITE_KEYS.CATEGORIES: self.categories,
            MICROSITE_KEYS.ADDRESS1: self.address1,
            MICROSITE_KEYS.MARKET_ID: self.market_id,
            MICROSITE_KEYS.WEBSITE: self.website,
            MICROSITE_KEYS.CUSTOMER_IDENTIFIER: self.customer_identifier,
            MICROSITE_KEYS.GEO: self.geo,
            MICROSITE_KEYS.TAX_ID: self.tax_id,
            MICROSITE_KEYS.EMAIL: self.email,
            MICROSITE_KEYS.PLACE: self.place,
            MICROSITE_KEYS.FACEBOOK_URL: self.facebook_url,
            MICROSITE_KEYS.FOURSQUARE_URL: self.foursquare_url,
            MICROSITE_KEYS.GOOGLEPLUS_URL: self.googleplus_url,
            MICROSITE_KEYS.LINKEDIN_URL: self.linkedin_url,
            MICROSITE_KEYS.PINTEREST_URL: self.pinterest_url,
            MICROSITE_KEYS.RSS_URL: self.rss_url,
            MICROSITE_KEYS.TWITTER_URL: self.twitter_url,
            MICROSITE_KEYS.INSTAGRAM_URL: self.instagram_url,
            MICROSITE_KEYS.YOUTUBE_URL: self.youtube_url,
            MICROSITE_KEYS.BLURB: self.blurb,
        }
        return common_data

    def get_common_data_for_provided_list_of_args(self, args=None):
        """ Return common data from microsite only if the property is in args """
        if not args:
            return {}

        common_data = self.get_common_data()

        for key in list(common_data.keys()):
            if key not in args:
                del common_data[key]

        return common_data

    def suppress_nap_data(self):
        """ Alters the microsite object by suppressing NAP data if specified by user"""
        if not self.show_phone_number:
            self.phone = None
            self.phone_work = None
        if not self.show_region:
            self.state = None
        if not self.show_city:
            self.city = None
        if not self.show_zip or self.service_area_business:
            self.zipcode = None
        if not self.show_address or self.service_area_business:
            self.address1 = None
            self.address2 = None
            self.geo = None


class NavigationItem:
    """ A navigation item."""

    def __init__(self, name, navigation_slug, pageid, icon=None):
        """ Initialize. """
        if not name:
            raise ValueError('name is required.')
        if not pageid:
            raise ValueError('pageid is required.')
        self.name = name
        self.navigation_slug = navigation_slug
        self.pageid = pageid

        icon = icon or CUSTOM
        self.icon = icon.lower()

    def get_url(self, site_slug):
        """ Returns a relative url, given the site slug. """
        # navigation_slug is like '' or "foo"
        # site_slug may be '' or "slug"
        # need to return / or /foo/ or /slug/foo/
        if self.navigation_slug:
            if site_slug:
                return '/{}/{}/'.format(site_slug, self.navigation_slug)
            else:
                return '/%s/' % self.navigation_slug
        else:
            if site_slug:
                return '/%s/' % site_slug
            else:
                return '/'


class NavigationWrapper:
    """ Wraps a Microsite NavigationItem, adding the current slug. """

    def __init__(self, nav_item, slug):
        """ Initialize. """
        self.nav_item = nav_item
        self.slug = slug

    @property
    def url(self):
        """ The relative url for this navigation item. """
        return self.nav_item.get_url(self.slug)

    @property
    def name(self):
        """ The name for the tab. """
        return self.nav_item.name

    @property
    def icon(self):
        """ The icon for the tab. """
        return self.nav_item.icon.lower()

    @property
    def pageid(self):
        """ The pageid for the tab. """
        return self.nav_item.pageid


class Layout:
    """ Represent a layout """

    def __init__(self, layout_id, name, phone_theme, desktop_theme, description):
        """initialization"""
        if not layout_id:
            raise ValueError('id is required')
        if not name:
            raise ValueError('name is required')

        self.layout_id = layout_id
        self.name = name
        self.phone_theme = phone_theme
        self.desktop_theme = desktop_theme
        self.description = description

    def preview_url(self, theme_id):
        """ Returns the path to the preview image for this theme. """
        return '/static/images/themes/%s.png' % theme_id

    @property
    def layout_description(self):
        """ Returns the textual description of this layout. """
        return self.layout_description

    @property
    def phone_preview_url(self):
        """ Returns the path to the preview image for this layout. """
        return self.preview_url(self.phone_theme)

    @property
    def desktop_preview_url(self):
        """ Returns the path to the preview image for this layout. """
        return self.preview_url(self.desktop_theme)
