""" Microsite domain methods. """
import logging
from copy import deepcopy

from app.models.partner import Partner
from vautil import tinyid
from datetime import datetime, timedelta

from fantasm.fsm import startStateMachine
from google.appengine.ext import ndb, deferred
from google.appengine.api.namespace_manager.namespace_manager import get_namespace

from app.constants import MIN_ACTIVITY_INTERVAL_HOURS, MAX_LOGIN_HISTORY_LENGTH, LOGO, FAVICON, SOCIAL_QUEUE, \
    ADD_ACCOUNT_GROUP_COUNTDOWN, DEFAULT_COMPANY_NAME
from app.domain import exceptions
from app.domain.batch_import import update_batch_import, create_batch_import, get_msid_for_pmsid
from app.domain.blob_mappings import get_resized_image_url
from app.domain.constants import Keys
from app.domain.exceptions import SiteSpidExistsException
from app.domain.microsite.objects import Microsite, Layout
from app.domain.social import SitesVSocial
from app.domain.url_mappings import lookup_hostslugs_for_msid, \
    add_hostslug_msid_mapping, get_hostslug_msid_mapping, \
    set_recently_created_slug_for_msid_in_memcache
from app.domain.validation import require_args
from app.domain.vbc import VbcAccountGroup, get_ms_pid
from app.domain.workflow.exceptions import ValidationException
from app.keys import MICROSITE_KEYS
from app.models.blob_mappings import MsidBlobMapping as MsidBlobMappingModel
from app.models.microsite import Microsite as MicrositeModel, Navigation as NavigationModel, \
    MicrositeTombstone as MicrositeTombstoneModel, ACCOUNT_GROUP_KWARG_MAPPING


from coresdk import CSApiException
from app.domain.vbcsdk_keys import API_KEY as VBC_KEY
from vbcsdk import ApiException as BusinessCenterApiException, Conflict409Error, NotFound404Error

import phonenumbers


def phone_number_digits(phone):
    """
    Strip phone number down to its digits.
    """
    return phonenumbers.normalize_digits_only(phone)


@require_args
def create_account_group_for_microsite(ms_entity):
    """ Returns a new account group for the given microsite. """
    name = ms_entity.name or DEFAULT_COMPANY_NAME

    try:
        microsite = Microsite.from_model(ms_entity)
        common_data = microsite.get_common_data()
        common_data.pop(MICROSITE_KEYS.NAME, None)  # TODO: pop until new vbc sdk in place (name is positional)
        account_group = VbcAccountGroup.create_account_group(microsite.pid, name, spid=microsite.spid, **common_data)
        return account_group
    except ValueError as ve:
        logging.critical("VBC account group create failed due to validation error. %s", ve.args[0])
        raise ValidationException({'Error': ['Site "{}" could not be created. {}'.format(name, ve.args[0])]})
    except BusinessCenterApiException as e:
        logging.critical('VBC account group create failed. %s', e.args[0])
        raise exceptions.AccountGroupInvalidException('Site "{}" could not be created. {}'.format(name, e.args[0]))
    except Exception as e:
        logging.critical("Unexpected exception. %s", str(e))
        raise e


def update_microsite_entity(msid, pid=None, notify_vbc=True, **kwargs):
    """ Updates an existing microsite entity.
        passing  the phone argument in kwargs will no longer do anything. Instead, pass phone_work
    @param msid the microsite MSID to update
    @param pid the namespace in which the microsite exists, defaults to the namespace manager's current namespace
    @raises MicrositeNotFoundException if the microsite is not found
    """

    if not msid:
        raise ValueError('msid is required.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Microsite must be updated in a pid context (either from the namespace manager, or '
                         'an explicit pid kwarg).')

    ms_model = MicrositeModel.get_by_msid(msid, pid)
    if not ms_model:
        raise exceptions.MicrositeNotFoundException()

    sso_token = kwargs.get(MICROSITE_KEYS.SSO_TOKEN)
    # ensure no microsites, other than that being updated, are using the given sso token
    ms_sso = MicrositeModel.lookup_by_sso_token(sso_token, pid=pid)
    if sso_token and ms_sso and ms_sso.msid != ms_model.msid:
        raise ValueError(f"{MICROSITE_KEYS.SSO_TOKEN} must be unique within a namespace.")

    old_spid = ms_model.spid
    old_agid = ms_model.agid

    # mandatory attributes
    kwargs[MICROSITE_KEYS.MSID] = msid
    kwargs[MICROSITE_KEYS.PID] = pid
    kwargs[MICROSITE_KEYS.SPID] = kwargs.get(MICROSITE_KEYS.SPID) or ms_model.spid
    kwargs[MICROSITE_KEYS.SSO_TOKEN] = kwargs.get(MICROSITE_KEYS.SSO_TOKEN) or ms_model.sso_token

    if MICROSITE_KEYS.PMSID in kwargs:
        old_pmsid = ms_model.pmsid
        new_pmsid = kwargs.get(MICROSITE_KEYS.PMSID)
        # this will throw an exception of batch import with pmsid already exists for this pid
        update_batch_import(ms_model.msid, old_pmsid, new_pmsid, pid=pid)

    kwargs = _strip_non_numeric_characters_from_phone_numbers(**kwargs)

    if kwargs.get('geo'):
        kwargs['geo'] = {'lat': kwargs.get('geo').lat, 'lon': kwargs.get('geo').lon}

    account_group_kwargs = {}

    original_kwargs = deepcopy(kwargs)
    for ms_name, account_group_name in ACCOUNT_GROUP_KWARG_MAPPING.items():
        if ms_name in list(kwargs.keys()):
            account_group_kwargs[account_group_name] = kwargs.pop(ms_name)

    for k, v in kwargs.items():
        if k not in (MICROSITE_KEYS.AGID, MICROSITE_KEYS.ACCOUNT_GROUP_ID):
            setattr(ms_model, k, v)

    for k, v in account_group_kwargs.items():
        setattr(ms_model.account_group, k, v)

    @ndb.transactional
    def transaction_callback():
        """ The core of this update method """

        if not ms_model.agid:
            account_group = create_account_group_for_microsite(ms_model)
            ms_model.account_group.account_group_id = account_group[VBC_KEY.AGID]
            ms_model.account_group.social_profile_id = account_group[VBC_KEY.SPID]

        logging.info("Saving microsite model %s", ms_model.to_dict())
        ms_model.put()

        if ms_model.spid != old_spid or ms_model.agid != old_agid:
            defer_url = Keys.DEFER_URL_ROOT + 'MicroSite/RegisterOrCreateSocialProfile/'
            deferred.defer(defer_register_microsite, msid, pid=pid,
                           _url=defer_url, _transactional=True, _queue='social')
        elif notify_vbc:
            ms_domain = Microsite.from_model(ms_model)
            try:
                common_data = ms_domain.get_common_data_for_provided_list_of_args(list(original_kwargs.keys()))
                VbcAccountGroup.update_account_group(pid, ms_model.agid, **common_data)
            except (ValueError, BusinessCenterApiException) as e:
                logging.error("Update to VBC account group was unsuccessful. Error: %s", e.args[0])

    transaction_callback()

    return ms_model


def _strip_non_numeric_characters_from_phone_numbers(**kwargs):
    """
    Takes the non-numeric (including spaces) out of phone_work if it exists in kwargs.
    Note: I do not want to put phone_work into kwargs if they aren't already there.
    """

    if MICROSITE_KEYS.PHONE_WORK in kwargs:
        kwargs[MICROSITE_KEYS.PHONE_WORK] = [phone_number_digits(number)
                                             for number in kwargs.get(MICROSITE_KEYS.PHONE_WORK, [])]

    return kwargs

def generate_msid():
    """ generate a msid """
    return 'MS-' + tinyid.TinyIDGenerator(namespace='MS').generate_tinyid().upper()


# R0912:136:create_microsite_entity: Too many branches (21/20)
# pylint: disable=R0912
def create_microsite_entity(pid=None, **kwargs):
    """ Creates a new microsite entity in the namespace identified by pid.

    @param pid = the namespace to create the microsite entity in, defaults to namespace manager's current namespace
    @param kwargs - passes through to the model constructor
    """
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Microsite must be created in a pid context (either from the namespace manager, or '
                         'an explicit pid kwarg).')
    pid = pid.upper()

    sso_token = kwargs.get(MICROSITE_KEYS.SSO_TOKEN)
    if sso_token and MicrositeModel.lookup_by_sso_token(sso_token, pid=pid):
        raise ValueError('sso_token must be unique within a namespace.')

    msid = kwargs.get(MICROSITE_KEYS.MSID, None)
    if not msid:
        msid = generate_msid()

    key = MicrositeModel.build_key(msid, pid=pid)

    pmsid = kwargs.get(MICROSITE_KEYS.PMSID)

    create_batch_import(msid, pmsid, pid=pid)

    kwargs = kwargs or {}

    kwargs[MICROSITE_KEYS.KEY] = key
    kwargs[MICROSITE_KEYS.MSID] = msid
    kwargs[MICROSITE_KEYS.PID] = pid

    kwargs = _strip_non_numeric_characters_from_phone_numbers(**kwargs)

    # change kwargs with '' value to None
    for k, v in kwargs.items():
        if v == '':
            kwargs[k] = None

    if kwargs.get(MICROSITE_KEYS.WEBSITE):
        if not kwargs[MICROSITE_KEYS.WEBSITE].startswith('http://'):
            kwargs[MICROSITE_KEYS.WEBSITE] = 'http://%s' % kwargs[MICROSITE_KEYS.WEBSITE]

    if kwargs.get(MICROSITE_KEYS.AGID):
        agid = kwargs[MICROSITE_KEYS.AGID]
        agid_keys = MicrositeModel.lookup_by_agid(agid, pid=pid, keys_only=True)
        if agid_keys:
            raise exceptions.AccountGroupInvalidException('A site with agid "%s" already exists. '
                                                          'Site will not be created' % agid)

    # No value passed for parameter 'kwds' in function call
    # pylint does not appear to understand how this decorator works
    @ndb.transactional(xg=True) # pylint: disable=E1120
    def transaction_callback():
        """ The core of this create """
        # Popping these values off because they won't play well with MicrositeModel
        hostname = kwargs.pop(MICROSITE_KEYS.HOSTNAME, None)
        slug = kwargs.pop(MICROSITE_KEYS.SLUG, None)

        if hostname and slug:
            # due to a race condition there's a chance this slug has been reserved
            # by another process... check if it has already been claimed
            if get_hostslug_msid_mapping(hostname, slug=slug):
                # Last Resort: the slug is still not unique, append part of the MSID to it
                slug = slug + '-' + msid.split('-')[1]
            # Transactional is False because we're already in a transaction...
            host_slug_mapping = add_hostslug_msid_mapping(hostname, slug, pid, msid, transactional=False)
            logging.info("Created Host Slug Mapping. HostSlug: '%s'. MSID: '%s'. PID: '%s'.",
                         host_slug_mapping.hostslug, host_slug_mapping.msid, host_slug_mapping.pid)

            set_recently_created_slug_for_msid_in_memcache(msid, slug)
        ms_entity = MicrositeModel(**kwargs)

        account_group = {}
        if ms_entity.agid:
            # check if account group exists
            account_group = VbcAccountGroup.get_account_group(pid, ms_entity.agid)
            accounts = account_group.get(VBC_KEY.ACCOUNTS) or []
            ms_account_ids = [account.get(VBC_KEY.ACCOUNT_ID) for account in accounts
                              if account.get(VBC_KEY.PRODUCT_ID) == 'MS']
            if ms_account_ids:
                raise exceptions.AccountGroupInvalidException('Sites "%s" have already been added to Account '
                                                              'Group "%s". Site will not be created.' %
                                                              (ms_account_ids, ms_entity.agid))
            ms_entity.account_group.social_profile_id = account_group.get(VBC_KEY.SPID)
        else:
            account_group = create_account_group_for_microsite(ms_entity)
            ms_entity.account_group.account_group_id = account_group[VBC_KEY.ACCOUNT_GROUP_ID]
            ms_entity.account_group.social_profile_id = account_group[VBC_KEY.SOCIAL_PROFILE_ID]

        ms_entity.put()
        # update_listing_profile(account_group.account_group_id, account_group.hourOfOperation)
        defer_url = Keys.DEFER_URL_ROOT + 'MicroSite/RegisterMicrosite/'
        deferred.defer(defer_register_microsite, msid, pid=pid,
                       _url=defer_url, _transactional=True, _queue=SOCIAL_QUEUE)
        return ms_entity

    new_ms = transaction_callback()
    return Microsite.from_model(new_ms)


def defer_register_microsite(msid, pid=None):
    """
    Register a microsite against an Account Group and Social Profile. Microsite must have an agid.

    If Microsite does not have a spid, get it from the Account Group.
    Add the Microsite to the Account Group.
    Register the spid against a Social Profile and associate the Microsite with that profile.
    """

    # TODO figure out why we are trying to create a new account group here.  It should have been created already in
    # the parent.

    if not msid:
        raise deferred.PermanentTaskFailure('msid is required.')
    pid = pid or get_namespace()
    if not pid:
        raise deferred.PermanentTaskFailure('pid is required.')
    pid = pid.upper()

    ms = MicrositeModel.get_by_msid(msid, pid)
    if not ms:
        msg = 'Site "%s" with pid "%s" does not exist. ' \
              'Site will not be register with Account Group or Social Profile.' % (msid, pid)
        raise deferred.PermanentTaskFailure(msg)
    if not ms.agid:
        raise deferred.PermanentTaskFailure('agid is required.')

    # core requires the host slug mapping to exist before we call vsocial.register_site_as_social_service
    # so quietly fail this task to happen later until the mapping is created.
    if not lookup_hostslugs_for_msid(pid, msid):
        msg = 'no host slug mapping created yet, deferring social service registration. msid: {}; pid: {}'.format(
            msid, pid
        )
        logging.debug(msg)
        raise deferred.SingularTaskFailure(msg)

    try:
        account_group = VbcAccountGroup.get_account_group(pid, ms.agid)
    except NotFound404Error:
        # if account group has been deleted then delete the microsite
        msg = 'Account Group "{}" does not exist. Deleting microsite "{}" with pid "{}".'.format(ms.agid, msid, pid)
        delete_microsite(msid, pid=pid)
        raise deferred.PermanentTaskFailure(msg)

    if not ms.spid:
        ms.spid = account_group[VBC_KEY.SPID]
        ms.put()

    vsocial = SitesVSocial(msid, pid=pid)
    try:
        # Register the microsite with the social profile
        vsocial.register_social_profile()

        # Associate the microsite with the social profile
        service = vsocial.register_site_as_social_service()
        logging.info('Register site as a social service result: %s', service)
    except SiteSpidExistsException:
        msg = 'Unable to register Site "{}" with Social Profile because spid "{}" already exists.'.format(msid, ms.spid)
        logging.exception(msg)
        raise deferred.PermanentTaskFailure(msg)
    except CSApiException as csae:
        logging.error(csae.args[0])
        raise deferred.SingularTaskFailure(csae.args[0])
    finally:
        # Register microsite against account group and CSs
        # defer it to task queue, but wait a few seconds to do it
        deferred.defer(defer_add_account_to_account_group, pid, msid, ms.agid,
                       _countdown=ADD_ACCOUNT_GROUP_COUNTDOWN, _queue=SOCIAL_QUEUE)


def defer_add_account_to_account_group(pid, msid, agid):
    """
    Deferrable call to VbcAccountGroup.add_account_to_account_group.
    Assumes that all variables are validated since they are validated in defer_register_microsite
    """
    try:
        VbcAccountGroup.add_account_to_account_group(pid, msid, agid)
    except ValueError as ve:
        raise deferred.SingularTaskFailure(ve.args[0])
    except NotFound404Error:
        # if account group has been deleted then delete the microsite
        msg = 'Account Group "{}" does not exist. Deleting microsite "{}" with pid "{}".'.format(agid, msid, pid)
        delete_microsite(msid, pid=pid)
        raise deferred.PermanentTaskFailure(msg)
    except Conflict409Error as e:
        logging.warning(e.args[0])
    except BusinessCenterApiException as e:
        msg = 'Received error status "%d" adding Site "%s" with pid "%s" to Account Group. ' \
              'Site was not be registered, but registration will be retried. Original message: %s'\
              % (e.statusCode, msid, pid, e.args[0])
        raise deferred.SingularTaskFailure(msg)


def validate_msid_for_msid_or_pmsid(msid=None, pmsid=None, pid=None):
    """
    Validates that an msid exists for an msid or pmsid.
    Either msid or pmsid may be None. Returns the msid if it is valid. None otherwise.
    """
    if not msid and not pmsid:
        raise ValueError('Either msid or pmsid is required.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Microsite must be looked up in a pid context (either from the datastore namespace, or '
                         'an explicit pid kwarg).')

    if msid:
        entity = get_microsite_model(msid, pid)
        if not entity:
            return None
    else:
        msid = get_msid_for_pmsid(pid, pmsid)
        if not msid:
            return None

    return msid


def get_microsite_for_msid_or_pmsid(msid=None, pmsid=None, pid=None):
    """
    Looks up a microsite by msid or pmsid. Either msid or pmsid may be None.
    """
    if not msid and not pmsid:
        raise ValueError('Either msid or pmsid is required.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Microsite must be looked up in a pid context (either from the datastore namespace, or '
                         'an explicit pid kwarg).')

    ms = None
    if msid:
        ms = get_microsite(msid, pid=pid)
    else:
        try:
            ms = get_microsite_for_pmsid(pmsid, pid=pid)
        except ValueError:
            # Value error raised if msid does not exist
            pass

    return ms


def get_microsite_model(msid=None, pid=None, agid=None):
    """
    Looks up a microsite by msid. To populate the entire entity with nav items and partner information use
    get_microsite.
    """
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Microsite must be looked up in a pid context (either from the datastore namespace, or '
                         'an explicit pid kwarg).')
    if msid is None and agid is None:
        raise ValueError('MSID or AGID is required to look up a microsite.')
    elif msid:
        return MicrositeModel.get_by_msid(msid, pid)
    elif agid:
        return MicrositeModel.lookup_by_agid(agid, pid)


def get_microsite(msid, pid=None):
    """ Looks up a microsite by msid.

    @param msid the msid (Microsite key_name) to lookup
    @param pid the namespace to look in, defaults to the namespace manager's current namespace
    """
    if not msid:
        raise ValueError('msid is required.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Microsite must be looked up in a pid context (either from the datastore namespace, or '
                         'an explicit pid kwarg).')
    pid = pid.upper()

    # retrieve Microsite, Navigation, and BlobMapping entities in one db.get
    ms_key = MicrositeModel.build_key(msid, pid=pid)
    keys = [ms_key]
    nav_keys = NavigationModel.build_predictive_keys(msid, pid=pid)
    keys.extend(nav_keys)
    blob_mapping_keys = MsidBlobMappingModel.build_predictive_keys(msid, pid=pid)
    keys.extend(blob_mapping_keys)

    entities = ndb.get_multi(keys)

    ms_entity = entities[0]
    nav_entities = entities[1:1 + len(nav_keys)]
    blob_mapping_entities = entities[1 + len(nav_keys):]

    partner = Partner.get(pid)
    ms = Microsite.from_model_and_populate(
        ms_entity,
        partner,
        nav_entities,
        blob_mapping_entities)

    return ms


def get_microsites(msids, pid=None):
    """ Looks up microsites by a list of msids.

        @param msids the list of msid (Microsite key_name) to lookup
        @param pid the namespace to look in, defaults to the namespace manager's current namespace
    """
    if not msids:
        raise ValueError('msid is required.')
    if not isinstance(msids, list):
        raise TypeError('msids must be a list.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Microsite must be looked up in a pid context (either from the datastore namespace, or '
                         'an explicit pid kwarg).')
    pid = pid.upper()

    # remove any duplicates
    msid_set = set(msids)
    ms_keys = [MicrositeModel.build_key(msid, pid=pid) for msid in msid_set]

    len_nav_items = []
    len_blob_items = []
    nav_keys = []
    blob_keys = []
    # get the nav and blob mapping keys for all microsites
    for msid in msid_set:
        nav_keys_set = NavigationModel.build_predictive_keys(msid, pid=pid)
        len_nav_items.append(len(nav_keys_set))
        nav_keys.extend(nav_keys_set)

        blob_keys_set = MsidBlobMappingModel.build_predictive_keys(msid, pid=pid)
        len_blob_items.append(len(blob_keys_set))
        blob_keys.extend(blob_keys_set)

    # get the entities
    ms_entities = ndb.get_multi(ms_keys)
    nav_entities = ndb.get_multi(nav_keys)
    blob_mapping_entities = ndb.get_multi(blob_keys)

    # add entities to induvidual microsites
    partner = Partner.get(pid)
    ms_list = []
    nav_cursor = 0
    blob_cursor = 0
    for ms_cursor in range(0, len(ms_entities)):
        if ms_entities[ms_cursor] is not None:
            ms = Microsite.from_model_and_populate(
                ms_entities[ms_cursor],
                partner,
                nav_entities[nav_cursor:nav_cursor+len_nav_items[ms_cursor]],
                blob_mapping_entities[blob_cursor:blob_cursor+len_blob_items[ms_cursor]]
            )
            if ms:
                ms_list.append(ms)
        nav_cursor += len_nav_items[ms_cursor]
        blob_cursor += len_blob_items[ms_cursor]

    return ms_list


def get_json_ready_microsite_models(msids, pid=None):
    """ Returns microsite models that are without the non-json-friendly properties by a list of msids.

        @param msids the list of msid (Microsite key_name) to lookup
        @param pid the namespace to look in, defaults to the namespace manager's current namespace

    """
    ms_models = get_microsite_models(msids, pid)

    ms_dicts = [ms.to_dict() for ms in ms_models]
    for ms_dict in ms_dicts:
        ms_dict.pop(MICROSITE_KEYS.CREATED)
        ms_dict.pop(MICROSITE_KEYS.UPDATED)
        ms_dict.pop(MICROSITE_KEYS.COLOR)
        ms_dict.pop(MICROSITE_KEYS.PHONE_THEME)
        ms_dict.pop(MICROSITE_KEYS.DESKTOP_THEME)
        ms_dict.pop(MICROSITE_KEYS.GEO)
        ms_dict.pop(MICROSITE_KEYS.ACTIVE_DATE_TIME)
        ms_dict.pop(MICROSITE_KEYS.LOGIN_COUNT)
        ms_dict.pop(MICROSITE_KEYS.LAST_LOGIN)
        ms_dict.pop(MICROSITE_KEYS.LOGIN_HISTORY)

    return ms_dicts


def get_microsite_models(msids, pid=None):
    """ Looks up microsites by a list of msids.

        @param msids the list of msid (Microsite key_name) to lookup
        @param pid the namespace to look in, defaults to the namespace manager's current namespace
    """
    if not msids:
        raise ValueError('msid is required.')
    if not isinstance(msids, list):
        raise TypeError('msids must be a list.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Microsite must be looked up in a pid context (either from the datastore namespace, or '
                         'an explicit pid kwarg).')
    pid = pid.upper()

    # remove any duplicates
    msid_set = set(msids)
    ms_keys = [MicrositeModel.build_key(msid, pid=pid) for msid in msid_set]

    # get the entities
    ms_entities_set = set(ndb.get_multi(ms_keys)).difference({None})

    return list(ms_entities_set)


def get_microsite_for_pmsid(pmsid, pid=None):
    """ Returns the microsite corresponding the given pmsid in the pid (or current) namespace.

    @param pmsid the pmsid (Site Id) to lookup
    @param pid the namespace to look in, defaults to the namespace manager's current namespace
    """

    if not pmsid:
        raise ValueError('pmsid is required.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Microsite must be looked up in a pid context (either from the datastore namespace, or '
                         'an explicit pid kwarg).')
    pid = pid.upper()

    msid = get_msid_for_pmsid(pid, pmsid)
    if not msid:
        raise ValueError('There is no microsite associated with pmsid ({}) on partner ({})'.format(pmsid, pid))

    return get_microsite(msid, pid=pid)


def delete_microsite(msid, pid=None, skip_tombstone=False):
    """ Starts the state machine to delete the microsite with the given msid and its dependencies """

    if not msid:
        raise ValueError('msid is required.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Microsite must be in a pid context (either from the datastore namespace, or '
                         'an explicit pid kwarg).')
    pid = pid.upper()

    ms_key = MicrositeModel.build_key(msid, pid=pid)
    context = {
        MICROSITE_KEYS.MSID: msid,
        MICROSITE_KEYS.PID: pid,
        'microsite_key': ms_key,
        'skip_tombstone': skip_tombstone,
    }

    startStateMachine('RemoveMicrosite', context)


def lookup_all_microsites(pid=None, count=100):
    """ Returns by default 100 Microsites in the current namespace. Does not retrieve navigation items.

    @param pid The pid to lookup microsites for. Defaults to the namespace manager's current namespace.
    @param count The number to return.
    """
    result = MicrositeModel.lookup_all(pid=pid, count=count)
    microsites = [Microsite.from_model(m) for m in result.results]
    return microsites


def get_microsite_for_sso_token(sso_token, pid=None):
    """
    Get a msid for the given sso_token. Returns None if no msid found.
    """
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('pid is required.')

    ms = MicrositeModel.lookup_by_sso_token(sso_token, pid=pid)
    if ms:
        return ms

    # Try to get the site using sso_token as the msid
    return MicrositeModel.build_key(sso_token, pid).get()


def get_microsite_for_agid(agid, pid=None):
    """
    Get a microsite for the given agid. Returns None if no microsite found.
    """
    return MicrositeModel.lookup_by_agid(agid, pid=pid)


def get_simple_microsite_summary(msid, pid=None):
    """ Returns a single microsite summary dict in the current namespace. """
    if not msid:
        return None

    ms_dicts = get_simple_microsite_summaries([msid], pid=pid)
    return ms_dicts[0] if len(ms_dicts) > 0 else None


def get_simple_microsite_summaries(msids, pid=None):
    """ Returns a list of microsite summary dicts in the current namespace. """
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Microsites must be looked up in a pid context (either from the datastore namespace, or '
                         'an explicit pid kwarg).')

    keys = [MicrositeModel.build_key(msid, pid=pid) for msid in msids]
    entities = ndb.get_multi(keys)

    ms_dicts = []
    for entity in entities:
        if not entity:
            logging.warn('get_simple_microsite_summaries encountered None entity for partner "%s". '
                         'Entity will be skipped and omitted from result set.', pid)
            continue

        microsite = Microsite.from_model(entity)
        blob_mapping_keys = MsidBlobMappingModel.build_predictive_keys(entity.msid, pid=pid)
        microsite.populate_blob_mappings_from_entities(ndb.get_multi(blob_mapping_keys))

        microsite_dict = microsite.__dict__
        microsite_dict[MICROSITE_KEYS.IDENTIFIER] = microsite_dict[MICROSITE_KEYS.PMSID]

        logo_mapping = microsite.blob_mapping.get(LOGO)
        favicon_mapping = microsite.blob_mapping.get(FAVICON)
        urls = lookup_hostslugs_for_msid(microsite.pid, microsite.msid)

        if logo_mapping:
            microsite_dict[MICROSITE_KEYS.THUMBNAIL_URL] = get_resized_image_url(logo_mapping, max_width=50,
                                                                                 max_height=50)
        if favicon_mapping:
            microsite_dict[MICROSITE_KEYS.FAVICON_URL] = favicon_mapping.get_url()
        if urls:
            microsite_dict[MICROSITE_KEYS.URL] = urls[0]

        del microsite_dict[MICROSITE_KEYS.SPID]
        del microsite_dict[MICROSITE_KEYS.AGID]
        del microsite_dict[MICROSITE_KEYS.PMSID]
        del microsite_dict[MICROSITE_KEYS.NAVIGATION_ITEMS]
        del microsite_dict[MICROSITE_KEYS.BLOB_MAPPING]
        del microsite_dict[MICROSITE_KEYS.GEO]
        del microsite_dict[MICROSITE_KEYS.BILLING_CODE]
        del microsite_dict[MICROSITE_KEYS.MARKET_ID]
        del microsite_dict[MICROSITE_KEYS.LOGIN_COUNT]
        del microsite_dict[MICROSITE_KEYS.LAST_LOGIN]
        del microsite_dict[MICROSITE_KEYS.LOGIN_HISTORY]
        ms_dicts.append(microsite_dict)

    return ms_dicts


def get_microsite_count_for_partner(pid):
    """ Returns the total number of microsites for a given partner. """
    return MicrositeModel.get_microsite_count_for_partner(pid)


def get_new_microsite_count_for_partner_within_given_time_period(pid, starting_time, ending_time):
    """
    Returns the number of microsites that are added for a given parnter in a given time period
    including microsites created from starting time up till but excluding ending time
    """
    return MicrositeModel.get_new_microsite_count_for_partner_within_given_time_period(
        pid=pid, starting_time=starting_time, ending_time=ending_time
    )


def get_total_new_microsite_count_for_given_time_period(starting_time, ending_time):
    """
    Returns total numbers of new microsite added in datastore during giben time period
    """
    partners = Partner.lookup_all_partners()
    total = 0
    for p in partners:
        total += get_new_microsite_count_for_partner_within_given_time_period(p.pid, starting_time, ending_time)

    return total


def get_deleted_microsite_count_for_partner_within_given_time_period(pid, starting_time, ending_time):
    """
    Returns number of deleted microsites during for given partner in given time period
    """
    return MicrositeTombstoneModel.get_count_for_partner_within_given_time_period(
        pid=pid, starting_time=starting_time, ending_time=ending_time
    )


def get_total_deleted_microsite_count_for_given_time_period(starting_time, ending_time):
    """
    Returns number of deleted microsites during given time period for all partners
    """
    partners = Partner.lookup_all_partners()
    total = 0
    for p in partners:
        total += get_deleted_microsite_count_for_partner_within_given_time_period(p.pid, starting_time, ending_time)

    return total


def add_microsite_activity(msid, pid=None, login_time=None):
    """ Add microsite usage activity to Microsite model """
    if not msid:
        raise ValueError('msid is required.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('pid is required.')
    ms = MicrositeModel.get_by_msid(msid, pid=pid)
    if not ms:
        raise exceptions.MicrositeNotFoundException()

    now = login_time or datetime.utcnow()
    min_interval_time = timedelta(hours=MIN_ACTIVITY_INTERVAL_HOURS)
    if not ms.login_count or not ms.last_login or ((now - ms.last_login) >= min_interval_time):
        ms.login_count += 1
        ms.last_login = now
        if (ms.login_count > MAX_LOGIN_HISTORY_LENGTH) and ms.login_history:
            del ms.login_history[0]
        ms.login_history.append(now)
        ms.put()

    return ms


@require_args
def get_business_profile_completeness(microsite):
    """
    Returns the percent completeness for the microsite business profile.
    """
    name = ''
    if microsite.name != DEFAULT_COMPANY_NAME:
        name = microsite.name
    profile_fields = [name,
                      microsite.phone,
                      microsite.email,
                      microsite.website,
                      microsite.categories,
                      microsite.address1,
                      microsite.country,
                      microsite.state,
                      microsite.city,
                      microsite.zipcode,
                      microsite.facebook_url,
                      microsite.twitter_url,
                      microsite.rss_url,
                      microsite.youtube_url,
                      microsite.linkedin_url,
                      microsite.instagram_url,
                      microsite.pinterest_url,
                      microsite.foursquare_url]

    return {
        'type': 'Percentage',
        'num_complete': len([field for field in profile_fields if bool(field)]),
        'num_total': len(profile_fields)
    }
