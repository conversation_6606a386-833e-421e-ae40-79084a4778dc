"""
Helpers to build json ld markup
"""
from app.domain.microsite.review import LOCATION_PAGE_REVIEW_SOURCE
from app.domain.microsite.schema_type import get_valid_google_review_schema


def build_review(source_id, name=None, review_body=None, author=None, date_published=None, review_rating=None):
    """
    Build a json ld aggregate rating. this must be added in the json ld as "aggregateRating"
    We can not build review meta data that we are not in control of:
        https://developers.google.com/search/docs/data-types/review-snippet#technical-guidelines


    :param name: The name of the reviewer
    :param review_body: The content of the review
    :param author: The author of the review
    :param date_published: The date the review was published
    :param review_rating: The rating of the review out of 5
    :param source_id: The Vendasta source id used for determining
    if we can build json_ld according to Googles guidelines
    :return: A dictionary containing hte aggregate rating
    """
    if source_id != LOCATION_PAGE_REVIEW_SOURCE:
        return

    json_ld = {
        '@type': 'Review'
    }

    if author:
        json_ld['author'] = {
            '@type': 'Person',
            'name': author
        }
    else:
        json_ld['author'] = {
            '@type': 'Person',
            'name': "Anonymous"
        }
    if date_published:
        json_ld['datePublished'] = date_published
    if name:
        json_ld['name'] = name
    if review_body:
        json_ld['reviewBody'] = review_body
    if review_rating:
        json_ld['reviewRating'] = {
            '@type': 'Rating',
            'ratingValue': review_rating
        }
    return json_ld


def build_aggregate_rating(rating_value, review_count):
    """
    Build a json ld aggregate rating. this must be added in the json ld as "aggregateRating"

    :param rating_value: The average rating of the reviews
    :param review_count: The number of reviews used to get this rating
    :return: A dictionary containing the aggregate rating
    """

    if rating_value and review_count:
        return {
            '@type': 'AggregateRating',
            'ratingValue': rating_value,
            'reviewCount': review_count,
        }
    return None


def build_jsonld_type(name=None, tax_id=None):
    """
    Build the json ld section of the page

    :param name: The name of the entity
    :param tax_id: The vendasta taxonomy id
    :return: A dictionary of json ld
    """

    json_ld = {'@context': 'http://schema.org',
               '@type': get_valid_google_review_schema(tax_id)}

    if name:
        json_ld['name'] = name

    return json_ld
