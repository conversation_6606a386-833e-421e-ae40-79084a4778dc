"""
Review Comment domain functions
"""
import cgi
from collections import defaultdict

import elastic
from elastic.documents.review_comment import ReviewCommentDocumentV2

from app.domain.microsite.review import format_first_name_last_initial_for_display, get_reviews_by_source
from app.domain.microsite.review import get_review_url


# pylint: disable=W1201
# pylint: disable=WV001

def get_review_comments(review_ids, account_group_id):
    """ Get review comments and the number of comments from ElasticSearch """
    review_ids = review_ids if isinstance(review_ids, list) else [review_ids]
    return _get_review_comments_by_review_id(review_ids, account_group_id)


def get_reviews_with_comments_by_source(agid, sources, offset=0, sort_published=False, to_json_str=False, limit=10):
    """ Get reviews with comments. """
    results = get_reviews_by_source(agid, sources, offset=offset, sort_published=sort_published, limit=limit)
    feed_data = [document.to_dict(to_json_str=to_json_str) for document in results]
    cursor = results.next_offset if results.has_more else None
    review_ids = [r['review_id'] for r in feed_data]
    _, counts = get_review_comments(review_ids, agid)
    for r in feed_data:
        r['reviewer_name'] = format_first_name_last_initial_for_display(r['reviewer_name'])
        r['comment_count'] = counts[r['review_id']]
        if ('url' not in list(r.keys()) or not r['url']) and r.get("source_id") == 12000:
            r['url'] = get_review_url(agid, r['review_id'])
    return feed_data, cursor


def _get_review_comments_by_review_id(review_ids, account_group_id):
    """
    Build the ElasticSearch query for review comments.
    :param   review_ids One or many review_ids, allowing for single search or list search
    :return: comments   A list of comment dictionaries
             counts     A dictionary of comment counts, indexed by review id
    """
    if not review_ids:
        return [], {}
    search = ReviewCommentDocumentV2.search()
    search.add_filter(elastic.FilterBuilder.term(ReviewCommentDocumentV2.account_group_id, account_group_id))
    search.add_filter(elastic.FilterBuilder.terms(ReviewCommentDocumentV2.review_id, review_ids))
    term_filter_aggregations = []
    for review_id in review_ids:
        term_filter_aggregations.append(elastic.FilterBuilder.term(ReviewCommentDocumentV2.review_id, review_id))
    search.add_aggregation(elastic.AggregationBuilder.filters(filters=term_filter_aggregations, name='comment_counts'))
    search.add_sort(elastic.SortBuilder.field(ReviewCommentDocumentV2.created))
    response = search.execute()
    comments = [_convert_review_comment_into_dict(review_comment) for review_comment in response]
    counts = defaultdict(int, {
        review_ids[index]: bucket.doc_count
        for index, bucket in enumerate(response.aggregations.filters('comment_counts').buckets)
    })
    return comments, counts


def _convert_review_comment_into_dict(review_comment):
    """ Build a dictionary of required values for the frontend. """
    return {
        'commenter_name': format_first_name_last_initial_for_display(cgi.escape(review_comment.commenter_name)),
        'comment_text': cgi.escape(review_comment.comment_text),
        'created': review_comment.created.isoformat(),
        'posted_by_owner': review_comment.posted_by_owner,
        'comment_id': review_comment.comment_id,
    }
