""" Defines a mapping between our taxonomy categories and types from http://schema.org.
    Information on Rich Snippets can be found at http://developers.google.com/structured-data/rich-snippets/reviews.

    Review type definition:

        itemReviewed*       The item that is being reviewed. This needs to be a valid schema.org type.
        reviewBody          The actual body of the review.
        author*             The author of the review. The reviewer's name needs to be a valid name.
        datePublished       The date that the review was published, in ISO 8601 date format.
        reviewRating        The rating given in this review. A nested schema.org/Rating (or more specific subtype).
            ratingValue     A numerical quality rating for the item.
            bestRating      The highest value allowed in this rating system. If omitted, 5 is assumed.
            worstRating     The lowest value allowed in this rating system. If omitted, 1 is assumed.

    If the rating system is not on a 5-point scale, both bestRating and worstRating are required properties.
"""

VTAX_TO_SCHEMA_MAPPING = {
    'active': 'PlayAction',
    'amateursportsteams': 'SportsTeam',
    'amusementparks': 'AmusementPark',
    'aquariums': 'Aquarium',
    'archery': 'PlayAction',
    'badminton': 'PlayAction',
    'basketballcourts': 'SportsActivityLocation',
    'beaches': 'Beach',
    'bikerentals': 'Store',
    'boating': 'PlayAction',
    'bowling': 'BowlingAlley',
    'climbing': 'PlayAction',
    'discgolf': 'PlayAction',
    'diving': 'PlayAction',
    'freediving': 'PlayAction',
    'scuba': 'PlayAction',
    'fishing': 'PlayAction',
    'fitness': 'SportsActivityLocation',
    'barreclasses': 'ExerciseGym',
    'bootcamps': 'ExerciseGym',
    'boxing': 'SportsActivityLocation',
    'dancestudio': 'SportsActivityLocation',
    'gyms': 'ExerciseGym',
    'martialarts': 'SportsActivityLocation',
    'pilates': 'ExerciseGym',
    'swimminglessons': 'SportsActivityLocation',
    'taichi': 'SportsActivityLocation',
    'healthtrainers': 'ExerciseGym',
    'yoga': 'ExerciseGym',
    'gokarts': 'SportsActivityLocation',
    'golf': 'GolfCourse',
    'gun_ranges': 'PlayAction',
    'gymnastics': 'PlayAction',
    'hanggliding': 'PlayAction',
    'hiking': 'PlayAction',
    'horseracing': 'PlayAction',
    'horsebackriding': 'PlayAction',
    'hot_air_balloons': 'PlayAction',
    'kiteboarding': 'PlayAction',
    'lakes': 'LakeBodyOfWater',
    'lasertag': 'PlayAction',
    'leisure_centers': 'SportsActivityLocation',
    'mini_golf': 'SportsActivityLocation',
    'mountainbiking': 'PlayAction',
    'paddleboarding': 'PlayAction',
    'paintball': 'PlayAction',
    'parks': 'Park',
    'dog_parks': 'Park',
    'skate_parks': 'Park',
    'playgrounds': 'Playground',
    'rafting': 'PlayAction',
    'recreation': 'SportsActivityLocation',
    'rock_climbing': 'PlayAction',
    'skatingrinks': 'SportsActivityLocation',
    'skydiving': 'PlayAction',
    'football': 'PlayAction',
    'spinclasses': 'ExerciseAction',
    'sports_clubs': 'SportsClub',
    'squash': 'PlayAction',
    'summer_camps': 'PlayAction',
    'surfing': 'PlayAction',
    'swimmingpools': 'PublicSwimmingPool',
    'tennis': 'PlayAction',
    'trampoline': 'SportsActivityLocation',
    'tubing': 'PlayAction',
    'zoos': 'Zoo',
    'arts': 'LocalBusiness',
    'arcades': 'EntertainmentBusiness',
    'galleries': 'ArtGallery',
    'gardens': 'Park',
    'casinos': 'Casino',
    'movietheaters': 'MovieTheatre',
    'culturalcenter': 'CivicStructure',
    'festivals': 'Festival',
    'museums': 'Museum',
    'opera': 'PerformingArtsTheatre',
    'theater': 'PerformingArtsTheatre',
    'sportsteams': 'SportsTeam',
    'psychic_astrology': 'LocalBusiness',
    'racetracks': 'EventVenue',
    'social_clubs': 'EventVenue',
    'stadiumsarenas': 'StadiumOrArena',
    'ticketsales': 'Store',
    'auto': 'AutomotiveBusiness',
    'auto_detailing': 'AutoBodyShop',
    'autoglass': 'AutoBodyShop',
    'autoloanproviders': 'AutomotiveBusiness',
    'autopartssupplies': 'AutoPartsStore',
    'autorepair': 'AutoRepair',
    'boatdealers': 'AutoDealer',
    'bodyshops': 'AutoBodyShop',
    'car_dealers': 'AutoDealer',
    'stereo_installation': 'AutoRepair',
    'carwash': 'AutoWash',
    'servicestations': 'GasStation',
    'motorcycledealers': 'MotorcycleDealer',
    'motorcyclerepair': 'MotorcycleRepair',
    'oilchange': 'AutoRepair',
    'parking': 'LocalBusiness',
    'rv_dealers': 'AutoDealer',
    'smog_check_stations': 'AutomotiveBusiness',
    'tires': 'AutoRepair',
    'towing': 'AutomotiveBusiness',
    'truck_rental': 'AutoRental',
    'windshieldinstallrepair': 'AutoRepair',
    'beautysvc': 'HealthAndBeautyBusiness',
    'barbers': 'BeautySalon',
    'cosmetics': 'BeautySalon',
    'spas': 'DaySpa',
    'eyelashservice': 'BeautySalon',
    'hair_extensions': 'HairSalon',
    'hairremoval': 'HealthAndBeautyBusiness',
    'laser_hair_removal': 'HealthAndBeautyBusiness',
    'hair': 'HairSalon',
    'blowoutservices': 'HairSalon',
    'hairstylists': 'HairSalon',
    'menshair': 'HairSalon',
    'makeupartists': 'HealthAndBeautyBusiness',
    'massage': 'HealthAndBeautyBusiness',
    'medicalspa': 'HealthAndBeautyBusiness',
    'othersalons': 'NailSalon',
    'permanentmakeup': 'HealthAndBeautyBusiness',
    'piercing': 'HealthAndBeautyBusiness',
    'rolfing': 'HealthAndBeautyBusiness',
    'skincare': 'HealthAndBeautyBusiness',
    'tanning': 'HealthAndBeautyBusiness',
    'spraytanning': 'HealthAndBeautyBusiness',
    'tanningbeds': 'HealthAndBeautyBusiness',
    'tattoo': 'TattooParlor',
    'education': 'EducationalOrganization',
    'adultedu': 'EducationalOrganization',
    'collegecounseling': 'CollegeOrUniversity',
    'collegeuniv': 'CollegeOrUniversity',
    'educationservices': 'EducationalOrganization',
    'elementaryschools': 'ElementarySchool',
    'highschools': 'HighSchool',
    'preschools': 'Preschool',
    'privatetutors': 'EducationalOrganization',
    'religiousschools': 'School',
    'specialed': 'School',
    'specialtyschools': 'School',
    'artschools': 'School',
    'cprclasses': 'EducationalOrganization',
    'cookingschools': 'School',
    'cosmetology_schools': 'School',
    'dance_schools': 'School',
    'driving_schools': 'School',
    'firstaidclasses': 'EducationalOrganization',
    'flightinstruction': 'EducationalOrganization',
    'language_schools': 'School',
    'massage_schools': 'School',
    'vocation': 'School',
    'testprep': 'EducationalOrganization',
    'tutoring': 'EducationalOrganization',
    'eventservices': 'Event',
    'bartenders': 'SocialEvent',
    'boatcharters': 'SocialEvent',
    'stationery': 'BusinessEvent',
    'catering': 'FoodEvent',
    'clowns': 'ChildrensEvent',
    'djs': 'SocialEvent',
    'magicians': 'SocialEvent',
    'musicians': 'SocialEvent',
    'officiants': 'Event',
    'eventplanning': 'Event',
    'partybusrentals': 'SocialEvent',
    'partyequipmentrentals': 'Event',
    'partysupplies': 'Event',
    'personalchefs': 'FoodEvent',
    'photographers': 'Event',
    'eventphotography': 'Event',
    'sessionphotography': 'Event',
    'venues': 'Event',
    'videographers': 'Event',
    'wedding_planning': 'SocialEvent',
    'financialservices': 'FinancialService',
    'banks': 'BankOrCreditUnion',
    'paydayloans': 'FinancialService',
    'financialadvising': 'FinancialService',
    'insurance': 'InsuranceAgency',
    'investing': 'BankOrCreditUnion',
    'taxservices': 'AccountingService',
    'food': 'FoodEstablishment',
    'bagels': 'CafeOrCoffeeShop',
    'bakeries': 'Bakery',
    'beer_and_wine': 'Winery',
    'breweries': 'Brewery',
    'bubbletea': 'Restaurant',
    'butcher': 'FoodEstablishment',
    'csa': 'LocalBusiness',
    'coffee': 'CafeOrCoffeeShop',
    'convenience': 'ConvenienceStore',
    'desserts': 'CafeOrCoffeeShop',
    'diyfood': 'Restaurant',
    'donuts': 'CafeOrCoffeeShop',
    'farmersmarket': 'FoodEstablishment',
    'fooddeliveryservices': 'FoodEstablishment',
    'foodtrucks': 'FastFoodRestaurant',
    'gelato': 'IceCreamShop',
    'grocery': 'FoodEstablishment',
    'icecream': 'IceCreamShop',
    'internetcafe': 'CafeOrCoffeeShop',
    'juicebars': 'FastFoodRestaurant',
    'pretzels': 'FastFoodRestaurant',
    'shavedice': 'FoodEstablishment',
    'gourmet': 'FoodEstablishment',
    'candy': 'FoodEstablishment',
    'cheese': 'FoodEstablishment',
    'chocolate': 'FoodEstablishment',
    'ethnicmarkets': 'FoodEstablishment',
    'markets': 'FoodEstablishment',
    'healthmarkets': 'FoodEstablishment',
    'herbsandspices': 'FoodEstablishment',
    'meats': 'FoodEstablishment',
    'seafoodmarkets': 'FoodEstablishment',
    'streetvendors': 'FoodEstablishment',
    'tea': 'CafeOrCoffeeShop',
    'wineries': 'Winery',
    'health': 'MedicalOrganization',
    'acupuncture': 'MedicalOrganization',
    'cannabis_clinics': 'MedicalOrganization',
    'chiropractors': 'MedicalOrganization',
    'c_and_mh': 'MedicalOrganization',
    'dentists': 'Dentist',
    'cosmeticdentists': 'Dentist',
    'endodontists': 'Dentist',
    'generaldentistry': 'Dentist',
    'oralsurgeons': 'Dentist',
    'orthodontists': 'Dentist',
    'pediatric_dentists': 'Dentist',
    'periodontists': 'Dentist',
    'diagnosticservices': 'DiagnosticLab',
    'diagnosticimaging': 'DiagnosticLab',
    'laboratorytesting': 'DiagnosticLab',
    'physicians': 'Physician',
    'allergist': 'Physician',
    'anesthesiologists': 'Physician',
    'audiologist': 'Physician',
    'cardiology': 'Physician',
    'cosmeticsurgeons': 'Physician',
    'dermatology': 'Physician',
    'earnosethroat': 'Physician',
    'familydr': 'Physician',
    'fertility': 'Physician',
    'gastroenterologist': 'Physician',
    'gerontologist': 'Physician',
    'internalmed': 'Physician',
    'naturopathic': 'Physician',
    'neurologist': 'Physician',
    'obgyn': 'Physician',
    'oncologist': 'Physician',
    'opthamalogists': 'Physician',
    'orthopedists': 'Physician',
    'osteopathicphysicians': 'Physician',
    'pediatricians': 'Physician',
    'podiatrists': 'Physician',
    'proctologist': 'Physician',
    'psychiatrists': 'Physician',
    'pulmonologist': 'Physician',
    'sportsmed': 'Physician',
    'tattooremoval': 'Physician',
    'urologists': 'Physician',
    'hearingaidproviders': 'MedicalClinic',
    'homehealthcare': 'MedicalOrganization',
    'hospice': 'MedicalOrganization',
    'hospitals': 'Hospital',
    'lactationservices': 'MedicalClinic',
    'laserlasikeyes': 'MedicalClinic',
    'massage_therapy': 'MedicalClinic',
    'medcenters': 'MedicalClinic',
    'medicaltransportation': 'MedicalClinic',
    'midwives': 'MedicalClinic',
    'nutritionists': 'MedicalClinic',
    'occupationaltherapy': 'MedicalClinic',
    'optometrists': 'Optician',
    'physicaltherapy': 'MedicalClinic',
    'reflexology': 'MedicalClinic',
    'rehabilitation_center': 'MedicalClinic',
    'retirement_homes': 'MedicalOrganization',
    'speech_therapists': 'MedicalClinic',
    'tcm': 'MedicalOrganization',
    'urgent_care': 'MedicalClinic',
    'weightlosscenters': 'MedicalClinic',
    'homeservices': 'HomeAndConstructionBusiness',
    'buildingsupplies': 'GeneralContractor',
    'carpetinstallation': 'GeneralContractor',
    'carpeting': 'GeneralContractor',
    'contractors': 'GeneralContractor',
    'damagerestoration': 'GeneralContractor',
    'electricians': 'Electrician',
    'flooring': 'GeneralContractor',
    'garage_door_services': 'GeneralContractor',
    'gardeners': 'HomeAndConstructionBusiness',
    'handyman': 'GeneralContractor',
    'hvac': 'HVACBusiness',
    'homecleaning': 'HomeAndConstructionBusiness',
    'home_inspectors': 'HomeAndConstructionBusiness',
    'home_organization': 'HomeAndConstructionBusiness',
    'hometheatreinstallation': 'HomeAndConstructionBusiness',
    'homewindowtinting': 'HomeAndConstructionBusiness',
    'interiordesign': 'HomeAndConstructionBusiness',
    'irrigation': 'HomeAndConstructionBusiness',
    'locksmiths': 'Locksmith',
    'landscapearchitects': 'GeneralContractor',
    'landscaping': 'GeneralContractor',
    'lighting': 'HomeAndConstructionBusiness',
    'masonry_concrete': 'GeneralContractor',
    'movers': 'MovingCompany',
    'painters': 'HousePainter',
    'plumbing': 'Plumber',
    'poolcleaners': 'HomeAndConstructionBusiness',
    'prefabricated': 'RealEstateAgent',
    'roofing': 'RoofingContractor',
    'securitysystems': 'HomeAndConstructionBusiness',
    'blinds': 'HomeAndConstructionBusiness',
    'solarinstallation': 'HomeAndConstructionBusiness',
    'televisionserviceproviders': 'CableOrSatelliteService',
    'treeservices': 'GeneralContractor',
    'utilities': 'GovernmentService',
    'windowwashing': 'HomeAndConstructionBusiness',
    'windowsinstallation': 'HomeAndConstructionBusiness',
    'hotelstravel': 'Hotel',
    'airports': 'Airport',
    'bedbreakfast': 'BedAndBreakfast',
    'campgrounds': 'Campground',
    'carrental': 'AutoRental',
    'guesthouses': 'LodgingBusiness',
    'hostels': 'LodgingBusiness',
    'hotels': 'Hotel',
    'motorcycle_rental': 'AutoRental',
    'rvparks': 'RVPark',
    'rvrental': 'AutoRental',
    'resorts': 'LodgingBusiness',
    'skiresorts': 'SkiResort',
    'tours': 'TouristInformationCenter',
    'trainstations': 'TrainStation',
    'transport': 'Service',
    'airlines': 'Airline',
    'airport_shuttles': 'TaxiService',
    'limos': 'TaxiService',
    'publictransport': 'BusTrip',
    'taxis': 'TaxiService',
    'travelservices': 'TravelAgency',
    'vacationrentalagents': 'TravelAgency',
    'vacation_rentals': 'LodgingBusiness',
    'localservices': 'ProfessionalService',
    'homeappliancerepair': 'HomeAndConstructionBusiness',
    'bailbondsmen': 'FinancialService',
    'bike_repair_maintenance': 'BikeStore',
    'carpet_cleaning': 'ProfessionalService',
    'childcare': 'ChildCare',
    'nonprofit': 'Organization',
    'couriers': 'ParcelDelivery',
    'drycleaninglaundry': 'DryCleaningOrLaundry',
    'electronicsrepair': 'ElectronicsStore',
    'funeralservices': 'ProfessionalService',
    'reupholstery': 'FurnitureStore',
    'itservices': 'ComputerStore',
    'datarecovery': 'ComputerStore',
    'mobilephonerepair': 'MobilePhoneStore',
    'jewelryrepair': 'JewelryStore',
    'junkremovalandhauling': 'ProfessionalService',
    'nannys': 'ChildCare',
    'notaries': 'Notary',
    'pest_control': 'ProfessionalService',
    'copyshops': 'ProfessionalService',
    'recording_studios': 'MusicRecording',
    'recyclingcenter': 'RecyclingCenter',
    'screenprinting': 'ProfessionalService',
    'screen_printing_tshirt_printing': 'ProfessionalService',
    'selfstorage': 'SelfStorage',
    'sewingalterations': 'ProfessionalService',
    'shipping_centers': 'ParcelDelivery',
    'shoerepair': 'LocalBusiness',
    'snowremoval': 'ProfessionalService',
    'watch_repair': 'ProfessionalService',
    'massmedia': 'BroadcastService',
    'printmedia': 'LocalBusiness',
    'radiostations': 'RadioStation',
    'televisionstations': 'TelevisionStation',
    'nightlife': 'EntertainmentBusiness',
    'adultentertainment': 'AdultEntertainment',
    'bars': 'BarOrPub',
    'champagne_bars': 'BarOrPub',
    'cocktailbars': 'BarOrPub',
    'divebars': 'BarOrPub',
    'gaybars': 'BarOrPub',
    'hookah_bars': 'BarOrPub',
    'lounges': 'BarOrPub',
    'pubs': 'BarOrPub',
    'sportsbars': 'BarOrPub',
    'wine_bars': 'BarOrPub',
    'comedyclubs': 'ComedyClub',
    'countrydancehalls': 'BarOrPub',
    'danceclubs': 'NightClub',
    'jazzandblues': 'MusicEvent',
    'karaoke': 'MusicVenue',
    'musicvenues': 'MusicVenue',
    'pianobars': 'MusicVenue',
    'poolhalls': 'EntertainmentBusiness',
    'pets': 'LocalBusiness',
    'animalshelters': 'AnimalShelter',
    'horse_boarding': 'AnimalShelter',
    'petservices': 'LocalBusiness',
    'dogwalkers': 'LocalBusiness',
    'pet_sitting': 'LocalBusiness',
    'groomer': 'LocalBusiness',
    'pet_training': 'LocalBusiness',
    'petstore': 'PetStore',
    'vet': 'VeterinaryCare',
    'professional': 'ProfessionalService',
    'accountants': 'AccountingService',
    'advertising': 'ProfessionalService',
    'architects': 'ProfessionalService',
    'boatrepair': 'AutoRepair',
    'careercounseling': 'ProfessionalService',
    'editorialservices': 'ProfessionalService',
    'employmentagencies': 'EmploymentAgency',
    'graphicdesign': 'ProfessionalService',
    'isps': 'CableOrSatelliteService',
    'lawyers': 'Attorney',
    'bankruptcy': 'Attorney',
    'businesslawyers': 'Attorney',
    'criminaldefense': 'Attorney',
    'duilawyers': 'Attorney',
    'divorce': 'Attorney',
    'employmentlawyers': 'Attorney',
    'estateplanning': 'Attorney',
    'general_litigation': 'Attorney',
    'immigrationlawyers': 'Attorney',
    'personal_injury': 'Attorney',
    'realestatelawyers': 'Attorney',
    'legalservices': 'ProfessionalService',
    'lifecoach': 'ProfessionalService',
    'marketing': 'ProfessionalService',
    'matchmakers': 'ProfessionalService',
    'officecleaning': 'ProfessionalService',
    'payroll': 'ProfessionalService',
    'personalassistants': 'ProfessionalService',
    'privateinvestigation': 'ProfessionalService',
    'publicrelations': 'ProfessionalService',
    'talentagencies': 'ProfessionalService',
    'taxidermy': 'ProfessionalService',
    'translationservices': 'ProfessionalService',
    'videofilmproductions': 'ProfessionalService',
    'web_design': 'ProfessionalService',
    'publicservicesgovt': 'GovernmentService',
    'courthouses': 'Courthouse',
    'departmentsofmotorvehicles': 'GovernmentService',
    'embassy': 'Embassy',
    'firedepartments': 'FireStation',
    'landmarks': 'LandmarksOrHistoricalBuildings',
    'libraries': 'Library',
    'policedepartments': 'PoliceStation',
    'postoffices': 'PostOffice',
    'realestate': 'RealEstateAgent',
    'apartments': 'ApartmentComplex',
    'commercialrealestate': 'LocalBusiness',
    'homestaging': 'LocalBusiness',
    'mortgagebrokers': 'LocalBusiness',
    'propertymgmt': 'LocalBusiness',
    'realestateagents': 'RealEstateAgent',
    'realestatesvcs': 'LocalBusiness',
    'sharedofficespaces': 'LocalBusiness',
    'university_housing': 'Residence',
    'religiousorgs': 'PlaceOfWorship',
    'buddhist_temples': 'BuddhistTemple',
    'churches': 'Church',
    'hindu_temples': 'HinduTemple',
    'mosques': 'Mosque',
    'synagogues': 'Synagogue',
    'restaurants': 'FoodEstablishment',
    'afghani': 'FoodEstablishment',
    'african': 'FoodEstablishment',
    'senegalese': 'FoodEstablishment',
    'southafrican': 'FoodEstablishment',
    'newamerican': 'FoodEstablishment',
    'tradamerican': 'FoodEstablishment',
    'arabian': 'FoodEstablishment',
    'argentine': 'FoodEstablishment',
    'armenian': 'FoodEstablishment',
    'asianfusion': 'FoodEstablishment',
    'australian': 'FoodEstablishment',
    'austrian': 'FoodEstablishment',
    'bangladeshi': 'FoodEstablishment',
    'bbq': 'FoodEstablishment',
    'basque': 'FoodEstablishment',
    'belgian': 'FoodEstablishment',
    'brasseries': 'FoodEstablishment',
    'brazilian': 'FoodEstablishment',
    'breakfast_brunch': 'FoodEstablishment',
    'british': 'FoodEstablishment',
    'buffets': 'FoodEstablishment',
    'burgers': 'FoodEstablishment',
    'burmese': 'FoodEstablishment',
    'cafes': 'CafeOrCoffeeShop',
    'cafeteria': 'FoodEstablishment',
    'cajun': 'FoodEstablishment',
    'cambodian': 'FoodEstablishment',
    'caribbean': 'FoodEstablishment',
    'dominican': 'FoodEstablishment',
    'haitian': 'FoodEstablishment',
    'puertorican': 'FoodEstablishment',
    'trinidadian': 'FoodEstablishment',
    'catalan': 'FoodEstablishment',
    'cheesesteaks': 'FoodEstablishment',
    'chicken_wings': 'FoodEstablishment',
    'chinese': 'FoodEstablishment',
    'cantonese': 'FoodEstablishment',
    'dimsum': 'FoodEstablishment',
    'shanghainese': 'FoodEstablishment',
    'szechuan': 'FoodEstablishment',
    'comfortfood': 'FoodEstablishment',
    'creperies': 'FoodEstablishment',
    'cuban': 'FoodEstablishment',
    'czech': 'FoodEstablishment',
    'delis': 'FoodEstablishment',
    'diners': 'FoodEstablishment',
    'ethiopian': 'FoodEstablishment',
    'hotdogs': 'FastFoodRestaurant',
    'filipino': 'FoodEstablishment',
    'fishnchips': 'FoodEstablishment',
    'fondue': 'FoodEstablishment',
    'food_court': 'FoodEstablishment',
    'foodstands': 'FoodEstablishment',
    'french': 'FoodEstablishment',
    'gastropubs': 'FoodEstablishment',
    'german': 'FoodEstablishment',
    'gluten_free': 'FoodEstablishment',
    'greek': 'FoodEstablishment',
    'halal': 'FoodEstablishment',
    'hawaiian': 'FoodEstablishment',
    'himalayan': 'FoodEstablishment',
    'hotdog': 'FoodEstablishment',
    'hotpot': 'FoodEstablishment',
    'hungarian': 'FoodEstablishment',
    'iberian': 'FoodEstablishment',
    'indpak': 'FoodEstablishment',
    'indonesian': 'FoodEstablishment',
    'irish': 'FoodEstablishment',
    'italian': 'FoodEstablishment',
    'japanese': 'FoodEstablishment',
    'korean': 'FoodEstablishment',
    'kosher': 'FoodEstablishment',
    'laotian': 'FoodEstablishment',
    'latin': 'FoodEstablishment',
    'colombian': 'FoodEstablishment',
    'salvadoran': 'FoodEstablishment',
    'venezuelan': 'FoodEstablishment',
    'raw_food': 'FoodEstablishment',
    'malaysian': 'FoodEstablishment',
    'mediterranean': 'FoodEstablishment',
    'mexican': 'FoodEstablishment',
    'mideastern': 'FoodEstablishment',
    'egyptian': 'FoodEstablishment',
    'lebanese': 'FoodEstablishment',
    'modern_european': 'FoodEstablishment',
    'mongolian': 'FoodEstablishment',
    'moroccan': 'FoodEstablishment',
    'pakistani': 'FoodEstablishment',
    'persian': 'FoodEstablishment',
    'peruvian': 'FoodEstablishment',
    'pizza': 'FoodEstablishment',
    'polish': 'FoodEstablishment',
    'portuguese': 'FoodEstablishment',
    'russian': 'FoodEstablishment',
    'salad': 'FoodEstablishment',
    'sandwiches': 'FoodEstablishment',
    'scandinavian': 'FoodEstablishment',
    'scottish': 'FoodEstablishment',
    'seafood': 'FoodEstablishment',
    'singaporean': 'FoodEstablishment',
    'slovakian': 'FoodEstablishment',
    'soulfood': 'FoodEstablishment',
    'soup': 'FoodEstablishment',
    'southern': 'FoodEstablishment',
    'spanish': 'FoodEstablishment',
    'steak': 'FoodEstablishment',
    'sushi': 'FoodEstablishment',
    'taiwanese': 'FoodEstablishment',
    'tapas': 'FoodEstablishment',
    'tapasmallplates': 'FoodEstablishment',
    'tex-mex': 'FoodEstablishment',
    'thai': 'FoodEstablishment',
    'turkish': 'FoodEstablishment',
    'ukrainian': 'FoodEstablishment',
    'vegan': 'FoodEstablishment',
    'vegetarian': 'FoodEstablishment',
    'vietnamese': 'FoodEstablishment',
    'shopping': 'Store',
    'adult': 'Store',
    'antiques': 'Store',
    'artsandcrafts': 'Store',
    'artsupplies': 'Store',
    'costumes': 'Store',
    'fabricstores': 'Store',
    'framing': 'Store',
    'auctionhouses': 'Store',
    'baby_gear': 'Store',
    'bespoke': 'ClothingStore',
    'media': 'Store',
    'bookstores': 'BookStore',
    'comicbooks': 'Store',
    'musicvideo': 'MusicStore',
    'mags': 'Store',
    'videoandgames': 'MovieRentalStore',
    'vinyl_records': 'MusicStore',
    'bridal': 'Store',
    'computers': 'ComputerStore',
    'deptstores': 'DepartmentStore',
    'discountstore': 'Store',
    'drugstores': 'Store',
    'electronics': 'ElectronicsStore',
    'opticians': 'Optician',
    'fashion': 'Store',
    'accessories': 'Store',
    'childcloth': 'ClothingStore',
    'formalwear': 'ClothingStore',
    'hats': 'ClothingStore',
    'leather': 'Store',
    'lingerie': 'ClothingStore',
    'maternity': 'ClothingStore',
    'menscloth': 'ClothingStore',
    'plus_size_fashion': 'ClothingStore',
    'shoes': 'ShoeStore',
    'sportswear': 'Store',
    'surfshop': 'SportingGoodsStore',
    'swimwear': 'ClothingStore',
    'vintage': 'Store',
    'womenscloth': 'ClothingStore',
    'fireworks': 'Store',
    'fleamarkets': 'Store',
    'flowers': 'Store',
    'florists': 'Florist',
    'giftshops': 'Store',
    'golfshops': 'SportingGoodsStore',
    'guns_and_ammo': 'Store',
    'hobbyshops': 'HobbyShop',
    'homeandgarden': 'Store',
    'appliances': 'Store',
    'furniture': 'FurnitureStore',
    'hardware': 'HardwareStore',
    'homedecor': 'HomeGoodsStore',
    'hottubandpool': 'Store',
    'kitchenandbath': 'HomeGoodsStore',
    'mattresses': 'FurnitureStore',
    'gardening': 'GardenStore',
    'jewelry': 'JewelryStore',
    'knittingsupplies': 'Store',
    'luggage': 'Store',
    'medicalsupplies': 'Store',
    'mobilephones': 'MobilePhoneStore',
    'motorcyclinggear': 'Store',
    'musicalinstrumentsandteachers': 'MusicStore',
    'officeequipment': 'OfficeEquipmentStore',
    'outlet_stores': 'OutletStore',
    'pawn': 'PawnShop',
    'personal_shopping': 'Service',
    'photographystores': 'Store',
    'poolbilliards': 'EntertainmentBusiness',
    'popupshops': 'LocalBusiness',
    'shoppingcenters': 'ShoppingCenter',
    'sportgoods': 'SportingGoodsStore',
    'bikes': 'BikeStore',
    'golfequipment': 'Store',
    'outdoorgear': 'Store',
    'thrift_stores': 'Store',
    'tobaccoshops': 'Store',
    'toys': 'ToyStore',
    'trophyshops': 'Store',
    'uniforms': 'Store',
    'watches': 'Store',
    'wholesale_stores': 'WholesaleStore',
    'wigs': 'Store',
    'industgoodsmanu': 'Thing',
    'other': 'Thing',
    'transportation': 'Thing',
    'air': 'Thing',
    'railway': 'Thing',
    'road': 'Thing',
    'water': 'Thing',
    'mineag': 'Thing',
    'mining': 'Thing',
    'ag': 'Thing',
    'horticulture': 'Thing',
    'livestock': 'Thing',
}

"""
A list containing schema.org types that have been tested on 
https://search.google.com/test/rich-results?code=2&user_agent=2
There may be more types that are still valid I have not tested the whole list extensively
"""
VALID_GOOGLE_REVIEW_SOURCES = ['FoodEstablishment', 'MedicalClinic', 'RealEstateAgent', 'CafeOrCoffeeShop',
                               'MedicalOrganization', 'Physician', 'LocalBusiness', 'SportsTeam', 'AmusementPark',
                               'SportsActivityLocation', 'Store', 'BowlingAlley', 'ExerciseGym', 'GolfCourse',
                               'SportsClub', 'PublicSwimmingPool', 'EntertainmentBusiness', 'ArtGallery', 'Casino']


def get_schema_types_by_taxonomy_ids(taxonomy_ids):
    """
    Get schema.org type from an existing taxonomy id.
    'Thing' is the least specific schema.org type, and is a parent to all other types.
    taxonomy_ids can be a string or list of strings, and either the leaf or full taxonomy string
    :param taxonomy_ids: List, Contains taxonomy id strings.
    :return: Set, Contains unique schema types for the input list or empty set if None input
    """
    if not taxonomy_ids:
        return {}
    taxonomy_ids = taxonomy_ids if isinstance(taxonomy_ids, list) else [taxonomy_ids]
    taxonomy_ids = [taxonomy_id.split(':')[-1] for taxonomy_id in taxonomy_ids]

    return {VTAX_TO_SCHEMA_MAPPING.get(taxonomy_id, 'Thing') for taxonomy_id in taxonomy_ids}


def get_valid_google_review_schema(taxonomy_ids):
    """
    This will get a schema.org type that is eligible for Google reviews, or return the default of LocalBusiness
    :param taxonomy_ids:
    :return: A string value that represents a valid schema.org type that is eligible for being reviewed on Google
    """

    types = get_schema_types_by_taxonomy_ids(taxonomy_ids)
    eligible_type = None
    for t in types:
        if t in VALID_GOOGLE_REVIEW_SOURCES:
            eligible_type = t

    return eligible_type if eligible_type else 'LocalBusiness'
