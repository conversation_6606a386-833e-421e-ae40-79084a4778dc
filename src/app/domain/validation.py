"""
Functions and decorators to help with validation.
"""
from functools import wraps
from inspect import getcallargs, getargspec

class RequireArgs:
    """
    Decorator which ensures all *args have a value.
    """

    def __init__(self, f, arg_list=None):
        """
        Initialize.
        """
        self.f = f
        self.__name__ = self.f.__name__
        self.arg_list = arg_list or []

    def get_required_args(self):
        """
        Returns a list of required args for the decorated method.
        """
        if self.arg_list:
            return self.arg_list

        argspec = getargspec(self.f)
        args = argspec.args
        if argspec.defaults:
            args = args[:-len(argspec.defaults)]
        return args

    def ensure_required_args_provided(self, required_args, *args, **kwargs):
        """
        Ensures a value is provided for each required argument otherwise a ValueError is raised.
        """
        callargs = getcallargs(self.f, *args, **kwargs)
        for arg_name in required_args:
            value = callargs.get(arg_name)

            # value must exist but can be 0 or False for int/float/bool types
            if (isinstance(value, (int, float, bool)) and value is None) or\
               (not isinstance(value, (int, float, bool)) and not value):
                raise ValueError('%s is required.' % arg_name)

    def __get__(self, obj, obj_type=None):
        """
        Ensure required args exist for a class instance method.
        """
        if obj is None:
            return self.f

        @wraps(self.f)
        def decorator(*args, **kwargs):
            """
            Decorator for class instance methods to ensure all *args have a value provided.
            """
            required_args = self.get_required_args()
            self.ensure_required_args_provided(required_args, obj, *args, **kwargs)
            return self.f(obj, *args, **kwargs)

        return decorator

    def __call__(self, *args, **kwargs):
        """
        Requires all *args have values then calls the function.
        """
        required_args = self.get_required_args()
        self.ensure_required_args_provided(required_args, *args, **kwargs)
        return self.f(*args, **kwargs)

def require_arg_list(arg_list=None):
    """ Require a specific list of args instead of all of them. """
    def _wrapper(f):
        """ Wraps the decorator. """
        return RequireArgs(f, arg_list=arg_list)
    return _wrapper

# Invalid name "require_args"
# Decorators begin with lowercased letter.
require_args = RequireArgs # pylint: disable=C0103
