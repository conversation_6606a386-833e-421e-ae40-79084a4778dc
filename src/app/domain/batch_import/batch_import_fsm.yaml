
state_machines:
  
  - name: BatchImport

    namespace: app.domain.batch_import.fsm
    queue: batch-import
    task_retry_limit: 20
    
    context_types:
      linenum: int
      page_index: int
      process_image_counter: int
      mapping_key: google.appengine.api.datastore_types.Key

    states:
      - name: Initialize
        action: Initialize
        initial: True
        transitions:
        - event: ok
          to: CreateBatchImportMetaEntity
        - event: bad-input
          to: BadInput
          
      - name: CreateBatchImportMetaEntity
        action: CreateBatchImportMetaEntity
        transitions:
        - event: ok
          to: IterateLines

      - name: IterateLines
        action: IterateLines
        continuation: True
        final: True
        transitions:
        - event: ok
          to: ValidateLine
          
      - name: ValidateLine
        entry: GetMicrositeBatch
        action: ValidateLine
        transitions:
        - event: ok
          to: ProcessMicrosite
        - event: row-skipped
          to: RowSkipped
          
      - name: ProcessMicrosite
        entry: GetMicrositeBatch
        action: ProcessMicrosite
        transitions:
        - event: ok
          to: ProcessPage
        - event: row-skipped
          to: RowSkipped

      - name: ProcessPage
        entry: GetMicrositeBatch
        action: ProcessPage
        transitions:
        - event: ok
          to: ReorderAndDeletePages
        - event: next-page
          to: ProcessPage
        - event: process-images-page
          to: ProcessImagesPage

      - name: ProcessImagesPage
        entry: GetMicrositeBatch
        action: ProcessImagesPage
        transitions:
        - event: ok
          to: ProcessPage
        - event: invalid-page-index
          to: ProcessPage
        - event: invalid-page-template
          to: ProcessPage
          
      - name: ReorderAndDeletePages
        entry: GetMicrositeBatch
        action: ReorderAndDeletePages
        transitions:
        - event: ok
          to: ProcessLogo

      - name: ProcessLogo
        entry: GetMicrositeBatch
        action: ProcessLogo
        transitions:
        - event: ok
          to: ProcessDesktopBackground
        - event: retry
          to: ProcessLogo
          countdown: 120

      - name: ProcessDesktopBackground
        entry: GetMicrositeBatch
        action: ProcessDesktopBackground
        transitions:
        - event: ok
          to: ProcessMobileBackground
        - event: retry
          to: ProcessDesktopBackground
          countdown: 120

      - name: ProcessMobileBackground
        entry: GetMicrositeBatch
        action: ProcessMobileBackground
        transitions:
        - event: ok
          to: RemapSlug
        - event: retry
          to: ProcessMobileBackground
          countdown: 120

      - name: RemapSlug
        entry: GetMicrositeBatch
        action: RemapSlug
        transitions:
        - event: ok
          to: CreateBatchImportEntity

      - name: CreateBatchImportEntity
        entry: GetMicrositeBatch
        action: CreateBatchImportEntity
        final: True
        
      - name: BadInput
        action: BadInput
        final: True

      - name: RowSkipped
        action: RowSkipped
        final: True

  - name: ProcessBatchImportPageImage
    namespace: app.domain.batch_import.fsm
    queue: batch-import
    context_types:
      mapping_key: google.appengine.ext.ndb.Key
      process_image_counter: int
      linenum: int
    states:
      - name: ProcessBatchImportPageImage
        action: ProcessBatchImportPageImage
        initial: True
        final: True
        transitions:
        - event: retry
          to: ProcessBatchImportPageImage
          countdown: 120
        
  - name: SetBatchImportMetaNumberToProcess
    namespace: app.domain.batch_import.util
    queue: batch-import
    context_types:
      number_to_process: int
    states:
      - name: SetBatchImportMetaNumberToProcess
        action: SetBatchImportMetaNumberToProcess
        initial: True
        final: True
        
  - name: IncrementBatchImportMetaNumberComplete
    namespace: app.domain.batch_import.util
    queue: batch-import
    context_types:
      increment_by: int
    states:
      - name: IncrementBatchImportMetaNumberComplete
        action: IncrementBatchImportMetaNumberComplete
        initial: True
        final: True

  - name: ProcessBatchImportCounterQueue
    namespace: app.domain.batch_import.util
    queue: batch-import
    context_types:
      count_until: datetime
    states:
      - name: ProcessBatchImportCounterQueue
        action: ProcessBatchImportCounterQueue
        initial: True
        final: True
        transitions:
        - event: continue
          to: ProcessBatchImportCounterQueue
          countdown: 5

  - name: BatchImportLogger
    namespace: app.domain.batch_import.logger
    queue: default
    context_types:
      linenum: int
      level: int
    states:
      - name: LogMessage
        action: LogMessage
        initial: True
        final: True

  - name: RetrieveYouTubeMetaInfo
    namespace: app.domain.batch_import.util
    queue: batch-import
    states:
      - name: RetrieveYouTube
        action: RetrieveYouTube
        initial: True
        final: True

  - name: ProcessPageImage
    namespace: app.domain.batch_import.util
    queue: batch-import
    context_types:
      mapping_key: google.appengine.ext.ndb.Key
    states:
      - name: ProcessPageImage
        action: ProcessPageImage
        initial: True
        final: True
        transitions:
        - event: retry
          to: ProcessPageImage
          countdown: 120
