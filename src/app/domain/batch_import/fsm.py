""" The code behind the 'batch-import' fantasm state machine """

import datetime
import json
import logging
from google.appengine.api.taskqueue.taskqueue import Task, Queue, TaskAlreadyExistsError, TombstonedTaskError
from google.appengine.ext import ndb, deferred

from dscache import dscache
from fantasm.fsm import startStateMachine
from werkzeug.utils import import_string
from werkzeug.datastructures import MultiDict

from app.constants import VIDEOS, IMAGES, DEFERRED_ROOT, API_JOB_ID, API_NOTIFICATION_URL, LAYOUT_1_API_KEY
from app.domain.batch_import import create_batch_import_meta_entity, create_batch_import_entity, get_msid_for_pmsid
from app.domain.batch_import_helpers.data_structures import MicrositeBatch, InvalidBatchSpecificationException
from app.domain.batch_import.logger import info as bi_info, warn as bi_warn, error as bi_error
from app.domain.batch_import.util import ImageProcessor, ProcessPageImage, UnifiedProvisionNotifier
from app.domain.blob_mappings import add_msid_blob_mapping, get_msid_blob_mapping, \
    delete_blob_mapping_by_key, get_image
from app.domain.exceptions import HostSlugMsidMappingExistsException, BatchImportExistsException, \
    AccountGroupInvalidException
from app.domain.microsite import get_microsite
from app.domain.pageid_blob_mapping import reorder_pageid_blob_mappings, process_mappings
from app.domain.url_mappings import lookup_hostnames_for_pid, remove_msid_for_hostname_slug, \
    lookup_hostslug_msid_mapping_for_msid, add_hostslug_msid_mapping, get_hostslug_msid_mapping
from app.domain.workflow import exceptions
from app.domain.workflow.page import create_page, update_page, ImagesPageForm
from app.domain.workflow.microsite import CreateMicrositeForm, update_microsite, UpdateMicrositeForm, \
    create_blank_microsite
from app.models import blob_mappings
from app.models.batch_import import BatchImportMeta as BatchImportMetaModel
from app.views.api.v2.keys import convert_keys_to_snake_case

# W0613:Unused argument 'obj'
# Fantasm expects a particular interface
# pylint: disable=W0613
from settings import USER_BATCH, USER_API, USER_CONFIG_KEY_API_USER, get_account_origin, ORIGIN_UNKNOWN


class Initialize:
    """ Initialize the state machine """

    def execute(self, context, obj):
        """ Check that the required variables are provided. """
        job_id = context.get(API_JOB_ID, None)
        if job_id:
            logging.info('jobId: %s', job_id)

        bad_input = False
        if not context.get('gcs_file_name'):
            logging.error('gcs_file_name is required.')
            bad_input = True

        if not context.get('pid'):
            logging.error('pid is required.')
            bad_input = True

        if not context.get('filename'):
            logging.error('filename is required.')
            bad_input = True

        if not context.get('host') and not bad_input:
            hosts = lookup_hostnames_for_pid(context['pid'])
            if not len(hosts) > 0:
                logging.error('No hosts found for pid "%s".', context['pid'])
                bad_input = True
            else:
                context['host'] = hosts[0]

        if bad_input:
            return 'bad-input'

        logging.info('Using host "%s" for import for pid "%s".', context['host'], context['pid'])

        return 'ok'


class CreateBatchImportMetaEntity:
    """ Creates a BatchImportMeta entity for this run. """

    def execute(self, context, obj):
        """ Create a BatchImportMeta entity. """
        pid = context['pid']
        filename = context['filename']
        meta_key = context.get('meta_key')
        job_id = context.get(API_JOB_ID, None)
        if job_id:
            logging.info('jobId: %s', job_id)

        notification_url = context.get(API_NOTIFICATION_URL, None)
        importid = create_batch_import_meta_entity(pid, filename=filename, key=meta_key)

        # start the machine to count up the completed records
        count_until = datetime.datetime.utcnow() + datetime.timedelta(days=2)
        counter_context = {
            'count_until': count_until,
            'importid': importid,
            API_JOB_ID: job_id,
            API_NOTIFICATION_URL: notification_url,
            'msid': context.get('msid', None),
        }
        logging.info('Sites batch import meta entity created')
        startStateMachine('ProcessBatchImportCounterQueue', counter_context, countdown=5)

        context['importid'] = importid
        return 'ok'


class IterateLines:
    """ Iterate through each line of the batch import file and process the data. """

    def continuation(self, context, obj, token=0):
        """ Read the next line from the batch import line or exit state machine if we are finished. """

        importid = context['importid']
        linenum = context.get('linenum', 0)

        cloudreader = cloudstorage.read_file(context['gcs_file_name'])
        logging.info('Seeking to (%s) %s in file %s', type(token), token or 0, context['gcs_file_name'])
        cloudreader.seek(int(token) if token else 0)

        obj['line'] = cloudreader.readline()
        if obj['line']:
            obj['line'] = obj['line'].strip()

        if not obj['line']:
            job_id = context.get(API_JOB_ID, None)
            notification_url = context.get(API_NOTIFICATION_URL, None)

            # nothing more to process, kick task to record the total number to import
            update_context = {
                'importid': importid,
                'number_to_process': linenum,
                API_JOB_ID: job_id,
                API_NOTIFICATION_URL: notification_url,
                'msid': context.get('msid', None),
            }
            taskname = 'SetBatchImportMetaNumberToProcess-%s' % importid
            startStateMachine('SetBatchImportMetaNumberToProcess', update_context, taskName=taskname)
            context['linenum'] = 0
            return None

        else:
            line = convert_keys_to_snake_case(json.loads(obj['line']))
            obj['line'] = json.dumps(line)
            context['linenum'] = linenum + 1
            return cloudreader.tell()


    def execute(self, context, obj):
        """ Process the current line of data. """

        if not obj.get('line'):
            return

        linenum = context['linenum']
        importid = context['importid']
        line = obj['line']
        job_id = context.get(API_JOB_ID, None)
        if job_id:
            logging.info('jobId: %s', job_id)

        # add a "counter" task to the batch-import-counter pull queue
        logging.info('Processing line (%d) %s', linenum, line)

        # add the line to dscache for subsequent imports
        cache_key = 'app.domain.batch_import:{}:{}'.format(importid, linenum)
        one_day = 86400
        result = dscache.set(cache_key, line, time=one_day, namespace='')
        if not result:
            raise Exception('Error storing JSON in dscache. Raising to provoke retry. Will cause an over-count.')
        context['mb_cache_key'] = cache_key

        return 'ok'


class GetMicrositeBatch:
    """ An entry action to inflate the MicrositeBatch object on obj['microsite_batch']. """

    def execute(self, context, obj):
        """ Execute. """
        linenum = context['linenum']
        importid = context['importid']
        pid = context['pid']
        msid = context.get('msid', None)
        cache_key = context['mb_cache_key']
        job_id = context.get(API_JOB_ID, None)
        if job_id:
            logging.info('jobId: %s', job_id)

        message = 'Processing line {} for import "{}"'.format(linenum, importid)
        if msid:
            message += ', msid "%s"' % msid
        message += '.'
        logging.info(message)

        serialized_mb = dscache.get(cache_key, namespace='')
        if not serialized_mb:
            raise Exception('Could not load "%s". Raising exception to provoke retry.' % cache_key)

        try:
            logging.info('Inflating Site.')
            mb = MicrositeBatch.deserialize(serialized_mb)
        except AttributeError as e:
            bi_error(importid, linenum, 'Unexpected attribute %s' % e.args[0], pid)
            logging.warn('Unexpected attribute in JSON. Skipping row.', exc_info=True)
            obj['invalid_line'] = serialized_mb
            return
        except InvalidBatchSpecificationException as e:
            bi_error(importid, linenum, e.args[0], pid)
            logging.warn('Invalid batch specification. Skipping row.', exc_info=True)
            obj['invalid_line'] = serialized_mb
            return

        logging.info('Site inflated')
        logging.info('Partner Site identifier "%s".', mb.identifier)

        obj['microsite_batch'] = mb


class ValidateLine:
    """ Validates that the line being processed is valid. """

    def execute(self, context, obj):
        """ Validates the line. """

        # Much of the validation actually occurs in the entry action GetMicrositeBatch;
        # here, we're just branching based on that result, so that subsequent states that
        # use this entry action don't have to bother with this check.
        if 'invalid_line' in obj:
            return 'row-skipped'

        linenum = context['linenum']
        importid = context['importid']
        pid = context['pid']
        mb = obj['microsite_batch']
        job_id = context.get(API_JOB_ID, None)
        if job_id:
            logging.info('jobId: %s', job_id)

        # check for the presence of the id. MicrositeBatch deserialization will take care of the remaining fields.
        if not mb.identifier:
            bi_error(importid, linenum, 'Identifier required.', pid)
            logging.warn('Partner microsite identifier required. Skipping row.')
            return 'row-skipped'

        return 'ok'


# pylint: disable=R0915
class ProcessMicrosite:
    """ Creates or updates the microsite entity. """

    def _get_hostslugs(self, pid, msid):
        """ Get the hostslugs for this site, minus any "redirector" hostslugs. """
        # Lookup the existing slug for this site; we'll assume there is exactly one
        # We look this up here to improve the idempotency of this state; that is, we want the
        # update_microsite() call below to effectively be the last, possibly failing, statement in the state
        hostslugs = lookup_hostslug_msid_mapping_for_msid(pid, msid)

        # filter out "redirector" hostslugs, they're not important here
        hostslugs = [hs for hs in hostslugs if hs.redirect_hostslug is None]
        return hostslugs

    def _resolve_api_user(self, context):
        """ Make a valiant effort to come up with the best API user. """
        # This is necessary because the v2 API shares creation with batch import.  If batch import is running, then
        # there is no API user, so designate the API user as "batch".  If an API call is running, make a best
        # effort to track down the account origin - it may be Partner Central, or it may be a partner.
        api_user = context.get(USER_CONFIG_KEY_API_USER)
        if api_user:
            if get_account_origin(api_user) == ORIGIN_UNKNOWN:
                # A partner is doing the create.
                api_user = USER_API
        else:
            api_user = USER_BATCH
        return api_user

    # pylint: disable=R0911
    def execute(self, context, obj):
        """ Create, or update, the microsite. """

        linenum = context['linenum']
        importid = context['importid']
        pid = context['pid']
        host = context['host']
        mb = obj['microsite_batch']
        job_id = context.get(API_JOB_ID, None)
        if job_id:
            logging.info('jobId: %s', job_id)

        existing_msid = get_msid_for_pmsid(pid, mb.identifier)
        existing_microsite = None
        if existing_msid:
            existing_microsite = get_microsite(existing_msid, pid=pid)

        if existing_microsite:  # BatchImport might point to a microsite that doesn't exist for some reason

            hostslugs = self._get_hostslugs(pid, existing_msid)

            # updating an existing microsite
            context['msid'] = existing_msid

            try:
                form = UpdateMicrositeForm(mb.formdata)
            except TypeError as e:
                # invalid form
                bi_error(importid, linenum, e.args[0], pid,
                         msid=existing_msid, partner_microsite_identifier=mb.identifier)
                logging.warn("Invalid form.", exc_info=True)
                context['partner_microsite_identifier'] = mb.identifier
                return 'row-skipped'

            form.msid.data = existing_msid
            form.hours_of_operation_json.data = mb.hours.json_value
            form.pmsid.data = mb.identifier
            if existing_microsite.spid:
                form.spid.data = existing_microsite.spid
            try:
                update_microsite(form, pid=pid)
            except exceptions.ValidationException as e:
                # invalid form
                bi_error(importid, linenum, e.args[0], pid,
                         msid=existing_msid, partner_microsite_identifier=mb.identifier)
                logging.warn("Invalid form.", exc_info=True)
                context['partner_microsite_identifier'] = mb.identifier
                return 'row-skipped'

            # If the incoming slug is different, set flag for future state to handle
            # This is potentially fragile because we're assuming only one hostslug.
            if len(hostslugs) > 1:
                logging.critical('Batch import found a site with more than one hostslug; this is unexpected '
                                 'and needs investigation. Only the first slug in the list will be remapped. '
                                 'PID "%s", MSID "%s".', pid, existing_msid)
            if not hostslugs:
                context['new_slug'] = mb.slug
            elif mb.slug.lower() != hostslugs[0].slug:
                context['new_slug'] = mb.slug
                context['old_slug'] = hostslugs[0].slug

            logging.info('Existing Site updated.')
        else:

            # creating a new microsite
            try:
                form = CreateMicrositeForm(mb.formdata)
            except TypeError as e:
                bi_error(importid, linenum, e.args[0], pid, partner_microsite_identifier=mb.identifier)
                logging.warn("Invalid form.", exc_info=True)
                context['partner_microsite_identifier'] = mb.identifier
                return 'row-skipped'

            form.hostname.choices = [(host, host)]
            form.hostname.data = host
            form.hours_of_operation_json.data = mb.hours.json_value
            form.pmsid.data = mb.identifier
            if not mb.layout:
                form.layout.data = LAYOUT_1_API_KEY  # Batch import should use short layout

            api_user = self._resolve_api_user(context)

            try:
                # create microsite
                microsite = create_blank_microsite(form, pid=pid, msid=context.get('msid', None), api_user=api_user)
                context['msid'] = microsite.msid
                logging.info('Created microsite "%s".', microsite.msid)
            except exceptions.ValidationException as e:
                # invalid form
                bi_error(importid, linenum, e.args[0], pid, partner_microsite_identifier=mb.identifier)
                logging.warn("Invalid form.", exc_info=True)
                context['partner_microsite_identifier'] = mb.identifier
                return 'row-skipped'
            except AccountGroupInvalidException as ae:
                # Some problem creating the account group
                bi_error(importid, linenum, ae.args[0], pid, partner_microsite_identifier=mb.identifier)
                logging.warn("Invalid form.", exc_info=True)
                context['partner_microsite_identifier'] = mb.identifier
                return 'row-skipped'
            except HostSlugMsidMappingExistsException:
                # microsite already exists
                hostslug = get_hostslug_msid_mapping(host, mb.slug)
                message = 'Microsite already exists for hostname "{}" and slug "{}".'.format(host, mb.slug)
                existing_site = get_microsite(hostslug.msid, pid)
                if existing_site:
                    message += ' The Site Id of the exisiting microsite is "{}" the new one is "{}". \
                    If you want to update the site the Ids have to match'.format(existing_site.pmsid, mb.identifier)
                else:
                    message += ' But a site could not be retrieved.'
                bi_error(importid, linenum, message, pid, partner_microsite_identifier=mb.identifier)
                logging.warn(message, exc_info=True)
                context['partner_microsite_identifier'] = mb.identifier
                return 'row-skipped'
            except BatchImportExistsException as ebie:
                bi_error(importid, linenum, ebie.args[0], pid, partner_microsite_identifier=mb.identifier)
                logging.warn(ebie.args[0], exc_info=True)
                context['partner_microsite_identifier'] = mb.identifier
                return 'row-skipped'

        return 'ok'


class ProcessPage:
    """ Create pages for the given microsite. """

    def execute(self, context, obj):
        """ Creates the page specified in context or Home. """

        linenum = context['linenum']
        importid = context['importid']
        pid = context['pid']
        msid = context['msid']
        mb = obj['microsite_batch']
        job_id = context.get(API_JOB_ID, None)
        if job_id:
            logging.info('jobId: %s', job_id)

        page_index = context.get('page_index', 0)
        if page_index >= len(mb.pages):
            return 'ok'

        current_page = mb.pages[page_index]

        microsite = get_microsite(msid, pid)
        nav_item = microsite.get_navigation(current_page.slug)

        # NOTE: If the page template changes, we don't not make any effort to delete the related resouces.
        # E.g., if a page was an Images page, and then became a Custom page, there are related images and mappings
        # stored in the system. The code become quite complex to handle this scenario, so it was decided
        # (Jason and Andreas Aug 15, 2011) to not worry about this rare case. This decision has some "feature"
        # aspects, e.g., if the page changes back from Custom to Images, the images are already present and
        # ready-to-go. Also, a future extension of the template system might include "sub-templates" so that
        # flipping between Images and, say, FooImages would leave the images intact.

        if nav_item:
            form_class = import_string('app.domain.workflow.page.Update%sPageForm' % current_page.template)
        else:
            form_class = import_string('app.domain.workflow.page.%sPageForm' % current_page.template)

        form = form_class(current_page.formdata)
        form.msid.data = msid
        form.pid.data = pid
        form.template.data = current_page.template

        good_page = True
        if nav_item:
            # update
            pageid = nav_item.pageid
            form.pageid.data = pageid
            try:
                update_page(form, microsite)
                logging.info("Updated %s Page: %s", current_page.template, pageid)
            except exceptions.ValidationException as e:
                # invalid form
                good_page = False
                message = 'Invalid page (slug "{}"): {}'.format(current_page.slug, e.args[0])
                bi_error(importid, linenum, message, pid,
                         msid=msid, partner_microsite_identifier=mb.identifier)
                logging.warn("Invalid form.", exc_info=True)
        else:
            # create
            try:
                pageid = create_page(form, microsite)
                logging.info("Created %s Page: %s", current_page.template, pageid)
            except exceptions.ValidationException as e:
                # invalid form
                good_page = False
                message = 'Invalid page (slug "{}"): {}'.format(current_page.slug, e.args[0])
                bi_error(importid, linenum, message, pid,
                         msid=msid, partner_microsite_identifier=mb.identifier)
                logging.warn("Invalid form.", exc_info=True)

        context['page_index'] = page_index + 1

        if good_page:
            if current_page.template == IMAGES:
                self.process_image_mappings(
                    json.loads(form.images.data), pageid, msid, pid, mb.identifier, linenum, importid
                )
                return 'process-images-page'
            if current_page.template == VIDEOS:
                # TODO - write test for this case
                youtube_context = {
                    'pid': pid,
                    'msid': msid,
                    'pageid': pageid,
                }
                startStateMachine('RetrieveYouTubeMetaInfo', youtube_context)

        if context['page_index'] >= len(mb.pages):
            return 'ok'
        else:
            return 'next-page'

    def process_image_mappings(self, image_dicts, pageid, msid, pid, pmsid, linenum, importid):
        """
        Process pageid blob mappings.
        Add any that are missing and delete any that are no longer referenced.
        Start up a state machine to process (fetch/update) images.
        """
        contexts = []
        mappings = process_mappings(image_dicts, pageid, msid, pmsid=pmsid, linenum=linenum,
                                    importid=importid, pid=pid)
        for mapping in mappings:
            # start state to fetch and process the image
            contexts.append({
                'mapping_key': mapping.key,
                'pid': pid,
                'msid': msid,
                'pmsid': pmsid,
                'importid': importid,
                'linenum': linenum
            })

        countdowns = list(range(0, len(contexts)*2, 2))
        startStateMachine('ProcessBatchImportPageImage', contexts, countdown=countdowns)


class ProcessImagesPage:
    """ Reorders PageidBlobMappings. """

    def execute(self, context, obj):
        """ Reorders the PageidBlobMappings based on the order of the provided images. """

        pid = context['pid']
        msid = context['msid']
        page_index = context['page_index']
        mb = obj['microsite_batch']

        if page_index < 1 or page_index > len(mb.pages):
            return 'invalid-page-index'

        images_page = mb.pages[page_index - 1]
        if images_page.template != IMAGES:
            return 'invalid-page-template'

        microsite = get_microsite(msid, pid=pid)
        nav_item = microsite.get_navigation(images_page.slug)
        pageid = nav_item.pageid

        # use an ImagesPageForm to ensure images is a dict - feels a bit hacky
        form = ImagesPageForm(formdata=MultiDict(mapping={'images': images_page.images}))
        image_urls = [image_dict['url'] for image_dict in json.loads(form.images.data)]
        reorder_pageid_blob_mappings(pageid, msid, image_urls, pid=pid)

        return 'ok'


class ReorderAndDeletePages:
    """ Reorders and deletes pages. """

    def execute(self, context, obj):
        """ Reorders and deletes the page according to the requested set of navigation slugs. """
        pid = context['pid']
        msid = context['msid']
        mb = obj['microsite_batch']

        desired_slugs = [p.slug for p in mb.pages]
        microsite = get_microsite(msid, pid)
        microsite.match_navigation(desired_slugs)
        return 'ok'


# W0223: Method is abstract in class 'ImageProcessor' but is not overridden
# This class is abstract and does not override the non-implemented methods in ImageProcessor
# pylint: disable=W0223
class BatchImportImageProcessor(ImageProcessor):
    """ Abstract parent for processing batch import images with logging on error/success. """

    def on_error(self, message, context, obj):
        """ Log batch import error processing image. """

        linenum = context['linenum']
        importid = context['importid']
        pid = context['pid']
        msid = context.get('msid')
        if 'microsite_batch' in obj:
            pmsid = obj['microsite_batch'].identifier
        else:
            pmsid = context.get('pmsid')

        bi_error(importid, linenum, message, pid, msid=msid, partner_microsite_identifier=pmsid)

    def on_success(self, image_url, context, obj):
        """ Log batch import image was successfully processed. """

        linenum = context['linenum']
        importid = context['importid']
        pid = context['pid']
        msid = context.get('msid')
        if 'microsite_batch' in obj:
            pmsid = obj['microsite_batch'].identifier
        else:
            pmsid = context.get('pmsid')

        bi_info(importid, linenum, 'Imported image "%s".' % image_url, pid,
                msid=msid, partner_microsite_identifier=pmsid)

    def get_image_url(self, context, obj):
        """ Returns the url of the image to process. """
        raise NotImplementedError("get_image_url must be implemented by child.")

    def get_blob_mapping(self, context, obj):
        """ Retrieve an existing blob mapping. """
        raise NotImplementedError("get_blob_mapping must be implemented by child.")

    def add_blob_mapping(self, context, obj, blob_info, import_info):
        """ Add a blob mapping with the given blob and import info. """
        raise NotImplementedError("add_blob_mapping must be implemented by child.")

class MicrositeImageProcessor(BatchImportImageProcessor):
    """ Abstract parent for processing images that belong to the top-level microsite. """

    def get_image_url(self, context, obj):
        """ Returns the url of the image to process. """
        raise NotImplementedError("get_image_url must be implemented by child.")

    def get_blob_mapping(self, context, obj):
        """ Retrieve an existing msid blob mapping. """
        return get_msid_blob_mapping(context['msid'], self.CATEGORY, context['pid'])

    def add_blob_mapping(self, context, obj, blob_info, import_info):
        """ Add a new msid blob mapping for a logo. """
        img = get_image(blob_info)
        add_msid_blob_mapping(context['msid'], self.CATEGORY, blob_info.key(), img.width, img.height,
                              import_info=import_info, pid=context['pid'])

    def remove_blob_mapping(self, context, obj):
        """ A hook to remove a blob mapping if the incoming batch does not require it. """
        key = blob_mappings.MsidBlobMapping.build_key(context['msid'], self.CATEGORY, pid=context['pid'])
        delete_blob_mapping_by_key(key)


class ProcessLogo(MicrositeImageProcessor):
    """ Process images for a given microsite. """

    CATEGORY = blob_mappings.LOGO
    DONE_EVENT = 'ok'

    def get_image_url(self, context, obj):
        """ Retrieve the logo url from the microsite batch object. """
        microsite_batch = obj['microsite_batch']
        return microsite_batch.logo_url


class ProcessDesktopBackground(MicrositeImageProcessor):
    """ Process desktop background image for a given microsite. """

    CATEGORY = blob_mappings.DESKTOP_BACKGROUND
    DONE_EVENT = 'ok'

    def get_image_url(self, context, obj):
        """ Retrieve the logo url from the microsite batch object. """
        microsite_batch = obj['microsite_batch']
        return microsite_batch.desktop_background_url


class ProcessMobileBackground(MicrositeImageProcessor):
    """ Process mobile background image for a given microsite. """

    CATEGORY = blob_mappings.MOBILE_BACKGROUND
    DONE_EVENT = 'ok'

    def get_image_url(self, context, obj):
        """ Retrieve the logo url from the microsite batch object. """
        microsite_batch = obj['microsite_batch']
        return microsite_batch.mobile_background_url


class ProcessBatchImportPageImage(ProcessPageImage, BatchImportImageProcessor):
    """ Process a page image while reporting batch import error/success messages. """
    pass


class RemapSlug:
    """ Sets up a new slug, deleting the old one if necessary. """

    def execute(self, context, obj):
        """ If new_slug on context, action taken, otherwise, simply skips. """

        if not 'new_slug' in context:
            return 'ok'  # nothing to do

        new_slug = context['new_slug']
        host = context['host']
        pid = context['pid']
        msid = context['msid']

        # We need to remove the old one first in case there are two entries that are attempting to swap
        # slugs. If we don't remove the old ones first, they end up in a deadlock waiting for each other
        # (slugs must be unique, so when one is used, it cannot be used to point to another).
        if 'old_slug' in context:
            old_slug = context['old_slug']
            remove_msid_for_hostname_slug(host, slug=old_slug, redirect_host=host, redirect_slug=new_slug)

        try:
            add_hostslug_msid_mapping(host, new_slug, pid, msid)
        except HostSlugMsidMappingExistsException as e:
            bi_error(context['importid'], context['linenum'], e.args[0], pid, msid=msid)
            logging.warn(e.args[0], exc_info=True)

        return 'ok'


class CreateBatchImportEntity:
    """ Creates a BatchImport entity for a microsite. """

    def post_to_notification_url(self, job_id, notification_url, msid):
        """POST a message to notify the API provisioning host."""
        if job_id and notification_url and not notification_url == 'None':
            defer_url = DEFERRED_ROOT + 'MicroSite/NotifyUnifiedProvisioningProcess/'
            deferred.defer(
                UnifiedProvisionNotifier.post_to_notification_url,
                job_id=job_id, notification_url=notification_url, msid=msid,
                _url=defer_url, _queue='unified-provisioning-notification'
            )

    def execute(self, context, obj):
        """ Creates the entity. """
        pid = context['pid']
        msid = context['msid']
        importid = context['importid']
        mb = obj['microsite_batch']
        linenum = context['linenum']
        notification_url = context.get(API_NOTIFICATION_URL, None)
        job_id = context.get(API_JOB_ID, None)
        if job_id:
            logging.info('jobId: %s', job_id)

        self.count_line(importid=importid, pid=pid, linenum=linenum)
        create_batch_import_entity(pid, mb.identifier, msid, importid)
        logging.info('Site put to datastore.')

        self.post_to_notification_url(job_id, notification_url, msid)

    def count_line(self, importid, pid, linenum):
        """ Adds a task to the pull queue to count the lines. """
        params = {
            'importid': importid,
            'pid': pid,
            'linenum': linenum
        }
        taskname = 'UpdateBatchImportMetaCountJob-%s-%d' % (importid, linenum)
        task = Task(name=taskname, method='PULL', payload=json.dumps(params))
        queue = Queue(name='batch-import-counter')
        try:
            queue.add(task)
        except (TaskAlreadyExistsError, TombstonedTaskError):
            pass  # this is okay; the task was emitted on a previous run


class BadInput:
    """ Final state when the necessary inputs are not provided. """

    def execute(self, context, obj):
        """ Output some information. """
        logging.critical('Necessary inputs are not present. Batch import will not be processed.')
        return


class RowSkipped:
    """ Final state for the failure of an individual input row. """

    def execute(self, context, obj):
        """ Output some information. """

        importid = context['importid']
        linenum = context['linenum']
        pid = context['pid']
        job_id = context.get(API_JOB_ID, None)
        if job_id:
            logging.info('jobId: %s', job_id)

        message = 'Row %d skipped' % linenum

        msid = context.get('msid')
        pmsid = context.get('partner_microsite_identifier')
        if pmsid:
            message += ' for identifier "%s"' % pmsid

            # update failed on the BatchImportMeta
            @ndb.transactional
            def tx(_importid, partner_microsite_identifier):
                """ Update BatchImportMeta in a transaction. """
                key = BatchImportMetaModel.build_key(_importid)
                entity = key.get()
                entity.failed.append(partner_microsite_identifier)
                entity.put()

            tx(importid, pmsid)

        message += '.'
        bi_warn(importid, linenum, 'Row skipped.', pid, msid=msid, partner_microsite_identifier=pmsid)
        logging.warn(message)
        inc_context = {'importid': importid, 'increment_by': 1}
        startStateMachine('IncrementBatchImportMetaNumberComplete', inc_context)
        return
