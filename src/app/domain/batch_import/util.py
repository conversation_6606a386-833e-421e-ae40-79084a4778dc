""" Utilities for batch_import. """

import time
import logging
import json
from datetime import datetime
from google.appengine.api import urlfetch as google_urlfetch, files as google_files
from google.appengine.ext.blobstore.blobstore import BlobInfo
from google.appengine.api import images as images_api
from google.appengine.ext import ndb
from google.appengine.api.taskqueue.taskqueue import Queue
from fantasm.fsm import startStateMachine
from app.models.batch_import import BatchImportMeta as BatchImportMetaModel
from app.models.remote import YouTubeRemoteApi
from app.domain.blob_mappings import ImportInfo
from app.domain.page import get_page, update_page_entity, VideosPage
from app.domain.pageid_blob_mapping import get_pageid_blob_mapping, add_pageid_blob_mapping

COUNTER_PULL_BATCH_SIZE = 1000
COUNTER_PULL_LEASE_DURATION = 60*15  # 15 minutes

# W0613:Unused argument 'obj'
# Fantasm expects a particular interface
# pylint: disable=W0613

# R0911:Too many return statements (7/6)
# Generally helps keep a Fantasm machine clean
# pylint: disable=R0911


class SetBatchImportMetaNumberToProcess:
    """ Used to set the number_to_process on a BatchImportMeta. """

    def execute(self, context, obj):
        """ Update the number_to_process. """

        @ndb.transactional
        def tx(importid, number_to_process):
            """ Update BatchImportMeta in a transaction. """
            key = BatchImportMetaModel.build_key(importid)
            entity = key.get()
            entity.number_to_process = number_to_process
            entity.put()

        tx(context['importid'], context['number_to_process'])


class IncrementBatchImportMetaNumberComplete:
    """ Used to update the number_completed on a BatchImportMeta. """

    def execute(self, context, obj):
        """ Update the number_completed. """

        @ndb.transactional
        def tx(importid, increment_by):
            """ Update BatchImportMeta in a transaction. """
            key = BatchImportMetaModel.build_key(importid)
            entity = key.get()
            entity.number_completed += increment_by
            entity.put()

        tx(context['importid'], context['increment_by'])


class ProcessBatchImportCounterQueue:
    """ Processes the batch-import-counter queue. """

    def execute(self, context, obj):
        """ Pull some tasks, group them, kick off tasks to increment counters. """

        count_until = context['count_until']
        stime = time.time()
        queue = Queue(name='batch-import-counter')

        tasks = queue.lease_tasks(COUNTER_PULL_LEASE_DURATION, COUNTER_PULL_BATCH_SIZE)

        while tasks:

            # Group according to 'importid'. Note that this works over _all_ importids (two machines will
            # simply race over one another, but the queue.lease_tasks guarantees that they don't get overlapping
            # work). At the end of this state, we use the context['importid'] to determine if this particular
            # machine instance should stop; each batch import kicks off one of these machine instances to count
            # up the tasks, so if two are running overlapping each will continue until their respective import
            # has been all counted.
            context_by_importid = {}
            for task in tasks:
                payload = json.loads(task.payload)
                importid = payload['importid']
                if importid not in context_by_importid:
                    context_by_importid[importid] = {
                        'importid': importid,
                        'increment_by': 1
                    }
                else:
                    context_by_importid[importid]['increment_by'] += 1

            # start a set of state machines for each collection
            contexts = list(context_by_importid.values())
            startStateMachine('IncrementBatchImportMetaNumberComplete', contexts)

            # delete this set
            queue.delete_tasks(tasks)

            # stop if we're over 5 minutes
            if time.time() - stime > 5*60:
                break

            # pull another set
            tasks = queue.lease_tasks(COUNTER_PULL_LEASE_DURATION, COUNTER_PULL_BATCH_SIZE)

        # If we're not past count_until and the job for importid is not complete, loop back to ourselves to continue.
        # Note the special case that number_to_process is 0: this is a race condition that this machine has fired
        # up before the original batch import fan-out has had the chance to count through all the lines. In this
        # case, we'll let this machine continue.
        meta_key = BatchImportMetaModel.build_key(context['importid'])
        meta = meta_key.get()
        if count_until > datetime.utcnow() and \
           (meta.number_to_process == 0 or meta.number_completed < meta.number_to_process):
            return 'continue'
        else:
            # halt this machine, we're done
            return


class RetrieveYouTube:
    """ Lookup the YouTube stuff for the videos page, if necessary. """

    def execute(self, context, obj, urlfetch=None):
        """ Lookup YouTube meta information. """
        pid = context['pid']
        msid = context['msid']
        pageid = context['pageid']

        page = get_page(pageid, msid, pid=pid)

        if not isinstance(page, VideosPage):
            return

        for video in page.videos:
            if video.title or video.caption or video.duration or video.thumbnail_url:
                 # Don't overwrite any existing information, including stuff that might have come up
                 # through the batch_import.
                continue
            video.url = YouTubeRemoteApi.get_full_url_from_shortened(video.url)
            videoid = YouTubeRemoteApi.parse_videoid(video.url)
            if videoid:
                details = YouTubeRemoteApi(urlfetch=urlfetch).get_video_details(videoid)
                video.title = details.title
                video.caption = details.caption
                video.duration = details.duration
                video.thumbnail_url = details.large_thumbnail_url
                video.phone_url = details.mobile_video_url

        update_page_entity(pageid, msid, videos=str(page.videos), pid=pid)


class ImageProcessor:
    """ Retrieves an image from a given url and saves to blobstore. """

    CATEGORY = None
    DONE_EVENT = None

    def execute(self, context, obj, urlfetch=None, files=None):
        """
        Retrieve the image from its url and save it to blobstore.
        Returns the blob key for the saved image.
        """
        urlfetch = urlfetch or google_urlfetch
        files = files or google_files

        image_url = self.get_image_url(context, obj)

        if not image_url:
            self.remove_blob_mapping(context, obj)
            return self.DONE_EVENT  # just move to next state

        # retrieve any existing BlobMapping, which might have previous download information (e.g., Etag)
        mapping = self.get_blob_mapping(context, obj)

        # try a HEAD request to compare the Etag / Last-Modified
        if mapping and mapping.blobkey and mapping.import_info and mapping.import_info.url == image_url:

            try:

                logging.debug('Fetching HEAD for "%s".', image_url)
                response = urlfetch.fetch(image_url, method='HEAD')

                if response.status_code == 200:

                    if mapping.import_info.etag and \
                       mapping.import_info.etag == response.headers.get('Etag'):

                        logging.debug('HEAD request to "%s" says that Etag matches. Skipping image.',
                                      image_url)
                        return self.DONE_EVENT  # no update required

                    if mapping.import_info.last_modified and \
                       mapping.import_info.last_modified == response.headers.get('Last-Modified'):

                        logging.debug('HEAD request to "%s" says that Last-Modified matches. Skipping image.',
                                      image_url)
                        return self.DONE_EVENT  # no update required

                # else fall-through

            except google_urlfetch.InvalidURLError:
                message = 'The url "%s" is invalid. Skipping image.' % image_url
                self.on_error(message, context, obj)
                logging.warn(message)
                return self.DONE_EVENT
            except (google_urlfetch.DownloadError, google_urlfetch.DeadlineExceededError):
                logging.warn('Could not contact remote server for "%s". Will retry shortly.')
                # fall-through

        # retrieve image
        response = None
        try:
            logging.debug('Fetching "%s".', image_url)
            response = urlfetch.fetch(image_url)
            logging.info('Received %d response for "%s".', response.status_code, image_url)
        except google_urlfetch.InvalidURLError:
            message = 'The url "%s" is invalid. Skipping image.' % image_url
            self.on_error(message, context, obj)
            logging.warn(message)
            return self.DONE_EVENT
        except google_urlfetch.ResponseTooLargeError:
            message = 'The response from "%s" is too large. Skipping image.' % image_url
            self.on_error(message, context, obj)
            logging.warn(message)
            return self.DONE_EVENT
        except (google_urlfetch.DownloadError, google_urlfetch.DeadlineExceededError):
            logging.warn('Could not contact remote server for "%s". Will retry shortly.')
            # fall-through

        # Note: response may be None at this point, in the case of a DownloadError/DeadlineExceededError above
        if not response or response.status_code != 200:
            # we didn't get a good response, so we'll "loop" and track a counter
            counter = context.get('process_image_counter', 1)

            max_retries = 10
            if counter > max_retries:
                message = 'Unable to retrieve "%s" after %d tries. Skipping image.' % (image_url, max_retries)
                if response:
                    message += ' Response from server: %s.' % response.status_code
                else:
                    message += ' Could not contact server.'
                self.on_error(message, context, obj)
                if response:
                    message += "\nContent: %s" % response.content
                logging.warn(message)
                return self.DONE_EVENT  # move on to next state

            context['process_image_counter'] = counter + 1
            return 'retry'

        assert response and response.status_code == 200  # sanity check

        try:
            self.store_image(context, obj, image_url, files, response)
            self.on_success(image_url, context, obj)
        except Exception as e:
            message = 'Retrieved a %s when trying to save the image at %s. ' \
                      'Please try to re-save the image and upload it again.' % (e.__class__, image_url)
            logging.exception(message)
            self.on_error(message, context, obj)

        return self.DONE_EVENT

    def store_image(self, context, obj, image_url, files, response):
        """ Stores the image to blob store and adds new mapping.

        @param image_url the image's url
        @param context the machine context
        @param files the files API
        @param response the HTTP response
        """
        if not response:
            raise ValueError('response is required.')

        # save image to blobstore
        file_name = files.blobstore.create()
        f = files.open(file_name, 'a')
        try:
            f.write(response.content)
        finally:
            f.close()
        files.finalize(file_name)

        # get key for newly saved image
        blob_key = files.blobstore.get_blob_key(file_name)

        # HACK: get_blob_key doesn't always return right away; we'll loop and sleep for a bit
        # http://code.google.com/p/googleappengine/issues/detail?id=4944
        sleep_time = 0.02
        total_sleep = 0
        for _ in range(1, 10):
            if blob_key:
                break
            else:
                time.sleep(sleep_time)
                total_sleep += sleep_time
                sleep_time *= 2
                blob_key = files.blobstore.get_blob_key(file_name)
        if not blob_key:
            raise Exception('Did not retrieve blob_key after waiting %f seconds.' % total_sleep +
                            'Raising error to provoke retry. This will likely result in an orphaned blob.')
        # ENDHACK

        last_modified = response.headers.get('Last-Modified')
        etag = response.headers.get('Etag')

        blob_info = BlobInfo.get(blob_key)
        import_info = ImportInfo(image_url, last_modified=last_modified, etag=etag)

        try:
            self.add_blob_mapping(context, obj, blob_info, import_info)
        except images_api.Error:
            logging.error('Image import did not succeed. Blob will be deleted.', exc_info=True)
            startStateMachine('RemoveBlob', {'blobkey': blob_key})
            raise

    def get_image_url(self, context, obj):
        """ Returns the url of the image to process. """
        raise NotImplementedError("get_image_url must be implemented by child.")

    def get_blob_mapping(self, context, obj):
        """ Retrieve an existing blob mapping. """
        raise NotImplementedError("get_blob_mapping must be implemented by child.")

    def add_blob_mapping(self, context, obj, blob_info, import_info):
        """ Add a blob mapping with the given blob and import info. """
        raise NotImplementedError("add_blob_mapping must be implemented by child.")

    def remove_blob_mapping(self, context, obj):
        """ A hook to remove a blob mapping if the incoming batch does not require it.
            Does NOT have to be overridden
        """
        return  # no-op

    def on_error(self, message, context, obj):
        """ Optional hook to provide custom error handling if desired. """
        return

    def on_success(self, image_url, context, obj):
        """ Optional hook to notify when image has been successfully processed. """
        return


class ProcessPageImage(ImageProcessor):
    """ Process image for given page. """

    def get_image_url(self, context, obj):
        """ Retrieve the image url from an existing pageid blob mapping. """
        mapping = self.get_blob_mapping(context, obj)
        return mapping.import_info.url if mapping and mapping.import_info else None

    def get_blob_mapping(self, context, obj):
        """ Retrieve a pageid blob mapping and add it to obj. """
        mapping = obj.get('mapping')
        if mapping:
            return mapping

        mapping_key = context.get('mapping_key')
        if not mapping_key:
            return None

        mapping = get_pageid_blob_mapping(mapping_key)
        if not mapping:
            logging.error("Error retrieving page id blob mapping for key: %s.", mapping_key)
            return None

        obj['mapping'] = mapping
        return mapping

    def add_blob_mapping(self, context, obj, blob_info, import_info):
        """ Add a new pageid blob mapping (old one will be deleted). """
        mapping = self.get_blob_mapping(context, obj)

        add_pageid_blob_mapping(mapping.pageid, mapping.msid, order=mapping.order, existing_key=mapping.key,
                                blob_key=blob_info.key(), import_info=import_info, pid=context['pid'],
                                caption=mapping.caption, alt_text=mapping.alt_text, title=mapping.title)


class UnifiedProvisionNotifier:
    """POST a message to notify the API provisioning host."""

    payload_format = """
    {
        "status": 200,
        "version": "2.0",
        "jobId": "%s",
        "notificationDateTime": "%s",
        "productId": "MS",
        "data":
            { "msid": "%s" }
        ,
        "message": "Account '%s' created."
    }
    """

    @classmethod
    def post_to_notification_url(cls, job_id, notification_url, msid):
        """POST a message to notify the API provisioning host."""
        utc_time = "%sZ" % datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S')
        final_payload = cls.payload_format % (job_id, utc_time, msid, msid)
        google_urlfetch.fetch(
            notification_url,
            method='POST', headers={'Content-Type': 'application/json'}, payload=final_payload
        )
