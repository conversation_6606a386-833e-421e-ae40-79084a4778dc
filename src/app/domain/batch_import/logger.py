""" Batch import logging. """

import hashlib
from google.appengine.ext import ndb
from fantasm.fsm import startStateMachine
from app.models.batch_import import BatchImportLog as BatchImportLogModel, BatchImportMeta as BatchImportMetaModel

INFO = 2
WARN = 3
ERROR = 4
VALID_LEVELS = (INFO, WARN, ERROR)
LEVEL_NAME = {
    INFO: "INFO",
    WARN: "WARN",
    ERROR: "ERROR"
}

def log(level, importid, linenum, message, pid, msid=None, partner_microsite_identifier=None):
    """ Logs a batch import message. """
    if level is None:
        raise ValueError('level is required.')
    level = int(level) # raises a ValueError if not an int
    if level not in VALID_LEVELS:
        raise ValueError('level "{}" must be in {}.'.format(level, VALID_LEVELS))
    if not importid:
        raise ValueError('importid is required.')
    if linenum is None:
        raise ValueError('linenum is required.')
    linenum = int(linenum) # raises a ValueError if not an int
    if not message:
        raise ValueError('message is required.')
    if not pid:
        raise ValueError('pid is required.')
        
    message = '{}: {}'.format(LEVEL_NAME[level], message)
    
    hash_str = '%d:%s:%d:%s' % (level, importid, linenum, message)

    m = hashlib.md5()
    m.update(hash_str.encode('utf-8'))
    taskname = 'batch-import-log-{}-{}-{}'.format(importid, linenum, m.hexdigest())
    
    context = {
        'level': level,
        'importid': importid,
        'linenum': linenum,
        'message': message,
        'pid': pid
    }
    if msid:
        context['msid'] = msid
    if partner_microsite_identifier:
        context['identifier'] = partner_microsite_identifier
        
    startStateMachine('BatchImportLogger', context, taskName=taskname)
    
def info(importid, linenum, message, pid, msid=None, partner_microsite_identifier=None):
    """ Logs an INFO message. """
    log(INFO, importid, linenum, message, pid, msid=msid, partner_microsite_identifier=partner_microsite_identifier)

def warn(importid, linenum, message, pid, msid=None, partner_microsite_identifier=None):
    """ Logs a WARN message. """
    log(WARN, importid, linenum, message, pid, msid=msid, partner_microsite_identifier=partner_microsite_identifier)
    
def error(importid, linenum, message, pid, msid=None, partner_microsite_identifier=None):
    """ Logs an ERROR message. """
    log(ERROR, importid, linenum, message, pid, msid=msid, partner_microsite_identifier=partner_microsite_identifier)

class LogMessage:
    """ Logs a message. """
    
    # W0613: 71:LogMessage.execute: Unused argument 'obj'
    # fantasm interface
    def execute(self, context, obj): # pylint: disable=W0613
        """ Stores the log message. """
        
        level = context['level']
        importid = context['importid']
        linenum = context['linenum']
        message = context['message']
        pid = context['pid']
        msid = context.get('msid')
        identifier = context.get('identifier')

        @ndb.transactional
        def tx():
            """ update parent and entity """
            # update the count on the parent
            parent_key = BatchImportMetaModel.build_key(importid)
            parent = parent_key.get()
            if level == ERROR:
                parent.error += 1
            elif level == WARN:
                parent.warn += 1
            elif level == INFO:
                parent.info += 1
            parent.put()
            
            # update or store the message in Tx (due to messages list)
            key = BatchImportLogModel.build_key(importid, linenum)
            entity = key.get()
            if entity:
                entity.messages.append(message)
                if level > entity.level:
                    entity.level = level
                entity.put()
            else:
                kwargs = {
                    'key': key,
                    'import_id': importid,
                    'linenum': linenum,
                    'pid': pid,
                    'msid': msid,
                    'pmsid': identifier,
                    'level': level,
                    'messages': [message]
                }
                entity = BatchImportLogModel(**kwargs)
                entity.put()
                
        tx()
