""" The domain methods and objects for batch import. """

import datetime
import logging

from google.appengine.ext import ndb


from app.domain import exceptions
from app.models.batch_import import BatchImportMeta as BatchImportMetaModel, BatchImport as BatchImportModel, \
    BatchImportLog as BatchImportLogModel
from app.models.microsite import Microsite as MicrositeModel
import settings


BATCH_FILES_BUCKET = 'batch-files-microsite-%s' % settings.ENVIRONMENT_NAME.lower()


def create_batch_import(msid, pmsid, pid):
    """ Create a batch import entity. """
    if not pmsid:
        return
    if not msid:
        raise ValueError('msid required.')
    if not pid:
        raise ValueError('pid is required.')
    
    # if a microsite doesn't exist for the pmsid, then just remove the old BatchImportModel because
    # it's for a site that has previously been deleted and the same pmsid is being used for a new site
    if get_msid_for_pmsid(pid, pmsid) is None:
        delete_batch_import(pmsid, pid=pid)

    @ndb.transactional
    def tx(tx_msid, tx_pmsid, tx_pid):
        """ Creates new BatchImport entity if one does not already exist for pid/pmsid and deletes the old one. """

        # check if entity already exists for this pid/pmsid
        key = BatchImportModel.build_key(tx_pid, tx_pmsid)
        entity = key.get()
        if entity:
            if entity.msid == tx_msid:
                logging.info('BatchImport Entity exists with the correct msid. No need to create it.')
            else:
                raise exceptions.BatchImportExistsException(
                    'BatchImport exists for pid "{}" and pmsid "{}"'.format(tx_pid, tx_pmsid)
                )
        else:
            # create new Batch Import entity
            last_import_id = 'Import-%s-never' % tx_pid
            entity = BatchImportModel(key=key, pid=tx_pid, partner_microsite_identifier=tx_pmsid, msid=tx_msid,
                                      last_import_id=last_import_id)
            entity.put()

    tx(msid, pmsid, pid)


def update_batch_import(msid, old_pmsid, new_pmsid, pid):
    """
    Creates a new BatchImport entity for new_pmsid if one does not already exists.
    Removes existing BatchImport entity for old_pmsid if it exists.

    @param msid: The microsite identifier the BatchImport entity refers to.
    @param old_pmsid: The previous BatchImport partner microsite identifier.
    @param new_pmsid: The new partner microsite identifier to create a BatchImport entity for.
    @param pid: The partner for which BatchImport entity exists.
    """
    if old_pmsid == new_pmsid:
        return

    create_batch_import(msid, new_pmsid, pid=pid)
    delete_batch_import(old_pmsid, pid=pid)


def delete_batch_import(pmsid, pid):
    """ Delete a BatchImport entity. """
    if not pmsid:
        return
    if not pid:
        raise ValueError('pid is required.')

    # delete the old entity (if it exists)
    key = BatchImportModel.build_key(pid, pmsid)
    key.delete()


def create_batch_import_meta_entity(pid, filename=None, number_to_process=0, number_completed=0, key=None):
    """ Creates a new BatchImportMeta entity. 
    
    @returns the import_id of the new meta entity
    """
    if not pid:
        raise ValueError('pid is required.')
    pid = pid.upper()
    if not key:
        key = BatchImportMetaModel.generate_new_key(pid)
    else:
        key = BatchImportMetaModel.build_key(key_name=key)
    import_id = key.string_id()
    entity = BatchImportMetaModel(key=key, pid=pid, import_id=import_id, filename=filename, 
                                  number_to_process=number_to_process, number_completed=number_completed)
    entity.put()
    return import_id


def create_batch_import_entity(pid, partner_microsite_identifier, msid, last_import_id):
    """ Creates a BatchImport entity, overwriting any existing one. 
    
    @returns the key_name of the new entity.
    """
    if not pid:
        raise ValueError('pid is required.')
    if not partner_microsite_identifier:
        raise ValueError('partner_microsite_identifier is required.')
    if not msid:
        raise ValueError('msid is required.')
    if not last_import_id:
        raise ValueError('last_import_id is required.')
    pid = pid.upper()
    msid = msid.upper()
    key = BatchImportModel.build_key(pid, partner_microsite_identifier)
    entity = BatchImportModel(key=key, pid=pid, partner_microsite_identifier=partner_microsite_identifier,
                              msid=msid, last_import_id=last_import_id)
    entity.put()
    return key.string_id()
    

def create_batch_file(file_value, arg_file_name="unknown"):
    """ Creates a file named arg_file_name with the contents of file_value. """
    gcs_file_name = f'/{BATCH_FILES_BUCKET}/{arg_file_name}'
    cloudstorage.write_file(gcs_file_name,
                            file_value,
                            file_type='text/json')

    return gcs_file_name


def get_msid_for_pmsid(pid, partner_microsite_identifier):
    """ Returns the msid for the (pid, partner_microsite_identifier) tuple. None if never imported before. """
    if not pid:
        raise ValueError('pid is required.')
    if not partner_microsite_identifier:
        raise ValueError('partner_microsite_identifier is required.')
    pid = pid.upper()

    # Get microsit model using pmsid lookup
    ms_entity = MicrositeModel.lookup_by_pmsid(partner_microsite_identifier, pid)
    return ms_entity.msid if ms_entity else None


def lookup_recent_import_jobs(count=25):
    """ Returns the most recent import jobs. """
    qr = BatchImportMetaModel.lookup_most_recent(count=count)
    return [BatchImportMeta.from_model(e) for e in qr.results]


def lookup_log_messages(import_id, count=1000):
    """ Returns the log messages for an import. """
    qr = BatchImportLogModel.lookup_by_import_id(import_id, count=count)
    items = []
    for result in qr.results:
        kwargs = {
            'pid': result.pid,
            'import_id': result.import_id,
            'linenum': result.linenum,
            'msid': result.msid,
            'pmsid': result.pmsid
        }
        for message in result.messages:
            kwargs['message'] = message
            items.append(BatchImportLog(**kwargs))
    return items
    

class BatchImportMeta:
    """ A domain object for a BatchImportMeta. """
    
    def __init__(self, **kwargs):
        """ Initialize. """
        if not kwargs.get('import_id'):
            raise ValueError('import_id is required.')
            
        self.import_id = kwargs.get('import_id')
        self.number_completed = kwargs.get('number_completed')
        self.number_to_process = kwargs.get('number_to_process')
        self.pid = kwargs.get('pid')
        self.filename = kwargs.get('filename')
        self.failed = kwargs.get('failed')
        self.created = kwargs.get('created')
        self.info = kwargs.get('info')
        self.warn = kwargs.get('warn')
        self.error = kwargs.get('error')
        
    @property
    def created_cst(self):
        """ The created datetime as CST. """
        return self.created - datetime.timedelta(hours=6)
        
    @property
    def message_count(self):
        """ The total number of message. """
        return self.info + self.warn + self.error
        
    @classmethod
    def from_model(cls, model):
        """ Creates a domain object given a model object. """
        if not model:
            return None
        return cls(**model.to_dict())
    

class BatchImportLog:
    """ An individual log message. """
    def __init__(self, **kwargs):
        self.pid = kwargs.get('pid')
        self.import_id = kwargs.get('import_id')
        self.linenum = kwargs.get('linenum')
        self.message = kwargs.get('message')
        self.msid = kwargs.get('msid')
        self.pmsid = kwargs.get('pmsid')
