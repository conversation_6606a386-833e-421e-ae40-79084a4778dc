"""
Domain for dealing with triggering hotness when user shows interest in listing sync pro
"""
import logging
import statsd

from vauth.foundational_products import is_impersonation_session
from vpubsub.messages import InterestInListingSyncProMessage


def user_interested_in_listing_sync_pro(partner_id, market_id, account_group_id, customer_message, current_user_id):
    """ Send a pubsub message indicating that a user showed interest in listing sync pro """
    secondary_tag = 'not_impersonated'

    data = {
        'partner_id': partner_id,
        'market_id': market_id,
        'account_group_id': account_group_id,
        'message': customer_message,
        'user_id': current_user_id,
    }
    message = InterestInListingSyncProMessage(**data)

    if not is_impersonation_session():
        message.publish()
    else:
        secondary_tag = 'impersonated'
        logging.info("Skipping publish of LSP message because user is impersonating")
        logging.debug("Message was: %s", message)

    metric = statsd.StatsDMetric('interested_in_lsp', tags=[f'ms_user_activate_now:{secondary_tag}'])
    statsd.tick_metric(metric)
