""" Functions and classes for navigation processing. """

def update_navigation_entity(order, navigation_slug, pageid, microsite):
    """ Updates an existing navigation item entity. """

    if order and order < 1:
        raise ValueError('order cannot be less than 1.')
    if not pageid:
        raise ValueError('pageid is required.')
    if not microsite:
        raise ValueError('microsite is required.')

    nav_item = microsite.get_navigation_for_pageid(pageid)
    if not nav_item:
        raise ValueError('nav_item for given pageid not found')

    if nav_item.navigation_slug != navigation_slug:
        # update navigation slug
        microsite.update_navigation_slug(pageid, navigation_slug)

    if not order:
        return
    
    # reorder list of nav items
    nav_item = microsite.get_navigation_for_pageid(pageid)
    main_navigation = microsite.main_navigation
    main_navigation.remove(nav_item)

    # insert nav item at new position
    main_navigation.insert(order-1, nav_item)
    # update navigation order
    microsite.match_navigation([nav.navigation_slug for nav in main_navigation])
