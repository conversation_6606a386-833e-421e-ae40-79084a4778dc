""" Exceptions for the domain layer. """


class DomainException(Exception):
    """ Parent class for all domain exceptions. """
    pass


class MicrositeAlreadyActivated(Exception):
    """ Tried to activate a microsite that is not a lite account"""


class MSAccountNotFoundException(DomainException):
    """ MS account not found on account group """

    def __init__(self, account_group_id):
        """ Initialize. """
        super().__init__(
            "Microsite account not found on account group: %s", account_group_id
        )


class MSAuthException(DomainException):
    """ Session does not have access to the account group """
    pass


class MSAccountMismatchException(DomainException):
    """ Session does not match the account group """
    pass


class InvalidImageUrlException(DomainException):
    """ Provided ImageUrl did not return a 200. """

    def __init__(self, url, status_code):
        """ Initialize. """
        msg = 'url: "{}" returned a non-200 ("{}") status code.'.format(url, status_code)
        super().__init__(msg)


class PartnerNotFoundException(DomainException):
    """ The request partner was not found. """
    status_int = 404


class PartnerExistsException(DomainException):
    """ A partner with given pid already exists. """

    def __init__(self, pid):
        """ Initialize. """
        msg = 'Partner "%s" already exists.' % pid
        super().__init__(msg)


class HostPidMappingExistsException(DomainException):
    """ A mapping keyed by host already exists. """
    status_int = 409

    def __init__(self, hostname, existing_pid_mapping):
        """ Initialize. """
        msg = 'A host->pid mapping keyed by "%s" already exists, pointing to pid "%s".' % \
              (hostname, existing_pid_mapping)
        super().__init__(msg)


class PartnerHostExistsException(DomainException):
    """ A partner and the host given already exist. """

    def __init__(self, hostname, pid):
        """ Initialize. """
        msg = 'A partner with the pid "%s" and the host "%s" already exist.' % \
              (pid, hostname)
        super().__init__(msg)


class HostSlugMsidMappingExistsException(DomainException):
    """ A mapping keyed by hostname/slug already exists. """

    def __init__(self, host, slug, msid):
        """ Initialize. """
        msg = 'A host/slug mapping keyed by "%s" already exists, pointing to msid "%s".' % \
              (slug and '{}/{}'.format(host, slug) or host, msid)
        super().__init__(msg)


class InvalidUsernameException(DomainException):
    """ The username is invalid. """
    pass


class InvalidPasswordException(DomainException):
    """ The password is invalid. """
    pass


class LoginExistsException(DomainException):
    """ A login with a given username already exists for this partner. """

    def __init__(self, username):
        """ Initalize. """
        msg = 'A login with username "%s" already exists.' % username
        super().__init__(msg)


class InvalidSlugException(DomainException):
    """ The slug is invalid. """
    pass


class InvalidHostnameException(DomainException):
    """ The hostname is invalid. """
    pass


class SiteSpidExistsException(DomainException):
    """ The Site already has a spid. """

    def __init__(self, msid, spid):
        """ Initialize. """
        msg = 'Site {} is registered with an existing social profile {}.'.format(msid, spid)
        super().__init__(msg)


class SocialProfileNotFoundException(DomainException):
    """ The social profile was not found """
    pass


class TooManyNavigationItemsException(DomainException):
    """ More than the max number of navigation items was added. """
    pass


class InvalidNavigationSlugException(DomainException):
    """ The navigation_slug was invalid. """
    pass


class NavigationSlugExistsException(DomainException):
    """ The navigation slug was not unique for a microsite. """
    pass


class UnknownNavigationSlugException(DomainException):
    """ The requested navigation slug is not found. """
    pass


class UnknownNavigationPageidException(DomainException):
    """ The requested navigation pageid is not found. """
    pass


class NavigationSlugIsRequiredException(DomainException):
    """ The navigation slug cannot be ''. """
    pass


class MicrositeNotFoundException(DomainException):
    """ The requested microsite was not found. """
    pass


class PageNotFoundException(DomainException):
    """ The requested page was not found. """
    pass


class PostNotFoundException(DomainException):
    """ The requested page was not found. """
    pass


class TooManyPageidBlobMappingsException(DomainException):
    """ More than the max number of pageid blob mappings was added. """
    status_int = 403


class PageidBlobMappingExistsException(DomainException):
    """ The pageid blob mapping was not unique for a given page. """
    pass


class BatchImportExistsException(DomainException):
    """ A BatchImport entity exists for a given pid/pmsid. """
    pass


class AccountGroupInvalidException(DomainException):
    """ An error occurred involving an account group. """
    pass


class ProductConfigurationInvalidException(DomainException):
    """ Provided key is not in the available product configuration """
    status_int = 406


class NotFoundException(DomainException):
    """ Microsite is not found linked to a product """
    status_int = 404


class PartnerMicrositeNotFoundException(NotFoundException):
    """ Microsite is not found linked to a product """
    status_int = 404


class NoValidMarketHostException(DomainException):
    """ No valid hostname for a market could be found"""
    pass


class NoValidHostException(DomainException):
    """ No valid hostname could be found """
    pass


class UnableToAddFbPageException(DomainException):
    """ Unable to add facebook page to a social profile exception """
    status_int = 400


class UnableToAddTabException(DomainException):
    """ Unable to add more info tab to a facebook page """
    status_int = 400


class AccountGroupNotFoundException(DomainException):
    """ An account group does not exist when it was expected to. """


class DefaultHostPidMappingNotFoundException(DomainException):
    """ We tried to get the default domain for a partner but it wasn't found """

    def __init__(self, partner_id):
        """ Initialize. """
        msg = f'No default host for partner id {partner_id} exists.'
        super().__init__(msg)
