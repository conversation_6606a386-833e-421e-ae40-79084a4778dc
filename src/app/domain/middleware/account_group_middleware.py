""" Account Group Middleware. """
import logging
import re
import time

from webob import Request, Response

from google.appengine.ext import deferred
import vauth.foundational_products
import vauth_configuration
import vbcsdk
from app.domain.exceptions import MSAccountNotFoundException, MSAuthException, MSAccountMismatchException
from app.models.microsite import Account<PERSON>roupModel, Microsite
from app.models.partner import Partner
from coresdk_v2.base import AccountGroupClient
from settings import VBC_API_KEY, VBC_CONFIG, VBC_USER, VSOCIAL_USER, VSOCIAL_API
from google.appengine.api.taskqueue import TaskAlreadyExistsError, TombstonedTaskError

from app.constants import REFRESH_ACCOUNT_GROUP_QUEUE, LOCAL_SEO_PAID_EDITION_ID_DEMO, LOCAL_SEO_PAID_EDITION_ID_PROD

ONE_WEEK_IN_SECONDS = (7 * 24 * 60 * 60)


class AccountGroupRedirectMiddleware:
    """
    Verify request's host maps to a partner before running VAuth middleware.
    """
    REDIRECT_ROOTS = [
        '/edit/',
    ]

    ACCOUNT_ROOTS = [
        '/edit/account/'
    ]

    def __init__(self, app, base_app):
        """
        Initialization
        """
        self.app = app
        self.base_app = base_app

    def is_redirect_route(self, path):
        """
        if it's an old account path that needs the agid added on
        """
        return next((True for root in self.REDIRECT_ROOTS if path.startswith(root) and '/account/' not in path), False)

    def is_account_route(self, path):
        """
        if it's the new account path that needs the account group information on the request
        """
        return next((True for root in self.ACCOUNT_ROOTS if path.startswith(root)), False)

    def __call__(self, environ, start_response):
        """

        """
        request = Request(environ)
        if not self.is_redirect_route(request.path) and not self.is_account_route(request.path):
            response = request.get_response(self.app)
            return response(environ, start_response)

        microsite_account_id = vauth_configuration.get_valid_account_id()

        if not microsite_account_id:
            response = request.get_response(self.app)
            return response(environ, start_response)

        pid = vauth.foundational_products.get_partner_id()
        partner = Partner.get(pid)

        try:
            account_group_id = request.path.split('/')[3]
        except IndexError:
            account_group_id = ""

        if self.is_account_route(request.path) and account_group_id:
            logging.info("checking permissions for account group %s", account_group_id)

            try:
                account_group = self.get_validated_account_group(account_group_id)
            except MSAuthException as e:
                logging.warning("Authorization exception raised: %s", e.message)
                response = self._handle_auth_exception(account_group_id, partner.pid, request, request.GET.get('code'))
                return response(environ, start_response)
            except MSAccountNotFoundException as e:
                logging.warning('Account %s does not exist: %s', account_group_id, e.message)
                response = self.build_404_response()
                return response(environ, start_response)
            except MSAccountMismatchException:
                logging.warning('MSID %s does not match session', microsite_account_id)
                response = self.get_response_for_mismatch_exception(microsite_account_id, partner.pid, request.path_qs,
                                                                    request.body)
                return response(environ, start_response)

            request.account_group_id = account_group.account_group_id
            request.account_group = account_group

            response = request.get_response(self.app)
        else:
            logging.info('Old route used: %s, redirecting to new contextual route', request.path)

            microsite_account = Microsite.get_by_msid(microsite_account_id, pid=partner.pid)
            account_group_id = microsite_account.account_group.account_group_id
            response = self.get_response_for_account_redirect(account_group_id, request.path_qs, request.body)

        refresh_account_group(account_group_id)
        return response(environ, start_response)

    @property
    def jinja_client(self):
        """ Get the global jinja2 client """
        return jinja2.get_jinja2(app=self.base_app)

    def build_403_response(self):
        """ Build a 403 response """
        response = Response(status=403)
        response.write(self.jinja_client.render_template("html/error_handlers/static/403.html"))
        return response

    def build_404_response(self):
        """ Build a 404 response """
        response = Response(status=404)
        response.write(self.jinja_client.render_template("html/error_handlers/static/404.html"))
        return response

    def get_validated_account_group(self, account_group_id):
        """
          lookup the account group the user is trying to access and make sure they have access to it
          Currently compares the account group to the ms account id that is trying to be accessed.
        """
        account_group = AccountGroupModel.get(account_group_id)
        microsite_id = vauth_configuration.get_valid_account_id()

        if not account_group or not microsite_id:
            raise MSAccountNotFoundException(account_group_id)

        msid = account_group.msid
        if not msid:
            # possibly just activated and we haven't received the pubsub yet
            logging.info('Account group: %s doesnt have MS, checking VBC to be sure', account_group.msid)
            client = vbcsdk.AccountGroupClient(VBC_USER, VBC_API_KEY, configuration=VBC_CONFIG)
            account_group_dict = client.getAccountGroupV3(accountGroupId=account_group_id)
            msid = account_group_dict.get('productsJson', {}).get('MS', {}).get('msid', None)
            logging.info('VBC msid: %s found', msid)

        if msid != microsite_id:
            logging.info('VBC msid: %s still doesnt match session id %s', msid, microsite_id)
            raise MSAccountMismatchException()

        user_id = vauth.foundational_products.get_user_id()
        if user_id:
            if not vauth.foundational_products.user_can_access_account(user_id, account_group_id):
                logging.info("User %s does not have permission to access account %s", user_id, account_group_id)
                raise MSAuthException()

        return account_group

    def _handle_auth_exception(self, account_group_id, partner_id, request, auth_code):
        """ method for handling the MSAuthException """

        # If we find that the user is attempting to access something they don't have permission to, it could be that we
        # don't actually have the right user session. This can happen if we didn't receive a logout webhook.. like for
        # example, when you impersonate different users without truly logging out. In this case, we want to clear their
        # session and try another session transfer, but we only want to do that once, because sending a user back to
        # a login screen when they are already logged in would otherwise cause an infinite redirect.
        if not 'vauth-retrying-on-unauthorized' in request.cookies and not auth_code:
            logging.info("Redirecting user for session transfer.")

            vauth.foundational_products.flush_current_session()
            sso_token = vauth.foundational_products.get_sso_token()
            redirect_to = vauth.foundational_products.VAuthMiddleware.get_session_transfer_url(account_group_id,
                                                                                               partner_id, request,
                                                                                               sso_token)
            response = Response(status=302, location=redirect_to)
            response.set_cookie('vauth-retrying-on-unauthorized', max_age=60)
            return response
        else:
            logging.info("User is not authorized.")
            return self.build_403_response()

    def get_response_for_mismatch_exception(self, microsite_account_id, partner_id, path, body):
        """
        Try to redirect to the account_group found on the microsite account
        """
        account = Microsite.get_by_msid(microsite_account_id, partner_id)
        if not account:
            return self.build_404_response()

        new_path = re.sub(r'\/AG-\w+\/', '/' + account.account_group.account_group_id + '/', path)
        return Response(status=307, location=new_path, body=body)

    def get_response_for_account_redirect(self, account_group_id, path, body):
        """
        Forms the new account url to redirect to
        """
        if path == '/edit/account/':
            stripped_path = ''
        else:
            stripped_path = path.replace('/edit/', '')
        return Response(
            status=307,
            location=f'/edit/account/{account_group_id}/{stripped_path}',
            body=body
        )


def refresh_account_group(account_group_id):
    """
    refresh an account group (formerly known as "refresh profile") Will trigger a scrape.  Scraping will not
    occur immediately unless forceRefresh flag is set to true
    """
    defer_url = f'/_ah/queue/deferred/refresh_account_group/{account_group_id}'
    seconds = int(time.time())
    seconds_for_weekly_task = int(seconds / ONE_WEEK_IN_SECONDS)
    weekly_task_name = 'defer-refresh-account-group-%s-%d' % (account_group_id, seconds_for_weekly_task)
    try:
        deferred.defer(deferred_refresh_account_group, account_group_id,
                       _url=defer_url, _queue=REFRESH_ACCOUNT_GROUP_QUEUE, _name=weekly_task_name)
        logging.info("refresh_account_group task_name: %s", weekly_task_name)
    except (TaskAlreadyExistsError, TombstonedTaskError):
        logging.info("refresh_account_group task with name %s already exists.", weekly_task_name)
    except Exception:
        logging.warning("refresh_account_group task could not be started with name %s",
                        weekly_task_name)


def deferred_refresh_account_group(account_group_id):
    """
    Deferred task for refreshing account group
    """
    if should_refresh_account_group(account_group_id):
        logging.info("Start refreshing account group: %s", account_group_id)
        client = AccountGroupClient(VSOCIAL_USER, VSOCIAL_API)
        client.refreshAccountGroupV2(agid=account_group_id, forceFlag=None, snapshot=None)
    else:
        logging.info("Skipping refreshing account group: %s", account_group_id)


def should_refresh_account_group(account_group_id):
    """
    Check whether refreshing account group should happen or not
    Return True if LSP is activated or LocalSEO Pro is activated
    """
    account_group = AccountGroupModel.get(account_group_id)
    if not account_group:
        logging.info("Could not find account group %s", account_group_id)
        return False

    if account_group.listing_sync_pro and account_group.listing_sync_pro.is_active:
        return True

    partner_id = account_group.partner_id
    if not partner_id:
        logging.info("Could not find partner id for account group %s", account_group_id)
        return False

    microsite = Microsite.lookup_by_agid(account_group_id, partner_id)
    if not microsite:
        logging.info("Could not find microsite for account group %s", account_group_id)
        return False

    return microsite.product_edition in [LOCAL_SEO_PAID_EDITION_ID_DEMO, LOCAL_SEO_PAID_EDITION_ID_PROD]
