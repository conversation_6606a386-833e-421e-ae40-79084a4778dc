""" Custom Page Middleware """
from webob import Request


class CustomPageMiddleware:
    """
    The CustomPageMiddleware renders one-off, custom overrides of our application's routes for partners that have
    requested some unique functionality that the application wasn't built for.

    USE WITH CAUTION - hosting our customer's custom workflows in this manner should only be used for very simple
    workflows (for example, with DH1E it's rendering a simple landing page on the root path, of which the application's
    current logic does not allow when you are configured to use slugs).
    """

    def __init__(self, app):
        """ INIT """
        self.app = app

    @webapp2.cached_property
    def jinja2(self):
        """ Returns a Jinja2 renderer cached in the app registry. """
        return jinja2.get_jinja2(app=self.app)

    def render_template(self, template, **context):
        """ Returns the rendered template as a string. """
        return self.jinja2.render_template(template, **context)

    def __call__(self, environ, start_response):
        """ CALL """
        request = Request(environ)

        if request.host == 'listings.fortheweb.com' and request.path == '/':
            start_response('200 OK', [('Content-Type', 'text/html')])
            return [str(self.render_template('custom/DH1E.html'))]

        response = request.get_response(self.app)
        return response(environ, start_response)
