""" Middleware to add required Microsite object fields. """
from app.domain.url_mappings import lookup_hostslug_msid_mapping_for_msid
from google.appengine.api.namespace_manager import namespace_manager

class FacebookPidMsidSelectorMiddleware:
    """Adds the msid, pid, and slug information to the handler."""

    def before_dispatch(self, handler):
        """ Looks at the hostname to add the pid to the handler. """
        handler.pid = handler.request.route_kwargs['pid'].upper()
        handler.msid = handler.request.route_kwargs['msid'].upper()

        mapping = lookup_hostslug_msid_mapping_for_msid(handler.pid, handler.msid)
        if mapping:
            mapping = mapping[0]
            handler.host = mapping.host
            handler.slug = mapping.slug
            handler.uses_slugs = False

        namespace_manager.set_namespace(handler.pid)


