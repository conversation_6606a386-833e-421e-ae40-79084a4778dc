""" Middleware to select the PID using the hostname. """

from google.appengine.api.namespace_manager.namespace_manager import set_namespace
from app.domain.cache import HostPidMappingCache


class PidSelectorMiddleware:
    """ Adds the pid and uses_slugs to the handler, using the hostname. It also set the namespace
    in the App Engine namespace_manager.
    """
    
    def before_dispatch(self, handler):
        """ Set handler properties before dispatching request """
        self.set_handler_pid_context(handler)

    @classmethod
    def set_handler_pid_context(cls, handler):
        """ Looks at the hostname to add the pid to the handler. """
        pm = HostPidMappingCache().get(handler.request.host)
        if pm:
            handler.pid = pm.pid
            handler.uses_slugs = pm.uses_slugs
            set_namespace(pm.pid)
        else:
            handler.abort(404)
