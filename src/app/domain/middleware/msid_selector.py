""" Middleware to select the MSID from the url. """

import logging
import re

import vauth_configuration
from app.domain.url_mappings import get_host_and_slug_for_url, get_hostslug_for_url
from app.domain.microsite import get_microsite
from app.domain.cache import HostSlugMsidMappingCache

class MsidSelectorMiddleware:
    """ Adds the msid to the handler, using the hostname and optional slug. """

    def before_dispatch(self, handler):
        """ Looks at the hostname and path to add the msid to the handler. """
        handler_resp = self.retreive_and_set_microsite_data_on_handler(handler)
        if isinstance(handler_resp, Response):
            return handler_resp

        if not hasattr(handler, 'msid') or not hasattr(handler, 'slug'):
            handler.abort(404)

    @classmethod
    def get_redirect_url(cls, handler):
        """Determine if a redirect URL is available for this request"""
        if handler.request.host in {'sales-test.vendasta.com', 'sales.vendasta.com'}:
            return 'http://www.vendasta.com'
        return None

    @classmethod
    def build_redirect_url(cls, url, redirect_hostslug, uses_slugs=True):
        """ Build a redirection URL by replacing the host-slug """
        current_hostslug = get_hostslug_for_url(url, uses_slugs=uses_slugs)
        current_hostslug_pattern = re.compile(current_hostslug.replace("\\", "\\\\"), re.IGNORECASE)
        new_url = current_hostslug_pattern.sub(redirect_hostslug, url)
        return new_url

    @classmethod
    def retreive_and_set_microsite_data_on_handler(cls, handler):
        """retrieve and set microsite data onto handler """
        request = handler.request

        if not hasattr(handler, 'pid'):
            logging.error('handler does not have pid attribute. ' +
                                 'Likely the PidSelectorMiddleware is not installed in the pipeline.')
            handler.abort(404)

        if not hasattr(handler, 'uses_slugs'):
            logging.error('handler does not have pid attribute. ' +
                                 'Likely the PidSelectorMiddleware is not installed in the pipeline.')
            handler.abort(404)

        try:
            host, slug = get_host_and_slug_for_url(request.path_url, uses_slugs=handler.uses_slugs)
        except ValueError:
            # TODO: Don't special case Vendasta, handle the market context for 404 redirects (if set) [MS-2271]
            redirect_url = cls.get_redirect_url(handler)
            if redirect_url:
                return handler.redirect(redirect_url)
            else:
                raise

        # Bots and such ilk looking for a Word Press login page
        if slug == "wp-login.php":
            return Response(status=404)

        mapping_cache = HostSlugMsidMappingCache()
        hsmm = mapping_cache.get(host, slug=slug)
        if hsmm:
            # if the incoming pid doesn't match this pid, raise a 401 Unauthorized
            if hsmm.pid != handler.pid:
                logging.critical('Middleware pids do not match ("%s" and "%s") using mapping "%s". '
                                 'This is very likely because of a data problem where HostPidMapping is '
                                 'not in sync with HostSlugMsidMapping. This needs manual intervention.',
                                 handler.pid, hsmm.pid, handler.request.path_url)
                handler.abort(401)

            handler.msid = hsmm.msid
            handler.slug = hsmm.slug
            # if the hsmm has a redirect_hostslug, we need to redirect to it, preserving the rest of the url
            if hsmm.redirect_hostslug:
                response = Response()
                response.status_int = 301
                response.location = cls.build_redirect_url(
                    request.url, hsmm.redirect_hostslug, uses_slugs=handler.uses_slugs)
                logging.warning(response.location)
                return response
        return handler

class MsidArgumentRequiredMiddleware:
    """ selects a microsite for admin purposes  from a request param """

    def before_dispatch(self, handler):        
        """ Looks at the query argument msid and checks if the msid exists if so it attaches the msid and the
            microsite itself to the handler """

        msid = vauth_configuration.get_valid_account_id()

        if not msid:
            logging.warning('msid is not found')
            handler.abort(403)

        microsite = get_microsite(msid, pid=handler.pid)
        if microsite:
            handler.msid = microsite.msid
            handler.microsite = microsite
        else:
            logging.warning('Attempt to load a Microsite with the msid %s', msid)
            handler.abort(404)


class MicrositeRequiredMiddleware:
    """
    Ensures that a microsite exists on the request handler.
    """

    def before_dispatch(self, handler):
        """
        Returns a 404 if microsite does not exist.
        """
        if not handler.microsite:
            error_message = "An account associated with pid=%s and agid=%s does not exist.  If you are confident " \
                            "these values are correct please contact customer support for assistance."
            handler.abort(404, error_message % (handler.pid, handler.agid))
