""" Middleware to retrieve the Partner Whitelabel styles/assets using vconfig and set it to handler. """
import logging

import settings
import vconfig

from app.domain.vbc import get_vbc_pid
from app.models.market_mapping import MarketMapping
from vconfig.sdk import ApiException
from partner_sdk.api import BrandingV2Client


def get_partner_whitelabel_config(pid, market_id=None):
    """ Wrapper for vconfig.get_config to catch ApiException """
    try:
        return vconfig.ConfigurationClient(settings.ENVIRONMENT_NAME).get_config(pid, market_id=market_id)
    except ApiException as e:
        logging.warning(e.args[0])


class GetPartnerWhitelabelDataMiddleware:
    """ Adds the whitelabel data to the handler for the current partner """

    def before_dispatch(self, handler):
        """ Retrieves the whitelabel styles/assets from the magic WhitelabelStyles
            class and adds it to the current request handler. """
        pid = handler.pid
        microsite = getattr(handler, 'microsite', None)

        if microsite and microsite.market_id:
            market_id = microsite.market_id
        else:
            market_mapping = MarketMapping.get_by_host_and_partner_id(handler.request.host, pid)
            market_id = market_mapping.market_id if market_mapping else None
        vbc_pid = get_vbc_pid(pid)
        
        legacy_whitelabel_data = get_partner_whitelabel_config(vbc_pid, market_id=market_id)
        branding_data = BrandingV2Client.get(vbc_pid, market_id=market_id)

        self.add_legacy_whitelabel_data_to_handler(handler, legacy_whitelabel_data, branding_data)

    @classmethod
    def add_legacy_whitelabel_data_to_handler(cls, handler, legacy_whitelabel_data, branding_data):
        """ add the whitelabel data to the handler """
        if branding_data:
            handler.wl_logo = branding_data.logo_url
            handler.wl_favicon = branding_data.favicon_url
            handler.wl_mobile_shortcut = branding_data.shortcut_icon_url
            handler.wl_primary_color = str(branding_data.primary_color).strip('#')
            handler.wl_partner_name = branding_data.name

        if legacy_whitelabel_data:
            handler.wl_product_name = legacy_whitelabel_data.ms_name
            handler.wl_spgid = legacy_whitelabel_data.social_profile_group_id
            handler.wl_business_directory_product_name = legacy_whitelabel_data.business_directory_name
            handler.wl_exit_link_url = legacy_whitelabel_data.exit_link_url
            handler.wl_exit_link_text = legacy_whitelabel_data.exit_link_text
            handler.wl_ld_enabled = bool(legacy_whitelabel_data.ld_enabled)
            handler.wl_ld_enabled_for_account_users = bool(legacy_whitelabel_data.ld_enabled_for_account_user)
            handler.wl_ld_show_expiry = bool(legacy_whitelabel_data.ld_show_expiry)
            handler.wl_enable_location_page = bool(legacy_whitelabel_data.ms_location_page_enabled)
            handler.wl_enable_citations_page = bool(legacy_whitelabel_data.ms_citations_page_enabled)
            handler.wl_enable_overview_page = bool(legacy_whitelabel_data.ms_overview_page_enabled)
            handler.wl_overview_ld_display_type = legacy_whitelabel_data.ms_overview_ld_display_type
            handler.wl_review_generation_enabled = legacy_whitelabel_data.review_generation_enabled
            handler.wl_listing_sync_pro_superadmin_enabled = legacy_whitelabel_data.listing_sync_pro_superadmin_enabled
            handler.wl_listing_sync_pro_enabled = bool(legacy_whitelabel_data.listing_sync_pro_enabled)
            handler.wl_listing_sync_pro_sell_price_annual = legacy_whitelabel_data.listing_sync_pro_sell_price_annual
            handler.wl_listing_sync_pro_sell_price_monthly = legacy_whitelabel_data.listing_sync_pro_sell_price_monthly
            handler.wl_listing_sync_pro_show_sell_price = legacy_whitelabel_data.listing_sync_pro_show_sell_price
            handler.wl_listing_sync_pro_show_renew_price = legacy_whitelabel_data.listing_sync_pro_show_renew_price
            handler.whitelabel_data = legacy_whitelabel_data
