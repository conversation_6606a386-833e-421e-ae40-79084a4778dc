""" Provides some level of authentication to public ajax endpoints, ensures request came from microsite app """
from webapp2 import cached_property

import vapi
from app.domain.cache import HostPidMappingCache
from app.models.market_mapping import MarketMapping


#pylint: disable=abstract-method
class MicrositeDomainRequestMixin(vapi.ApiHandler):
    """ Make sure the request is coming from one of our apps. """

    REQUIRES_HTTPS = False

    ALLOWED_METHODS = frozenset(['GET'])

    @cached_property
    def partner_id(self):
        """ Cached partner_id, get from HostPidMappingCache """
        return HostPidMappingCache().get(self.request.host).pid

    @cached_property
    def market_id(self):
        """ Cached market_id, get from MarketMapping """
        market_mapping = MarketMapping.get_by_host_and_partner_id(self.request.host, self.partner_id)
        if market_mapping:
            return market_mapping.market_id
        else:
            return None

    def check_credentials(self, api_user, api_key):
        """ No auth for now """
        return True

    def validate_version(self):
        """ We do not need to validate the version for this endpoint. """
        return

    def validate_credentials(self):
        """ Make sure the request is coming from one of our apps. """
        return
