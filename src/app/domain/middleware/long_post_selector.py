"""
long form post selector middleware
"""
import logging
from app.domain.permalink import lookup_permalink_post_mapping
from app.domain.post import lookup_post_by_post_slug, lookup_post
from app.domain.utils import is_dev_appserver


class PostIDSelectorMiddleware:
    """ run through pid and msid selector middleware logic to set partner and microsite data on handler, then
    adds post id to handler.
    """

    def before_dispatch(self, handler):
        """ Looks at the hostname to add the pid to the handler. """
        if is_dev_appserver():
            port_index = len(handler.request.host) - (len(str(handler.request.server_port)) + 1)
            host = handler.request.host[:port_index]
            link = host + handler.request.path
        else:
            link = handler.request.host + handler.request.path
        link = link.rstrip('/')
        ppm = lookup_permalink_post_mapping(link)
        if ppm:
            logging.debug("Found PermalinkPostMapping for request url:%s", handler.request.url)
            logging.debug("Matching permalink:%s", link)
            if not isinstance(handler.uses_slugs, bool) or not handler.slug:
                handler.pid = ppm.pid
                handler.msid = ppm.msid
            handler.long_post = lookup_post(post_id=ppm.post_id, msid=ppm.msid, pid=ppm.pid)
        else:
            logging.debug("No matching PermalinkPostMapping found for %s", handler.request.url)
            post_slug = handler.request.route_kwargs.get('postslug')
            logging.debug("Lookup post by postslug: %s", post_slug)
            if not hasattr(handler, 'msid'):
                return handler.abort(404)
            handler.long_post = lookup_post_by_post_slug(post_slug, msid=handler.msid, pid=handler.pid)

        if not hasattr(handler, 'long_post'):
            return handler.abort(404)

        if not handler.long_post:
            return handler.abort(404)
