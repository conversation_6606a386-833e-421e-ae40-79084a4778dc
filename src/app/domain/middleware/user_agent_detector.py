""" Middleware to detect the correct profile based on user agent string. """

import logging
import re

from webapp2 import Response
from app.constants import OVERRIDE_PARAM, OVERRIDE_MOBILE_PARAM, OVERRIDE_COOKIE_EXPIRATION, \
    PR_DESKTOP, PR_TABLET, PR_PHONE, VALID_PROFILES, VALID_MOBILE_PROFILES, UA_REGEXES, UA_PROFILE_MAP


class UserAgentDetectorMiddleware:
    """ Adds User-Agent profile attrs `ua_profile` and `ua_mobile_profile`
        to the handler, which are used by the view layer to modify page
        appearance. The active profile can be overridden via GET URI parameters. """

    def before_dispatch(self, handler):
        """ Detects the correct User-Agent profile for the device that made the request. """
        request = handler.request
        handler.ua_profile = PR_DESKTOP

        profile = request.get(OVERRIDE_PARAM)
        profile_cookie = handler.request.cookies.get(OVERRIDE_PARAM)  # not MultiDict

        # check for overrides via GET param
        if profile in VALID_PROFILES:
            handler.ua_profile = profile
            response = Response(status=301)
            logging.info('Overriding User-Agent profile %s=%s via GET parameter.',
                         OVERRIDE_PARAM, profile)
            mobile_profile = request.get(OVERRIDE_MOBILE_PARAM)
            # don't need to override mobile profile if desktop profile is selected
            if handler.ua_profile != PR_DESKTOP and mobile_profile in VALID_MOBILE_PROFILES:
                logging.info(
                    'Overriding User-Agent mobile profile %s=%s via GET parameter.',
                    OVERRIDE_MOBILE_PARAM, mobile_profile
                )
                handler.ua_mobile_profile = mobile_profile
                response.set_cookie(OVERRIDE_MOBILE_PARAM, mobile_profile,
                                    max_age=OVERRIDE_COOKIE_EXPIRATION)

            response.set_cookie(OVERRIDE_PARAM, profile, max_age=OVERRIDE_COOKIE_EXPIRATION)

            # need to remove these GET params so that the redirect doesn't break widget iframe
            request.GET.pop(OVERRIDE_PARAM, None)
            request.GET.pop(OVERRIDE_MOBILE_PARAM, None)

            response.location = handler.request.url

            return response

        # check for overrides via cookies
        elif profile_cookie in VALID_PROFILES:
            handler.ua_profile = profile_cookie
            logging.info(
                "Overriding User-Agent profile %s=%s via cookie.",
                OVERRIDE_PARAM, handler.ua_profile)
            mobile_profile = handler.request.cookies.get(OVERRIDE_MOBILE_PARAM)
            # don't need to override mobile profile if desktop profile is selected
            if handler.ua_profile != PR_DESKTOP and mobile_profile in VALID_MOBILE_PROFILES:
                handler.ua_mobile_profile = mobile_profile
                logging.info("Overriding User-Agent mobile profile %s=%s via cookie.",
                             OVERRIDE_MOBILE_PARAM, mobile_profile)

        # no overrides are active
        else:
            user_agent = request.user_agent
            profile = mobile_profile = None
            if user_agent and isinstance(user_agent, str):
                for regex in UA_REGEXES:
                    if re.search(regex, user_agent):
                        profile, mobile_profile = UA_PROFILE_MAP[regex]
                        break
            else:
                logging.info("user_agent is not a string. \nuser_agent: %s \ntype of user_agent: %s", user_agent,
                             type(user_agent))
            if not profile:
                logging.info("No appropriate ua_profile could be detected from User-Agent" +
                             " header '%s'. Defaulting to desktop profile.", user_agent)
                handler.ua_profile = PR_DESKTOP
                handler.ua_mobile_profile = None
            else:
                handler.ua_profile = profile
                handler.ua_mobile_profile = mobile_profile

            # Changing all tablet requests to phone requests until dedicated Tablet Styles
            if handler.ua_profile == PR_TABLET:
                handler.ua_profile = PR_PHONE

    def after_dispatch(self, handler, response):
        """ Updates existing override cookies if they exist. """
        request = handler.request
        profile = request.get(OVERRIDE_PARAM)
        if profile in VALID_PROFILES:
            response.set_cookie(OVERRIDE_PARAM, profile, max_age=OVERRIDE_COOKIE_EXPIRATION)
            mobile_profile = request.get(OVERRIDE_MOBILE_PARAM)
            if mobile_profile in VALID_MOBILE_PROFILES:
                response.set_cookie(OVERRIDE_MOBILE_PARAM, mobile_profile,
                                    max_age=OVERRIDE_COOKIE_EXPIRATION)
        return response
