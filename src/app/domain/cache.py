""" Implements a caching layer over the domain. """

import logging
from google.appengine.api import memcache as google_memcache
from settings import ENABLE_CACHE, STATIC_VERSION_NUMBER
from app.domain.constants import host_pid_mapping_expire_signal, \
    host_slug_msid_mapping_expire_signal, post_permalink_mapping_expire_signal
from app.domain.post import lookup_post
from app.domain.url_mappings import get_host_pid_mapping, get_hostslug_msid_mapping

CACHE_NAMESPACE = 'cache'


class AbstractCache:
    """ An abstract parent class for caches. 
    
    NOTE: cached items are always stored in a common namespace (because expiration, etc. needs to work even if not
    in a partner namespace, e.g., batch import), so it is important to ensure that the cache key is globally unique.
    """
    
    def __init__(self, time=0, memcache=None, cache_enabled=None):
        """ Initialize. """
        self.hits = 0
        self.misses = 0
        self.reset_stats()
        self.memcache = memcache or google_memcache
        self.time = time
        if cache_enabled is None:
            self.enabled = ENABLE_CACHE
        else:
            self.enabled = cache_enabled
        
    def __build_mkey(self, key):
        """ Builds a version-specific key, so that memcache entries expire on new releases. """
        return '{}:{}'.format(key, STATIC_VERSION_NUMBER)
        
    def _get(self, key):
        """ Gets the entity from cache. If not in cache, retrieves it and cache it. """
        if not self.enabled:
            return self._get_entity(key)
            
        mkey = self.__build_mkey(key)
        entity = self.memcache.get(mkey, namespace=CACHE_NAMESPACE)
        if not entity:
            logging.debug('Cache miss for key "%s".', key)
            self.misses += 1
            entity = self._get_entity(key)
            self.memcache.set(mkey, entity, time=self.time, namespace=CACHE_NAMESPACE)
        else:
            logging.debug('Cache hit for key "%s".', key)
            self.hits += 1
        return entity
    
    # W0613: AbstractCache._get_entity: Unused argument 'key'
    # part of abstract interface
    def _get_entity(self, key):  # pylint: disable=W0613
        """ Abstract method. Retrieves the non-cached entity. """
        raise NotImplementedError()
        
    def _delete(self, key):
        """ Deletes the entity from cache. """
        if not self.enabled:
            return True
            
        logging.debug('Deleting key "%s" from cache.', key)
        mkey = self.__build_mkey(key)
        return self.memcache.delete(mkey, namespace=CACHE_NAMESPACE)
        
    def reset_stats(self):
        """ Resets the stats """
        self.hits = 0
        self.misses = 0


class SimpleKeyAbstractCache(AbstractCache):
    """ An abstract cache for caches that have only a single, string key. """

    def _build_key(self, key_name):
        """ Builds a key. """
        return self.KEY_ROOT + key_name
        
    def _get_key_name_from_key(self, key):
        """ Returns the key_name from the key. """
        return key[len(self.KEY_ROOT):]
    
    # W0613: SimpleKeyAbstractCache._get_entity: Unused argument 'key'
    # part of abstract interface
    def _get_entity(self, key):  # pylint: disable=W0613
        """ Abstract method. Retrieves the non-cached entity. """
        raise NotImplementedError()
        
    def get(self, key_name):
        """ Gets the entity from the cache. """
        return self._get(self._build_key(key_name))
        
    def delete(self, key_name):
        """ Deletes the key_name from cache. """
        return self._delete(self._build_key(key_name))
        
    @classmethod
    def expiration_listener(cls, key_name):
        """ Deletes the key_name from cache. 
        
        The expected message is the key_name, e.g., MSID, PID, or hostname.
        """
        return cls().delete(key_name)


class HostPidMappingCache(SimpleKeyAbstractCache):
    """ A cache for HostPidMapping objects. """
    
    KEY_ROOT = 'app.domain.cache.HostPidMappingCache:'
    
    def _get_entity(self, key):
        """ Gets the HostPidMapping. """
        hostname = self._get_key_name_from_key(key)
        return get_host_pid_mapping(hostname)


class HostSlugMsidMappingCache(AbstractCache):
    """ A cache for HostSlugMsidMapping objects. """
    
    KEY_ROOT = 'app.domain.cache.HostSlugMsidMapping:'
    
    def _build_key(self, host, slug=None):
        """ Builds a cache key. """
        slug = slug or ''
        return '{}{}:{}'.format(self.KEY_ROOT, host, slug)
    
    def get(self, host, slug=None):
        """ Gets a HostSlugMsidMapping. """
        key = self._build_key(host, slug=slug)
        return self._get(key)
        
    def delete(self, host, slug=None):
        """ Deletes a HostSlugMsidMapping from the cache. """
        key = self._build_key(host, slug=slug)
        return self._delete(key)
        
    def _get_host_slug_from_key(self, key):
        """ Parses the host and slug from the key. """
        (_, host, slug) = key.split(':', 2)
        if not slug:
            slug = None
        return host, slug,
        
    def _get_entity(self, key):
        """ Gets a HostSlugMsidMapping from datastore. """
        (host, slug) = self._get_host_slug_from_key(key)
        return get_hostslug_msid_mapping(host, slug=slug)

    @classmethod
    def expiration_listener(cls, host_and_slug):
        """ Deletes the key_name from cache. 
        
        The expected message is "[host]:[slug]", e.g., "www.partner.com:joes-plumbing".
        If the slug is null (i.e., for a custom domain), the expected message is
        "[host]:", e.g., "www.roses-flowers.com:". THE TRAILING COLON IS EXPECTED/REQUIRED!
        """
        (host, slug) = host_and_slug.split(':')
        if not slug:
            slug = None
        return cls().delete(host, slug=slug)


class PostPermalinkMappingCache(AbstractCache):
    """ A cache for PostPermalinkMapping objects. """
    
    KEY_ROOT = 'app.domain.cache.PostPermalinkMapping:'
    
    def _build_key(self, post_id, msid, pid):
        """ Builds a cache key. """
        return '{}{}:{}:{}'.format(self.KEY_ROOT, post_id, msid, pid)
    
    def get(self, post_id, msid, pid):
        """ Gets a PostPermalinkMapping mapping. """
        key = self._build_key(post_id, msid, pid)
        return self._get(key)
        
    def delete(self, post_id, msid, pid):
        """ Deletes a PostPermalinkMapping from the cache. """
        key = self._build_key(post_id, msid, pid)
        return self._delete(key)
        
    def _get_post_id_msid_pid_from_key(self, key):
        """ Parses the post_id, msid, pid from the key. """
        (_, post_id, msid, pid) = key.split(':')
        return post_id, msid, pid,
        
    def _get_entity(self, key):
        """ Gets a permalink from datastore. """
        (post_id, msid, pid) = self._get_post_id_msid_pid_from_key(key)
        permalink = lookup_post(post_id, msid, pid=pid).permalink
        return permalink

    @classmethod
    def expiration_listener(cls, key):
        """ Deletes the key_name from cache. """
        (post_id, msid, pid) = key.split(':')
        return cls().delete(post_id, msid, pid)

    

# hook up the expiration signal listeners
host_pid_mapping_expire_signal.connect(HostPidMappingCache.expiration_listener)
host_slug_msid_mapping_expire_signal.connect(HostSlugMsidMappingCache.expiration_listener)
post_permalink_mapping_expire_signal.connect(PostPermalinkMappingCache.expiration_listener)
