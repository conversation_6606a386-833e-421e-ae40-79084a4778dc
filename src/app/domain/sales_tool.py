""" methods for interacting with sales tool."""
import json

import settings
import logging
from salestoolsdk import SalespersonClient
from settings import ST_API_USER, ST_API_KEY
from vautil.phonenumber import format_phone_number, InvalidPhoneNumberException


def get_sales_person_details(account_group_id):
    """ get sales person details """
    client = SalespersonClient(ST_API_USER, ST_API_KEY, configuration=settings.ENVIRONMENT_NAME.lower())
    sales_person_dict = client.getSalesPersonV2(accountGroupId=account_group_id)
    sales_person_details = {
        'salesPerson': '',
        'agid': account_group_id
    }
    sales_person = sales_person_dict.get(account_group_id)
    if sales_person:
        sales_person_details.update({
            'salesPerson': '{} {}'.format(sales_person['firstName'], sales_person['lastName']),
            'email': sales_person['email'],
            'phoneNumber': format_phone_numbers(sales_person['phoneNumber'], sales_person.get('country')),
            'salesPersonImage': sales_person['photoUrlSecure'],
        })
    return json.dumps(sales_person_details)


def format_phone_numbers(phone_number_list, country):
    """ format phone numbers for display """
    result = []
    for number in phone_number_list:
        if number:
            try:
                formatted_phone_number = format_phone_number(number, country)
            except InvalidPhoneNumberException:
                if number != '0':
                    logging.warning('%s is not a valid phone number', number)
            else:
                result.append(formatted_phone_number)
    return result
