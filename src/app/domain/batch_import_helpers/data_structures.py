""" Data structures for batch import. """

import json
from werkzeug.datastructures import ImmutableMultiDict
from app.constants import VALID_SLUG as VALID_MICROSITE_SLUG, \
    VALID_SLUG_REGEX_STRING as VALID_MICROSITE_SLUG_REGEX_STRING, VALID_NAVIGATION_SLUG, \
    VALID_NAVIGATION_SLUG_REGEX_STRING, TEMPLATES, CUSTOM, ATTRIBUTES as att
from app.domain.hours_of_operation import HoursOfOperation

# NB: DO NOT use the "real" API_KEY because it implies you are running on App Engine, something that
# DirectWest batch import does not do.
class API_KEY:
    """ local version of API_KEY to avoid App Engine imports. """
    AGID = 'agid'
    ACCOUNT_GROUP_ID = 'accountGroupId'


REQUIRED_MICROSITE_ATTRIBUTES = (att.SLUG,)
MICROSITE_ATTRIBUTES = (
    att.PARTNER_IDENTIFIER,
    att.SLUG,
    att.SPID,
    att.AGID,
    att.SSO_TOKEN,
    att.CUSTOMER_IDENTIFIER,
    att.NAME,
    att.BLURB,
    att.PHONE,
    att.PHONE_WORK,
    att.PHONE_CALL_TRACKING,
    att.EMAIL,
    att.WEBSITE,
    att.ADDRESS1,
    att.ADDRESS2,
    att.CITY,
    att.STATE,
    att.ZIPCODE,
    att.COUNTRY,
    att.PLACE,
    att.LATITUDE,
    att.LONGITUDE,
    att.TWITTER_URL,
    att.FACEBOOK_URL,
    att.RSS_URL,
    att.YOUTUBE_URL,
    att.LINKEDIN_URL,
    att.INSTAGRAM_URL,
    att.PINTEREST_URL,
    att.FOURSQUARE_URL,
    att.GOOGLEPLUS_URL,
    att.LOGO_URL,
    att.DESKTOP_BACKGROUND_URL,
    att.MOBILE_BACKGROUND_URL,
    att.HOURS, att.COLOR,
    att.PAGES, att.ANNOUNCEMENT,
    att.CATEGORIES,
    att.LAYOUT,
    att.MARKET_ID,
    att.TAX_ID)

REQUIRED_PAGE_ATTRIBUTES = (att.SLUG, att.TEMPLATE, att.TAB_NAME, att.HEADING1, att.TITLE)
PAGE_ATTRIBUTES = (
    att.SLUG,
    att.TEMPLATE,
    att.TAB_NAME,
    att.HEADING1,
    att.TITLE,
    att.META_KEYWORDS,
    att.META_DESCRIPTION,
    att.TOP_CONTENT,
    att.BOTTOM_CONTENT,
    att.EMAIL,
    att.IMAGES,
    att.MOBILE_GALLERY_STYLE,
    att.DESKTOP_GALLERY_STYLE,
    att.VIDEOS,
    att.COUPONS)


class AbstractBatch(dict):
    """ Allows attributes to be specified dynamically. """
    
    # values are for unit tests only
    ATTRIBUTES = ('a', 'b')
    REQUIRED_ATTRIBUTES = ('a')
    
    def __getattr__(self, name, default_value=None):
        """ Gets a possibly dynamic attribute. """

        if name not in self.ATTRIBUTES:
            raise AttributeError(name)

        return self.get(name, default_value)
        
    def __setattr__(self, name, value):
        """ Sets a possibly dynamic attribute. """
        
        if name not in self.ATTRIBUTES:
            # public api allows accountGroupId in place of agid. Internally we just want agid
            # this sets the agid to the accountGroupId value
            if name is API_KEY.ACCOUNT_GROUP_ID:
                setattr(self, API_KEY.AGID, value)
            return  # Ignore invalid attributes
            
        # validate the value, unless it's being cleared (set to None)
        if value is not None:
            validation_method = '_validate_%s' % name
            if hasattr(self, validation_method):
                getattr(self, validation_method)(value)
            
        if isinstance(value, str):
            value = value.strip()
            
        self[name] = value
        
    def validate(self):
        """ Validates that the object is valid. """
        # ensure required values have been specified
        for key in self.REQUIRED_ATTRIBUTES:
            if key not in self or self[key] is None:
                raise InvalidBatchSpecificationException('Key "%s" must be specified.' % key)
        
    def serialize(self):
        """ Returns a serialized form. """
        self.validate()
        return json.dumps(self)
        
    @classmethod
    def deserialize(cls, serialized):
        """ Inflates from a serialized form. """
        if not serialized:
            raise ValueError('serialized is required.')
        try:
            value_dict = json.loads(serialized)
        except ValueError as e:
            raise InvalidBatchSpecificationException('Invalid JSON: %s' % e.args[0])
        if not isinstance(value_dict, dict):
            raise InvalidBatchSpecificationException('JSON must be a dictionary.')
        obj = cls.from_dict(value_dict)
        obj.validate()
        return obj
        
    @classmethod
    def from_dict(cls, value_dict):
        """ Inflates from a dictionary of values. """
        obj = cls()
        for key, value in value_dict.items():
            setattr(obj, key, value)
        return obj
        
    @property
    def formdata(self):
        """ Returns self as a multidict-type wrapper supporting getlist method. """
        return ImmutableMultiDict(mapping=self)


class MicrositeBatch(AbstractBatch):
    """ A microsite specification suitable for batch import. """
    
    ATTRIBUTES = MICROSITE_ATTRIBUTES
    REQUIRED_ATTRIBUTES = REQUIRED_MICROSITE_ATTRIBUTES
    
    def __init__(self):
        """ Initialize an empty batch. """
        super().__init__()
        self.pages = []
        self.hours = HoursOfOperation()
        
    def add_page(self, page):
        """ Adds a new page to the microsite, ensuring that the slug is unique. """
        if not page:
            raise ValueError('page is required')
        for existing_page in self.pages:
            if existing_page.slug == page.slug:
                raise InvalidBatchSpecificationException('Page slugs must be unique.')
        self.pages.append(page)
        
    def get_page(self, slug):
        """ Returns the page with the given slug if present, None otherwise. """
        for page in self.pages:
            if page.slug == slug:
                return page
        return None
    
    def _validate_slug(self, value):
        """ Validates the microsite slug. """
        if not VALID_MICROSITE_SLUG.match(value):
            raise InvalidBatchSpecificationException('Slug "%s" is invalid. "%s" expected.' % \
                                                     (value, VALID_MICROSITE_SLUG_REGEX_STRING))
                                                     
    def _validate_categories(self, value):
        """ Validates the microsite categories. """
        if not value:
            return
        if not isinstance(value, list):
            raise InvalidBatchSpecificationException('Categories "%s" is invalid. A list is expected.' % value)
        for item in value:
            if not isinstance(item, str):
                raise InvalidBatchSpecificationException('Categories must be a list of strings only.')
    
    def serialize(self):
        """ Serialize the microsite after ensuring that there is at least one page with empty string slug. """
        
        hoo = self.hours # keep the hoo object around
        try:
            # convert the hoo to something that will serialize
            self.hours = self.hours.to_dict()
            return super().serialize()
        finally:
            self.hours = hoo
        
    @classmethod
    def deserialize(cls, serialized):
        """ After parent class deserialization, created the MicrositeBatchPage objects on pages attribute. """
        ms = super().deserialize(serialized)
        
        page_dicts = ms.pages # parent deserialization just jammed the dictionaries on the attribute
        ms.pages = []
        for page_dict in page_dicts:
            if not isinstance(page_dict, dict):
                page_dict = json.loads(page_dict)
            page = MicrositeBatchPage.from_dict(page_dict)
            page.validate()
            ms.pages.append(page)
            
        # convert dictionary hours to a formal HoursOfOperation object
        if ms.hours and not isinstance(ms.hours, HoursOfOperation):
            ms.hours = HoursOfOperation(values_dict=ms.hours)
            
        return ms
        
    def validate(self):
        """ Validate. """
        super().validate()

        # check that there are some pages
        if not self.pages:
            raise InvalidBatchSpecificationException('At least one page must be specified.')

        # check that all slugs are unique
        used_slugs = set()
        for page in self.pages:
            if not isinstance(page, dict):
                page = json.loads(page)
            if att.SLUG in page:
                if page[att.SLUG] in used_slugs:
                    raise InvalidBatchSpecificationException(
                        'Slugs must be unique. Found duplicate "%s".' % page[att.SLUG])
                used_slugs.add(page[att.SLUG])


class MicrositeBatchPage(AbstractBatch):
    """ A page for a microsite. """

    ATTRIBUTES = PAGE_ATTRIBUTES
    REQUIRED_ATTRIBUTES = REQUIRED_PAGE_ATTRIBUTES
    
    def __init__(self):
        """ Initialize """
        super().__init__()
        self.template = CUSTOM
    
    def _validate_slug(self, value):
        """ Validates the page navigation slug. """
        if not VALID_NAVIGATION_SLUG.match(value):
            raise InvalidBatchSpecificationException('Slug "%s" is invalid. "%s" expected.' % \
                                                     (value, VALID_NAVIGATION_SLUG_REGEX_STRING))

    def _validate_template(self, value):
        """ Validates the template. """
        if value not in TEMPLATES:
            raise InvalidBatchSpecificationException('Template "%s" is invalid. Must be in %s.' % \
                                                     (value, TEMPLATES))


class BatchImportException(Exception):
    """ Abstract parent for all batch_import exceptions. """


class InvalidBatchSpecificationException(BatchImportException):
    """ The batch object is invalid. """
    pass
