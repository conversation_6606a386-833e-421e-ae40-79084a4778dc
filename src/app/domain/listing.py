"""
Listing domain module
"""

import logging
from datetime import datetime


from marketplace_packages_sdk import MarketplacePackagesAPI, PartnerAppRequest, AppEnablementStatus

from coresdk_v2.base import NotFound404Error as CoreNotFound, InternalServer500Error
from app.constants import DATE_DISPLAY_FORMAT, ISO_DATETIME_FORMAT, \
    ISO_DATE_FORMAT, DATE_DISPLAY_FORMAT_FOR_PARSE
from app.domain.core.listing import lookup_listing_history_deltas

from listing_products_sdk.api import ListingProductsClient

from listing_products_sdk._generated.listing_products.v1.addon_attributes_pb2 import ADDON_TYPE_LD

from vax.utils.env import env_from_app_engine_env
from vax.errors import UninitializedException

CITATIONS_ALL_BY_SRID_DAILY = 'citations-all-by-srid-daily'


def is_partner_using_marketplace_for_ld(partner_id):
    """
    Checks whether the passed in account groups partner is using the marketplace for selling listing distribution.

    You can check this in Partner Center by going into Marketplace > Manage Products > My Products and searching for
    Listing Distribution.

    :param partner_id: The partners id
    :return: True if using LD in the marketplace, false otherwise
    """
    env = env_from_app_engine_env()

    try:
        ListingProductsClient.get_environment()
    except UninitializedException:
        ListingProductsClient.set_environment(env)

    partnerAppRequestList = []
    response = ListingProductsClient.get_multi_addon_attributes(None, [ADDON_TYPE_LD], None)
    for addon in response.addon_attributes:
        tempRequest = PartnerAppRequest(partner_id=partner_id, app_id=addon.key)
        partnerAppRequestList.append(tempRequest.to_proto())

    marketplace_response = MarketplacePackagesAPI.get_multi_partner_app_enablement_status(partnerAppRequestList)
    for response in marketplace_response.partner_app_response:
        if response.status == AppEnablementStatus.ENABLED:
            return True
    return False


def get_listing_history_category_changes(lid, activated_date_string=None):
    """ Returns listing history for account group id and listing id """
    try:
        result = lookup_listing_history_deltas(lid)
    except (CoreNotFound, InternalServer500Error) as error:
        logging.exception(error.args[0])
        result = {}

    for history_items in result.values():
        if not len(history_items):
            continue

        for history_item in history_items:
            created_datetime = datetime.strptime(history_item['created'], ISO_DATETIME_FORMAT)
            history_item['created'] = created_datetime.strftime(DATE_DISPLAY_FORMAT)

        if activated_date_string:
            activated_datetime_object = datetime.strptime(activated_date_string, ISO_DATE_FORMAT)
            history_created = datetime.strptime(history_items[-1]['created'], DATE_DISPLAY_FORMAT_FOR_PARSE)
            if history_created > activated_datetime_object:
                formatted_activated_date = activated_datetime_object.strftime(DATE_DISPLAY_FORMAT)
                history_items.append({'created': formatted_activated_date, 'details': 'Not Found', 'matchFlag': None})

    return result

