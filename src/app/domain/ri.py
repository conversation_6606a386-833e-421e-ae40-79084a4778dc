""" Rep Intel sdk wrapper. """
from srsdk import AccountClient
from app.domain.validation import require_args
import settings


def _get_account_client():
    """
    :return: The Rep Intel account sdk client.
    """
    return AccountClient(settings.RI_API_USER, settings.RI_API_KEY, configuration=settings.ENVIRONMENT_NAME.lower())


@require_args
def get_account_async(srid):
    """
    :return: The Rep Intel account for the provided srid.
    """
    client = _get_account_client()
    return client.getV2Async(srid=srid)
