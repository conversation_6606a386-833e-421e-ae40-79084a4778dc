"""
Until we get all partners moved into Marketplace for LSP instead of old- / service-style LSP,
we need to make sure the whitelists and blacklists in Partner Center match the hard-coded list in `account-group`.
The white/blacklists in PC determine who can sell marketplace LSP.
The list in AG determines if they get billed for old-style LSP:
    https://github.com/vendasta/account-group/blob/536c4deda4c1c4b20ad2ea161/internal/pubsub/listing_sync_pro.go#L20-L82
Helpful Venn-ish diagram / notes:
    https://jamboard.google.com/d/1vEgN3P26MYT0vyqHUf6Aky8wnS4HC7MvuUDPypivoo4/viewer?f=0
"""

from aasdk import CatalogClient
import settings

from app.domain.slack_notify import notify_listings_team


# This *could* be grabbed dynamically by Github API, but that's a big enough security risk, to leave this hard-coded.
LSP_DISCOUNT_LISTS = [
    (('uberall us yearly', 'A-TMPJGS28X7'), ('uberall us discount yearly', 'A-W52RXJFPSL')),
    (('uberall us monthly', 'A-ZR7M2V6TCD'), ('uberall us discount monthly', 'A-P6V3G584DR')),
    (('uberall ca yearly', 'A-2BRLL3FH4K'), ('uberall ca discount yearly', 'A-TD5HVH6LHM')),
    (('uberall ca monthly', 'A-C8GSD6X4D2'), ('uberall ca discount monthly', 'A-Z6S5D3QGHF')),
]
LSP_BLACKLISTED_NOT_RELATED_TO_DISCOUNT = [
    ('uberall aus yearly', 'A-XQL2HMD6VV'),
    ('uberall de yearly', 'A-W2GGC7TJW3'),
    ('uberall fr yearly', 'A-C6CNBPXPCC'),
    ('uberall it yearly', 'A-KK3MNLFDP5'),
    ('uberall uk yearly', 'A-WCH8K4S8LS'),
    ('yext us yearly', 'A-WNW446NCNS'),
    ('yext us monthly', 'A-8PHKXVRZFS'),
]
LSP_WHITELISTED_MONTHLY_NOT_RELATED_TO_DISCOUNT = [
    ('uberall ca monthly', 'A-C8GSD6X4D2'),
    ('uberall aus monthly', 'A-P3XFBZ6HCC'),
    ('uberall de monthly', 'A-JZGCTTMHLG'),
    ('uberall fr monthly', 'A-VM8PJZ8PZ5'),
    ('uberall it monthly', 'A-CDFBK8XW2W'),
    ('uberall uk monthly', 'A-FR72RDNMP6'),
]
PARTNERS_WHO_CAN_SEE_BOTH_DISCOUNT_AND_NORMAL = {
    'GHM'
}


def _permissions_for_app(addon_id):
    """
    Args:
        addon_id (string):

    Returns:
        blacklist (set<string>): The partner IDs that have been blacklisted for this app.
        whitelist (set<string>): The partner IDs that have been whitelisted for this app.
    """
    client = CatalogClient(apiUser=settings.AA_API_USER, apiKey=settings.AA_API_KEY)
    response = client.getMarketplaceAppPartnerAccessPermissionsV3(app_id=addon_id)
    whitelist = set(response.get('whitelisted_partner_ids', []))
    blacklist = set(response.get('blacklisted_partner_ids', []))
    if not whitelist and not blacklist:
        message = f'LSP distribution check could not get permissions for addon {addon_id}.'
        notify_listings_team(message)
        raise Exception(message)
    return blacklist, whitelist


def _dont_fail_to_bill_anyone():
    """ See name. """
    partners_on_any_whitelist = set()
    for (_, addon_id) in \
            [discount_needs_whitelist for (_, discount_needs_whitelist) in LSP_DISCOUNT_LISTS] + \
            LSP_WHITELISTED_MONTHLY_NOT_RELATED_TO_DISCOUNT:
        _, whitelist = _permissions_for_app(addon_id)
        partners_on_any_whitelist = partners_on_any_whitelist | whitelist

    partners_on_all_blacklists = None
    for (_, addon_id) in \
            [normal_needs_blacklist for (normal_needs_blacklist, _) in LSP_DISCOUNT_LISTS] + \
            LSP_BLACKLISTED_NOT_RELATED_TO_DISCOUNT:
        blacklist, whitelist = _permissions_for_app(addon_id)
        if partners_on_all_blacklists is None:
            partners_on_all_blacklists = blacklist
        else:
            partners_on_all_blacklists = partners_on_all_blacklists & blacklist

    cannot_sell_marketplace = partners_on_all_blacklists - partners_on_any_whitelist

    not_getting_billed = cannot_sell_marketplace
    if len(not_getting_billed) > 0:
        notify_listings_team(f"These partners aren't getting billed for old-style LSP! {not_getting_billed}")


def _partners_who_sell_discount_shouldnt_see_normal():
    """ See name. """
    for ((normal_name, normal_id), (discount_name, discount_id)) in LSP_DISCOUNT_LISTS:
        _, discount_whitelist = _permissions_for_app(discount_id)
        normal_blacklist, _ = _permissions_for_app(normal_id)
        partners_who_see_both_but_shouldnt = \
            discount_whitelist - normal_blacklist - PARTNERS_WHO_CAN_SEE_BOTH_DISCOUNT_AND_NORMAL
        if len(partners_who_see_both_but_shouldnt) > 0:
            notify_listings_team(
                "These partners can see both {} and {}: {} . Blacklist them from {}.)"
                .format(normal_name, discount_name, partners_who_see_both_but_shouldnt, normal_name)
            )


def distribution_lists_are_in_sync():
    """ See module docstring. """
    for (lsp_name, addon_id) in \
            [discount for (normal, discount) in LSP_DISCOUNT_LISTS] + \
            LSP_WHITELISTED_MONTHLY_NOT_RELATED_TO_DISCOUNT:
        blacklist, _ = _permissions_for_app(addon_id)

        if len(blacklist) > 0:
            notify_listings_team(f'Had blacklist on whitelist-LSP {lsp_name} ({addon_id})!')

    for (lsp_name, addon_id) in \
            LSP_BLACKLISTED_NOT_RELATED_TO_DISCOUNT + \
            [normal for (normal, discount) in LSP_DISCOUNT_LISTS]:
        _, whitelist = _permissions_for_app(addon_id)

        if len(whitelist) > 0:
            notify_listings_team(f'Had whitelist on blacklist-LSP {lsp_name} ({addon_id})!')

    _dont_fail_to_bill_anyone()

    _partners_who_sell_discount_shouldnt_see_normal()
