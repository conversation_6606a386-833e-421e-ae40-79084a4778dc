""" Functions and classes related to microsite pages. """
import json
from vautil import tinyid
import urllib.parse
from datetime import datetime
from google.appengine.api.namespace_manager.namespace_manager import get_namespace
from werkzeug.utils import import_string

from app.domain import exceptions
from app.domain.pageid_blob_mapping import get_pageid_blob_mappings
from app.models.page import Page as PageModel, CUSTOM, TEMPLATES
from app.models.remote import YouTubeRemoteApi

ERROR_404 = 'ERROR-404'


def create_page_entity(**kwargs):
    """ Creates a new page entity. If 'template' is not in the kwargs, the default is CUSTOM.

    @returns the pageid of the new page
    """
    if not kwargs.get('msid'):
        raise ValueError('msid is required.')
    pid = kwargs.get('pid', get_namespace())
    if not pid:
        raise ValueError('Pages cannot be created in the default namespace.')

    kwargs['template'] = kwargs.get('template', CUSTOM)
    if kwargs['template'] not in TEMPLATES:
        raise ValueError('Invalid template type "{}". Must be one of {}.'.format(kwargs['template'], TEMPLATES))

    pageid = 'PG-' + tinyid.TinyIDGenerator(namespace='PG', tinyid_len=12).generate_tinyid().upper()
    msid = kwargs['msid']

    key = PageModel.build_key(pageid, msid, pid=pid)

    kwargs['key'] = key
    kwargs['pid'] = pid
    kwargs['pageid'] = pageid

    page_model_class = import_string('app.models.page.%sPage' % kwargs['template'])
    new_page_entity = page_model_class(**kwargs)
    new_page_entity.put()

    # domain-level prepare
    page_domain_class = import_string('app.domain.page.%sPage' % kwargs['template'])
    page_domain_class.prepare_new_entity(new_page_entity, **kwargs)

    return pageid


def update_page_entity(pageid, msid, pid=None, **kwargs):
    """ Updates an existing page entity. Note: this update is not performed in a transaction (simply for speed).

    @param pageid identifies the page to update
    @param msid the microsite MSID this page belongs to (required for building key)
    @param pid the namespace in which the microsite exists, defaults to the namespace manager's current namespace
    @raises PageNotFoundException if the page is not found
    """

    if not pageid:
        raise ValueError('pageid is required.')
    if not msid:
        raise ValueError('msid is required.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Page must be updated in a pid context (either from the namespace manager, or '
                         'an explicit pid kwarg).')

    key = PageModel.build_key(pageid, msid, pid=pid)
    page = key.get()
    if not page:
        raise exceptions.PageNotFoundException()

    page_domain_class = import_string('app.domain.page.%sPage' % page.template)
    old_page_domain_object = page_domain_class.from_model(page)

    for k, v in kwargs.items():
        setattr(page, k, v)

    page.put()

    kwargs['old_page'] = old_page_domain_object
    page_domain_class.prepare_new_entity(page, **kwargs)


def create_404_page():
    """ returns a custom 404 page """
    kwargs = {
        'pageid': ERROR_404,
        'title': '404 - Page not found',
        'h1': '',
        'template': 'Custom',
        'top_content': ''
    }
    return CustomPage(**kwargs)


def get_page(pageid, msid, pid=None):
    """ Gets a page by id. """
    if not msid:
        raise ValueError('msid is required.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Cannot retrieve pages from the default namespace.')
    entity = None
    if pageid:
        key = PageModel.build_key(pageid, msid, pid=pid)
        entity = key.get()

    if not pageid or not entity:
        return create_404_page()

    domain_class = import_string('app.domain.page.%sPage' % entity.template)
    return domain_class.from_model(entity)


class Page:
    """ The Page abstract parent class. """

    def __init__(self, **kwargs):
        """ Initialize. """
        self.pageid = kwargs.get('pageid')
        self.msid = kwargs.get('msid')
        self.pid = kwargs.get('pid')
        self.title = kwargs.get('title')
        self.h1 = kwargs.get('h1')
        self.meta_keywords = kwargs.get('meta_keywords')
        self.meta_description = kwargs.get('meta_description')
        self.template = kwargs.get('template')
        self.top_content = kwargs.get('top_content')
        self.bottom_content = kwargs.get('bottom_content')

    @classmethod
    def from_model(cls, model):
        """ Creates a domain page given a model page. """
        if not model:
            return None
        return cls(**model.to_dict())

    @property
    def template_include(self):
        """ Returns the name of the template include that corresponds to this page template type. """
        return 'microsites/html/templates/%s.html' % self.template

    # W0613: Unused argument 'new_entity'
    # implementing abstract interface
    @classmethod
    def prepare_new_entity(cls, new_entity, **kwargs):  # pylint: disable=W0613
        """ A hook to allow domain classes to prepare related services and entities. """
        return

    @property
    def has_content(self):
        """ True if the page has content else False """
        return False


class LongFormPage(Page):
    """ A long form post page template. """
    @property
    def has_content(self):
        return True


class ReviewsPage(Page):
    """ A review add page template. """
    @property
    def has_content(self):
        return True


class SingleReviewPage(Page):
    """ A single review template. """
    @property
    def has_content(self):
        return True


class CustomPage(Page):
    """ A custom page template. """
    @property
    def has_content(self):
        return bool(self.top_content or self.bottom_content)


class ImagesPage(Page):
    """ A page with images. """

    def __init__(self, **kwargs):
        self.images = kwargs.pop('images', '[]')
        self.mobile_gallery_style = kwargs.pop('mobile_gallery_style', None)
        self.desktop_gallery_style = kwargs.pop('desktop_gallery_style', None)
        super().__init__(**kwargs)

    @cached_property
    def mappings(self):
        """ Returns the urls of the images for this page. """
        return get_pageid_blob_mappings(self.pageid, self.msid, self.pid)

    @property
    def has_content(self):
        return bool(json.loads(self.images) or self.top_content or self.bottom_content)


class VideosPage(Page):
    """ A page with videos. """
    
    def __init__(self, **kwargs):
        """ Initializes the Videos page. """
        self.videos = Videos(json_obj=kwargs.pop('videos', None))
        super().__init__(**kwargs)

    @property
    def has_content(self):
        return bool(self.videos or self.top_content or self.bottom_content)


class CouponsPage(Page):
    """ A page with coupons. """
    
    def __init__(self, **kwargs):
        """ Initializes the Coupons page. """
        self.coupons = Coupons(json_obj=kwargs.pop('coupons', None))
        super().__init__(**kwargs)

    @property
    def has_content(self):
        return bool(self.coupons or self.top_content or self.bottom_content)


class ContactPage(Page):
    """ A page with contact information. """

    def __init__(self, **kwargs):
        """ Initialize. """
        self.email = kwargs.pop('email', None)
        super().__init__(**kwargs)

    @property
    def has_content(self):
        return bool(self.email or self.top_content or self.bottom_content)


class Videos(list):
    """ A list of Video objects. """
    
    def __init__(self, json_obj, *args, **kwargs):
        """ Accepts a json representation of videos and converts them into a list of Video objects. """
        super().__init__(*args, **kwargs)

        if not json_obj:
            return

        videos = json.loads(json_obj)
        if videos and isinstance(videos, list):
            #videos.reverse()
            self.extend([Video(video_dict) for video_dict in videos])
            
    def __str__(self):
        """ Returns a JSON representation. """
        return json.dumps([video for video in self])


class Coupons(list):
    """ A list of Coupons objects. """
    
    def __init__(self, json_obj, *args, **kwargs):
        """ Accepts a json representation of coupons and converts them into a list of Coupon objects. """
        super().__init__(*args, **kwargs)

        if not json_obj:
            return

        coupons = json.loads(json_obj)
        if coupons and isinstance(coupons, list):
            self.extend([Coupon(cDict) for cDict in coupons])
            
    def __str__(self):
        """ Returns a JSON representation. """
        return json.dumps([coupon.to_dict() for coupon in self])


class Video(dict):
    """ A object containing attributes describing a video. """
    
    ATTRIBUTES = ('url', 'title', 'caption', 'duration', 'phone_url', 'thumbnail_url')
    
    def __init__(self, video_dict):
        """ Initialize the video object. """
        super().__init__()
        
        if not video_dict.get('url'):
            raise ValueError("url is required.")
            
        for attribute in self.ATTRIBUTES:
            if attribute in video_dict:
                self[attribute] = video_dict[attribute]

    def __getattr__(self, name):
        if name not in self.ATTRIBUTES:
            raise AttributeError()
        return self.get(name)
        
    def __setattr__(self, name, value):
        if name not in self.ATTRIBUTES:
            raise AttributeError()
        self[name] = value
        
    @cached_property
    def is_youtube(self):
        """ Returns true if this is a YouTube video. """
        return bool(self.youtube_videoid)
        
    @cached_property
    def youtube_videoid(self):
        """ Returns the youtube videoid, None otherwise. """
        return YouTubeRemoteApi.parse_videoid(self.url)

    @cached_property
    def filetype(self):
        """ Returns the file type of the video"""
        return self.url.split(".")[-1] 

    @cached_property
    def youtube_query_string(self):
        """ returns query string parameters the user has added to their youtube url for controlling the video """
        if self.is_youtube:
            return urllib.parse.urlparse(self.url).query
        else:
            return ''

    
class Coupon(dict):
    """ A object containing attributes describing a coupon. """
    
    ATTRIBUTES = ('title', 'detail', 'image_url', 'category', 'start', 'end', 'expiry')
    DATE_CONVERSION_ATTRIBUTES = ('start', 'end', 'expiry')

    def __init__(self, cdict):
        """ Initialize the coupon object. """
        super().__init__()
        
        if not cdict.get('title'):
            raise ValueError("title is required.")
        
        for attribute in self.ATTRIBUTES:
            if attribute in cdict:
                if attribute in self.DATE_CONVERSION_ATTRIBUTES:
                    self[attribute] = self.date_from_string(cdict[attribute])
                else:
                    self[attribute] = cdict[attribute]
        
    def __getattr__(self, name):
        if name not in self.ATTRIBUTES:
            raise AttributeError()
        return self.get(name)

    def __setattr__(self, name, value):
        if name not in self.ATTRIBUTES:
            raise AttributeError()
        self[name] = value

    def to_dict(self):
        """ Returns a dictionary with formatted date strings. """
        d = self.copy()
        for attribute in self.DATE_CONVERSION_ATTRIBUTES:
            if attribute in self:
                d[attribute] = self.date_to_string(self[attribute])
        return d

    @staticmethod
    def date_from_string(string):
        """ returns a date from a string that is formatted as YYYY-MM-DD """
        if string:
            return datetime.strptime(string, "%Y-%m-%d")
        else:
            return None
        
    @staticmethod
    def date_to_string(datefield):
        """ returns a string from a date and formats it as YYYY-MM-DD """
        if datefield:
            return datefield.strftime("%Y-%m-%d")
        else:
            return ''
