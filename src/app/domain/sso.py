"""SSO domain functions"""
from vautil.http import add_query_params_to_url

from sso_sdk import ServiceProviderClient
from sso_sdk._internal.service_provider import ServiceContext, Partner
from vax.utils.env import env_from_app_engine_env

_SP_CLIENT = ServiceProviderClient()
_SP_CLIENT.set_environment(env_from_app_engine_env())


class MissingSessionTransferURLException(Exception):
    """ the session transfer url was not found """
    pass


def get_session_transfer_url(partner_id, next_url=None):
    """ get the session transfer url for microsites """
    context = ServiceContext(partner=Partner(partner_id=partner_id))
    session_transfer_resp = _SP_CLIENT.get_session_transfer_url('MS', context=context)
    if not session_transfer_resp:
        raise MissingSessionTransferURLException
    session_transfer_url = session_transfer_resp.session_transfer_url
    return add_query_params_to_url(session_transfer_url, {'nextUrl': next_url})

