""" Functions and classes related to microsite page blob mappings. """
import logging
import hashlib
from google.appengine.api import images as images_api
from google.appengine.api.images import get_serving_url
from google.appengine.ext.blobstore import B<PERSON><PERSON><PERSON>n<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from google.appengine.api import urlfetch, taskqueue
from google.appengine.api.namespace_manager.namespace_manager import get_namespace
from google.appengine.ext import ndb, deferred
from app.domain import exceptions
from app.domain.exceptions import PageidBlobMappingExistsException, TooManyPageidBlobMappingsException
from app.domain.constants import Keys
from app.domain.validation import require_args, require_arg_list
from app.domain.blob_mappings import get_image_info, ImportInfo, delete_blob_mapping_by_key, write_to_blobstore, \
    validate_is_image, get_image_dimensions
from app.models.page import PageidBlobMapping as PageidBlobMappingModel, MAX_PAGEID_BLOB_MAPPINGS
from app.models.post import PostidBlobMapping as PostidBlobMappingModel
from app.domain.batch_import.logger import error as bi_error


def add_pageid_blob_mapping(pageid, msid, order=MAX_PAGEID_BLOB_MAPPINGS, existing_key=None, blob_key=None,
                            image_width=None, image_height=None, import_info=None, pid=None, caption=None,
                            alt_text=None, title=None, uses_gcs=False):
    """ Adds a Pageid to blob mapping to the datastore.

    @param pageid The id of the page to create blob mapping for.
    @param msid The id of the microsite this page belongs to.
    @param order The order this item will be added relative to others. If not provided mapping will be added
                 to the end of the list.
    @param existing_key Indicates a mapping already exists and should be deleted.
    @param blobinfo The blob info we are mapping to the given pageid.
    @param import_info An optional ImportInfo containing details about an imported image
    @param pid The namespace this mapping belongs in.
    @param caption The optional caption for the blob.
    @param alt_text The optional alt text for the blob (e.g., <img alt=""/>)
    @param title The optional title text for the blob (e.g., <img title=""/>)
    """
    if not pageid:
        raise ValueError('pageid required')
    if not msid:
        raise ValueError('msid required')
    if order:
        int(order)
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('PageidBlobMapping cannot be created in default namespace.')

    pid = pid.upper()
    kwargs = {}

    if blob_key:
        kwargs['blobkey'] = blob_key
        kwargs['serving_url'] = get_serving_url(blob_key)
        kwargs['width'] = int(image_width)
        kwargs['height'] = int(image_height)
        kwargs['uses_gcs'] = uses_gcs

    if import_info:
        kwargs['import_url'] = import_info.url
        kwargs['import_last_modified'] = import_info.last_modified
        kwargs['import_etag'] = import_info.etag
        kwargs['import_filename'] = import_info.filename
        kwargs['import_md5_hash'] = import_info.md5_hash

    if caption is not None:
        kwargs['caption'] = caption
    if alt_text is not None:
        kwargs['alt_text'] = alt_text
    if title is not None:
        kwargs['title'] = title

    if existing_key:
        delete_blob_mapping_by_key(existing_key)

    @ndb.transactional
    def tx(pageid, msid, order, pid=None, **kwargs):
        """ Updates all the pageid blob mappings in a transaction to facilitate re-ordering. """

        mappings = __get_pageid_blob_mappings_from_datastore(pageid, msid, pid=pid)

        if len(mappings) >= MAX_PAGEID_BLOB_MAPPINGS:
            raise exceptions.TooManyPageidBlobMappingsException(
                "Too many blob mappings - maximum is %d" % MAX_PAGEID_BLOB_MAPPINGS)

        # check if mapping with import url already exists
        import_url = import_info and import_info.url
        if import_url:
            for mapping in mappings:
                if mapping.import_url == import_url:
                    raise exceptions.PageidBlobMappingExistsException(
                        'The pageid blob mapping for "%s" already exists. They must be unique for each page.' % \
                        import_url)

        # find a slot number
        existing_key_names = {mapping.key.string_id() for mapping in mappings}
        for n in range(MAX_PAGEID_BLOB_MAPPINGS):
            new_mapping_key = PageidBlobMappingModel.build_key(pageid, msid, n, pid=pid)
            if new_mapping_key.string_id() not in existing_key_names:
                break

        new_mapping = PageidBlobMappingModel(key=new_mapping_key, pageid=pageid, msid=msid, order=0, **kwargs)

        # insert the new mapping in the list
        mappings.insert(order-1, new_mapping)

        # re-index the mappings so there is a nice, increasing order
        new_order = 0
        for mapping in mappings:
            new_order += 1
            mapping.order = new_order

        ndb.put_multi(mappings)
        return new_mapping

    new_mapping = tx(pageid, msid, order, pid=pid, **kwargs)
    return PageidBlobMapping.from_model(new_mapping)


def update_pageid_blob_mapping(existing_key, caption=None, alt_text=None, title=None, order=None):
    """ Updates a PageidBlobMapping information.

    To clean caption, alt_text, or title, pass the empty string '' (as opposed to None; None means do not update).
    """
    if not existing_key:
        raise ValueError('existing_key is required.')
    mapping = existing_key.get()
    if not mapping:
        raise ValueError('PageidBlobMapping not found for key "%s".' % existing_key)
    if caption is not None:
        mapping.caption = caption
    if alt_text is not None:
        mapping.alt_text = alt_text
    if title is not None:
        mapping.title = title
    if order is not None:
        mapping.order = order
    mapping.put()
    return PageidBlobMapping.from_model(mapping)

def get_pageid_blob_mapping(key):
    """ Retrieves a PageidBlobMapping for a given key. """
    entity = key.get()
    return PageidBlobMapping.from_model(entity)

def get_pageid_blob_mappings(pageid, msid, pid=None):
    """ Retrieves the PageidBlobMappings for a given pageid. """
    entities = __get_pageid_blob_mappings_from_datastore(pageid, msid, pid=pid)
    return [PageidBlobMapping.from_model(entity) for entity in entities]

def __get_pageid_blob_mappings_from_datastore(pageid, msid, pid=None):
    """ Retrieves all of the PageidBlobMappings using predictive keys. """
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Pid cannot be in default namespace.')
    keys = PageidBlobMappingModel.build_predictive_keys(pageid, msid, pid=pid)
    mappings = ndb.get_multi(keys)
    mappings = [mapping for mapping in mappings if mapping is not None]
    mappings.sort(key=lambda entity: entity.order)
    return mappings

def add_or_update_mapping(image_dict, existing_mappings, pageid, msid, pid):
    """ Retrieve the mapping that matches the given import_url or create one. """

    # check if mapping exists
    mapping = None
    for existing_mapping in existing_mappings:
        blobinfo = existing_mapping.blobkey and BlobInfo(existing_mapping.blobkey)
        if existing_mapping.import_info and (image_dict.get('url') == existing_mapping.import_info.url) or \
           blobinfo and (image_dict.get('blobkey') == blobinfo.key()):
            mapping = existing_mapping
            break

    caption = image_dict.get('caption', '')
    alt_text = image_dict.get('alt_text', '')
    title = image_dict.get('title', '')

    if not mapping:
        # add blob mapping
        import_info = ImportInfo(image_dict.get('url'), md5_hash=image_dict.get('md5_hash'))
        mapping = add_pageid_blob_mapping(pageid, msid, import_info=import_info, pid=pid,
                                          caption=caption, alt_text=alt_text, title=title)
    else:
        # update optional information (e.g., caption, alt_text, title)
        if mapping.caption != caption or mapping.alt_text != alt_text or mapping.title != title:
            mapping = update_pageid_blob_mapping(mapping.key, caption=caption, alt_text=alt_text, title=title)

    return mapping

def reorder_pageid_blob_mappings(pageid, msid, images, pid=None):
    """ Reorders PageidBlobMappings based on the list of images (urls) provided.

    @params pageid The pageid to retrieve blob mappings for.
    @param msid The id of the microsite this page belongs to.
    @param images The list of urls indicating the new order of the mappings.
    @param pid The namespace this mapping belongs in.

    @returns A reordered list of PageidBlobMappings for the given pageid.
    """

    if not pageid:
        raise ValueError('pageid required')
    if not msid:
        raise ValueError('msid required')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('PageidBlobMappings cannot be retrieved from default namespace.')

    if not images:
        # There might be a case where a page has no images so just return an empty list
        return []

    pid = pid.upper()

    @ndb.transactional
    def tx(pageid, msid, images, pid=None):
        """ Reorders all the mappings in a transaction. """

        mappings = __get_pageid_blob_mappings_from_datastore(pageid, msid, pid=pid)
        for mapping in mappings:
            if mapping.import_url in images:
                mapping.order = images.index(mapping.import_url) + 1
            else:
                raise ValueError('The pageid blob mapping for "%s" does not correspond to an page image.' % \
                                 mapping.import_url)

        ndb.put_multi(mappings)
        return mappings

    mappings = tx(pageid, msid, images, pid=pid)
    mappings.sort(key=lambda entity: entity.order)
    return [PageidBlobMapping.from_model(mapping) for mapping in mappings]

def process_mappings(image_dicts, pageid, msid, pmsid=None, linenum=None, importid=None, pid=None):
    """
    Process pageid blob mappings.
    Add any that are missing and delete any that are no longer referenced.
    Start up a state machine to process (fetch/update) images.
    """
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Page must be updated in a pid context (either from the namespace manager, or '
                         'an explicit pid kwarg).')
    if len(image_dicts) > MAX_PAGEID_BLOB_MAPPINGS:
        msg = 'Too many Images given for a Page.  Only the first %d will be processed' % MAX_PAGEID_BLOB_MAPPINGS
        bi_error(importid, linenum, msg, pid, msid=msid,
                partner_microsite_identifier=pmsid)
        logging.warn(msg, exc_info=True)
        del image_dicts[MAX_PAGEID_BLOB_MAPPINGS:]

    mappings = []
    existing_mappings = get_pageid_blob_mappings(pageid, msid, pid=pid)
    delete_mappings_from_image_dicts(image_dicts, existing_mappings)

    for image_dict in image_dicts:
        if image_dict.get('md5_hash'):
            # any image dicts with md5 hash are files that have already been uploaded
            continue
        try:
            mapping = add_or_update_mapping(image_dict, existing_mappings, pageid, msid, pid)
            mappings.append(mapping)
        except PageidBlobMappingExistsException as pbee:
            if bool(pmsid) and bool(linenum) and bool(importid):
                bi_error(importid, linenum, pbee.args[0], pid, msid=msid,
                        partner_microsite_identifier=pmsid)
                logging.warn(pbee.args[0], exc_info=True)
            else:
                raise pbee
        except TooManyPageidBlobMappingsException as tme:
            if bool(pmsid) and bool(linenum) and bool(importid):
                bi_error(importid, linenum, tme.args[0], pid, msid=msid,
                        partner_microsite_identifier=pmsid)
                logging.warn(tme.args[0], exc_info=True)
            else:
                raise tme

    return mappings

@require_arg_list(arg_list=['pageid', 'msid'])
def process_image_dicts(image_dicts, pageid, msid, pid=None):
    """
    An alternative to process_mappings.
    Processes a list of image dicts containing pageid blob mapping data.
    Any new mappings containing urls are immediately async fetched (as opposed to process_mappings which starts an FSM)

    Updates the pageid blob mappings for the page to match what is supplied in image dicts by deleting un found urls,
    adding new ones and updating changed ones.

    :param image_dicts: [{'caption': 'blah', 'alt_text':'blah', 'title':'blah', 'url': 'blah'}] url is the only
        required field
    :param pageid: id of the page that we are updating
    :param msid: id of the microsite holding the page we are updating
    :param pid: id of the partner holding the microsite we are updating
    :return: A list of errors urls which could not be fetched or None
    """
    # image_dicts must be provided but can be an empty list
    if not isinstance(image_dicts, list):
        raise TypeError('image_dicts must be a list.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('Page must be updated in a pid context (either from the namespace manager, or '
                         'an explicit pid kwarg).')

    # get the existing images and remove the ones that have been removed by user, only after that add the new ones
    existing_mappings = get_pageid_blob_mappings(pageid, msid, pid=pid)
    delete_mappings_from_image_dicts(image_dicts, existing_mappings=existing_mappings)
    error_urls = add_mappings_from_image_dicts(image_dicts, pageid, msid, pid=pid, existing_mappings=existing_mappings)

    if not error_urls:
        existing_mappings = get_pageid_blob_mappings(pageid, msid, pid=pid)
    update_mappings_from_image_dicts(image_dicts, existing_mappings=existing_mappings)

    return error_urls

@require_args
def process_temp_blobinfo(blobinfo, msid, pid=None):
    """
    Blob was uploaded but has not been mapped to a page.
    This typically happens when images are added to a Page before it is saved.

    Get the image info and queue a task to delete the potentially orphaned blob.
    If the blob ends up getting mapped to a Page then the task will be deleted.

    :return: An ImageInfo object for the provided blobinfo.
    """
    image_info = get_image_info(blobinfo)
    # one week in seconds
    one_week_in_s = 604800
    retry_options = taskqueue.TaskRetryOptions(task_retry_limit=Keys.DEFAULT_TASK_RETRY)
    name = _construct_blobinfo_task_name(blobinfo.key(), msid, pid=pid)
    url = Keys.DEFER_URL_ROOT + 'CleanupBlob/'
    logging.debug('Deferring task to delete temporary blobinfo with name "%s".', name)
    deferred.defer(__cleanup_blob, blobkey=blobinfo.key(), pid=pid, _countdown=one_week_in_s,
                   _name=name, _url=url, _retry_options=retry_options, _queue=Keys.QUEUE_BLOB_CLEANUP)

    return image_info

@require_args
def create_mappings_for_blobs(image_dicts, pageid, msid, pid=None):
    """ Create a PageidBlobMapping for a previously uploaded blob.
    :param image_dicts: A list of dictionaries containing data for the blob being mapped.
    :param pageid: The page to create the mapping for.
    :param msid: Microsite the page belongs to.
    :param pid: Partner the microsite belongs to.
    :return: A list of mappings created.
    """
    mappings = []
    queue = taskqueue.Queue(name=Keys.QUEUE_BLOB_CLEANUP)
    blobkeys = [image_dict.get('blobkey') for image_dict in image_dicts]

    for blobkey, image_dict in zip(blobkeys, image_dicts):
        if not blobkey:
            continue

        kwargs = {
            'blob_key': BlobKey(blobkey),
            'pid': pid,
            'caption': image_dict.get('caption'),
            'alt_text': image_dict.get('alt_text'),
            'title': image_dict.get('title'),
            'image_width': image_dict.get('width'),
            'image_height': image_dict.get('height')
        }
        mapping = add_pageid_blob_mapping(pageid, msid, **kwargs)

        # remove tasks to delete blob mapping from queue
        name = _construct_blobinfo_task_name(blobkey, msid, pid=pid)
        logging.debug('Deleting deferred task to with name "%s".', name)
        queue.delete_tasks(taskqueue.Task(name=name))

        mappings.append(mapping)

    return mappings

@require_args
def _construct_blobinfo_task_name(blobkey, msid, pid=None):
    """
    Construct a task name for a blobinfo.
    Task name must match expression "^[a-zA-Z0-9_-]{1,500}$"
    """
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('pid is required.')
    # this hash will be unique for each blob key
    key_hash = hashlib.md5(str(blobkey))
    return '{}-{}-{}'.format(pid, msid, key_hash.hexdigest())

def __cleanup_blob(blobkey, pid=None):
    """ Delete unused blob entity.
    :param blobkey: The key of the blob to delete.
    :param pid: The partner namespace
    """
    if not blobkey:
        raise ValueError('blobkey is required.')
    # Ensure no mapping has been created actually using this blobinfo
    page_mapping_keys = PageidBlobMappingModel.lookup_by_blobkey(blobkey, pid=pid, keys_only=True)
    post_mapping_keys = PostidBlobMappingModel.lookup_by_blobkey(blobkey, pid=pid, keys_only=True)
    if not page_mapping_keys and not post_mapping_keys:
        # This blob is an orphan. Proceed with deletion.
        blobinfo = BlobInfo.get(blobkey)

        if blobinfo:
            blobinfo.delete()
        else:
            logging.info("blob with key: '%s' has already been removed", blobkey)

@require_arg_list(arg_list=['pageid', 'msid'])
def add_mappings_from_image_dicts(image_dicts, pageid, msid, pid=None, existing_mappings=None):
    """ Add pageid blob mappings for the list of data specified in image_dicts. """
    # image_dicts must be provided but can be an empty list
    if not isinstance(image_dicts, list):
        raise TypeError('image_dicts must be a list.')
    pid = pid or get_namespace()
    if not pid:
        raise ValueError('pid is required.')

    existing_mappings = existing_mappings or []
    error_urls = None
    # add images from urls
    url_images_to_add = compare_image_mappings(image_dicts, existing_mappings)
    if url_images_to_add:
        _, error_urls = add_images_to_images_page_from_urls(pageid, msid, pid, url_images_to_add)

    # create mappings for any images uploaded to blobstore
    existing_blobkeys = [existing_mapping.blobkey for existing_mapping in existing_mappings]
    file_images_to_map = [image_dict for image_dict in image_dicts if image_dict.get('blobkey') and
                                                                      image_dict['blobkey'] not in existing_blobkeys]
    if file_images_to_map:
        create_mappings_for_blobs(file_images_to_map, pageid, msid, pid=pid)

    return error_urls


def compare_image_mappings(image_dicts, existing_mappings):
    """ Compare image URLs, etags and last-modified headers to determine if image is to be update """
    image_dicts_to_add = []
    existing_urls = [existing_mapping.import_info.url for existing_mapping in existing_mappings if
                 (existing_mapping.import_info and existing_mapping.import_info.url)]
    for image_dict in image_dicts:
        if image_dict.get('url'):
            if image_dict['url'] not in existing_urls:
                image_dicts_to_add.append(image_dict)
            else:
                mapping = [existing_mapping for existing_mapping in existing_mappings
                           if existing_mapping.import_info and
                           existing_mapping.import_info.url == image_dict['url']][0]
                if image_headers_equal(image_dict['url'], mapping):
                    continue
                image_dicts_to_add.append(image_dict)
    return image_dicts_to_add


def image_headers_equal(image_url, existing_mapping):
    """ Compare headers of image from URL and an existing URl mapping """
    response = urlfetch.fetch(image_url, method='HEAD')
    if not existing_mapping.import_info.last_modified or \
       existing_mapping.import_info.last_modified != response.headers.get('last-modified'):
        return False
    if not existing_mapping.import_info.etag or \
       existing_mapping.import_info.etag != response.headers.get('etag'):
        return False
    return True


def update_mappings_from_image_dicts(image_dicts, existing_mappings=None):
    """ Update existing pageid blob mappings, if they exist, with the list of data in image_dicts. """
    # image_dicts must be provided but can be an empty list
    if not isinstance(image_dicts, list):
        raise TypeError('image_dicts must be a list.')

    existing_mappings = existing_mappings or []
    # find and update existing mappings
    order = 0
    for image_dict in image_dicts:
        order += 1
        mapping_matches = [existing_mapping for existing_mapping in existing_mappings
                           if existing_mapping.import_info and
                              (existing_mapping.import_info.url and
                              existing_mapping.import_info.url == image_dict.get('url')) or
                              (existing_mapping.blobkey and
                              existing_mapping.blobkey == image_dict.get('blobkey'))]
        if len(mapping_matches) > 1:
            raise PageidBlobMappingExistsException("There exists multiple mappings for the same image: %s" %
                                                   mapping_matches)

        # take the mapping out of the list if it exists
        mapping = mapping_matches and mapping_matches[0]
        if not mapping:
            continue

        caption = image_dict.get('caption', '')
        alt_text = image_dict.get('alt_text', '')
        title = image_dict.get('title', '')
        if (mapping.caption != caption or mapping.alt_text != alt_text or mapping.title != title or
                    mapping.order != order):
            update_pageid_blob_mapping(mapping.key, caption=caption, alt_text=alt_text, title=title, order=order)

def _is_missing_mapping(mapping, import_urls, serving_urls):
    """
    A mapping is missing from the updated list of mappings if
    - it was imported by url, but not in the list of known import urls OR
    - it was imported by a url already in an existing mapping with etag or last-modifed headers updated OR
    - it was imported from file, but not in the list of known serving urls
    """
    if mapping.import_info and mapping.import_info.url:
        if mapping.import_info.url in import_urls:
            return not image_headers_equal(mapping.import_info.url, mapping)
        else:
            return True
    return mapping.serving_url and mapping.serving_url not in serving_urls

def _get_missing_mappings(image_dicts, existing_mappings):
    """
    Find mappings in existing_mappings that are not present in image_dicts.
    A mapping will contain either an import url (image was specified by a URL) or an md5_hash (image was loaded from
    a file) as a unique identifier.  The identifier must be present in one of the image dictionaries, or else the
    mapping will be deemed missing.
    """
    serving_urls = [d['serving_url'] for d in image_dicts if d.get('serving_url')]
    import_urls = [d['url'] for d in image_dicts if d.get('url')]
    missing_mappings = \
        [mapping for mapping in existing_mappings if _is_missing_mapping(mapping, import_urls, serving_urls)]
    return missing_mappings

def delete_mappings_from_image_dicts(image_dicts, existing_mappings=None):
    """ Delete any existing pageid blob mappings that do not exist within the list of data in image_dicts. """
    # image_dicts must be provided but can be an empty list
    if not isinstance(image_dicts, list):
        raise TypeError('image_dicts must be a list.')

    existing_mappings = existing_mappings or []
    # delete all unused mappings
    delete_mappings = _get_missing_mappings(image_dicts, existing_mappings)

    for delete_mapping in delete_mappings:
        # need to notify microsite blob mapping no longer exists
        delete_blob_mapping_by_key(delete_mapping.key)

@require_args
def add_images_to_images_page_from_urls(pageid, msid, pid, image_urls_or_dicts):
    """
    In parallel, fetches the urls supplied in image_urls. Uploads them to blobs and maps the blobs to the pageid.
    Returns a tuple of 2 lists, the first list being all successfully uploaded urls, the second being urls that did not
    upload properly. Check the logs for info as to why.
    """
    successfully_uploaded_image_urls    = []
    unsuccessful_urls                   = []
    url_rpc_map                         = {}
    url_info_map                        = {}

    for url in image_urls_or_dicts:
        if not isinstance(url, (str, dict)):
            raise ValueError("%s must be either string, unicode or dictionary." % image_urls_or_dicts)
        if isinstance(url, dict):
            image_dict = url.copy() # we are going to pop something off
            url = image_dict.pop('url')
            url_info_map[url] = image_dict
        rpc = urlfetch.create_rpc()
        url_rpc_map[url] = rpc
        urlfetch.make_fetch_call(rpc, url)

    for url, rpc in list(url_rpc_map.items()):
        try:
            response = rpc.get_result()
            if response.status_code != 200:
                raise exceptions.InvalidImageUrlException(url, response.status_code)
            validate_is_image(response.content) # raises NotImageError if not valid data
            # save image to GCS
            width, height = get_image_dimensions(response.content)
            blob_key = write_to_blobstore(pid, msid, response.content)
            image_dict = url_info_map.get(url) or {}
            image_etag = response.headers.get('etag')
            image_last_modified = response.headers.get('last-modified')
            import_info = ImportInfo(url, image_last_modified, image_etag)
            add_pageid_blob_mapping(pageid, msid, pid=pid, blob_key=blob_key, image_width=width, image_height=height,
                                    import_info=import_info, uses_gcs=True, **image_dict)
            successfully_uploaded_image_urls.append(url)

        except (images_api.Error, exceptions.InvalidImageUrlException, urlfetch.DownloadError,
                urlfetch.DeadlineExceededError, urlfetch.Error):
            unsuccessful_urls.append(url)

    return successfully_uploaded_image_urls, unsuccessful_urls

class PageidBlobMapping:
    """ A pageid->blob mapping. """

    def __init__(self, **kwargs):
        """ Initialize."""
        if not kwargs.get('pageid'):
            raise ValueError('pageid is required.')

        self.key = kwargs['key']
        self.pageid = kwargs['pageid']
        self.msid = kwargs['msid']
        self.order = kwargs['order']
        self.blobkey = kwargs['blobkey']
        self.serving_url = kwargs.get('serving_url')
        self.width = kwargs.get('width')
        self.height = kwargs.get('height')
        self.caption = kwargs.get('caption')
        self.alt_text = kwargs.get('alt_text')
        self.title = kwargs.get('title')

        self.import_info = None
        if kwargs.get('import_url') or kwargs.get('import_md5_hash'):
            self.import_info = ImportInfo(url=kwargs.get('import_url'),
                                          last_modified=kwargs.get('import_last_modified'),
                                          etag=kwargs.get('import_etag'),
                                          filename=kwargs.get('import_filename'),
                                          md5_hash=kwargs.get('import_md5_hash'))

    @classmethod
    def from_model(cls, model):
        """ Builds the class from a model. """
        if not model:
            return None
        return cls(**model.to_dict())
