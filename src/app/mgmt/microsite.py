"""
Pipelines related to microsite entities
"""
from app import constants
from app.domain import navigation
from app.models.page import ReviewsPage
from app.models.partner import Partner
from app.pipelines.microsite.base import CreateSitePages
from mapreduce import mapreduce_pipeline, context, operation as op
from vpipeline import CommonPipeline

from mapreduce.mapreduce_pipeline import MapperPipeline

from app.domain.url_mappings import add_hostslug_msid_mapping
from app.domain.utils import slugify
from app.domain.microsite import Microsite as MicrositeDomain
from app.models.url_mappings import HostSlugMsidMapping, HostPidMapping
from app.models.microsite import Microsite
from vpipeline.big_query_pipeline import BigQueryWorkflowPipeline
from vpipeline.input_readers import BigQueryInputReader


PAGE_QUERY = """
SELECT msid, pid
FROM
(
  SELECT page.msid as msid, page.pid as pid
  FROM [datastore.Page] page
  JOIN EACH [datastore.Microsite] ms on page.msid = ms.msid
  WHERE
    page.msid IN (
      SELECT msid FROM (
        SELECT count(*) as the_count, msid
        FROM [datastore.Page]
        GROUP BY msid
      ) where the_count = 1
    )
    AND
      (page.title.string = 'Home' OR page.title.string = ms.name)
    AND
      (page.h1.string = 'Home' OR page.h1.string = ms.name)
    AND
      page.top_content is null
    AND
      page.bottom_content is null
    AND
      (page.template = 'Custom' or page.template = 'Page')
),
(
  SELECT msid, pid
  FROM [datastore.Microsite]
  WHERE msid NOT IN (SELECT msid FROM [datastore.Page])
)
GROUP BY msid, pid;
"""
NO_PAGES_QUERY = """
SELECT msid, pid
FROM [datastore.Microsite]
WHERE
  msid not in (
    SELECT msid
    FROM [datastore.Page]
    GROUP BY msid
  );
"""

class AddMissingHostSlugMappings(CommonPipeline):
    """
    Add host mappings for sites that don't have one
    """
    def run(self):  # pylint: disable=W0221
        """
        run
        """
        yield mapreduce_pipeline.MapperPipeline(
            "Add missing msid host mappings",
            "app.mgmt.microsite.missing_host_slug_mapper",
            "mapreduce.input_readers.DatastoreInputReader",
            None,
            params={
                "entity_kind": 'app.models.microsite.Microsite',
                "namespace": 'FRAN'
            },
            shards=16)


def missing_host_slug_mapper(entity):
    """
    Remove action required tag from social post entities
    """
    mapping = HostSlugMsidMapping.lookup_by_msid_query(entity.msid).get()
    if not mapping:
        host_mapping = HostPidMapping.lookup_by_pid_query(entity.pid).get()
        company_name = entity.name + '-' + entity.msid[3:]
        slug = slugify(company_name)
        add_hostslug_msid_mapping(host_mapping.host, slug, entity.pid, entity.msid, transactional=False)
        yield op.counters.Increment('Added slug {} for msid {}'.format(slug, entity.msid))


def set_default_sso_tokens_mapper(entity):
    """
    Set sso token for accounts that do not have one to be equal to the msid
    """
    msid = entity.msid
    if not entity.sso_token:
        entity.sso_token = msid
        yield op.db.Put(entity)


class SetDefaultSSOTokenPipeline(CommonPipeline):
    """ Pipeline to set default sso tokens for all partners. """

    def run(self, **kwargs):
        """ Run the pipeline """
        partner_keys = Partner.all_partners(keys_only=True)
        for partner_key in partner_keys:
            pid = partner_key.string_id()

            yield MapperPipeline("set microsite sso tokens",
                                 set_default_sso_tokens_mapper.__module__ + '.' +
                                 set_default_sso_tokens_mapper.__name__,
                                 "mapreduce.input_readers.DatastoreInputReader",
                                 params={
                                     "entity_kind": Microsite.__module__ + '.' + Microsite.__name__,
                                     "namespace": pid
                                 },
                                 shards=32
                                 )


def start_default_theme(project_id):
    """
    Start a pipeline to set the default microsite theme to the My Listing
    """
    if project_id not in ['microsite-test', 'microsite-demo', 'microsite-prod']:
        raise ValueError('project_id is not valid')

    callback_pipeline_path = SetDefaultThemePipeline.__module__ + '.' + SetDefaultThemePipeline.__name__
    stage = BigQueryWorkflowPipeline(project_id, PAGE_QUERY, callback_pipeline_path)
    stage.start()
    return stage.pipeline_id


class SetDefaultThemePipeline(CommonPipeline):
    """
    Set the default My Listing for sites that are unused
    """
    def run(self, project_id, job_id, **kwargs):
        """ run """
        shards = kwargs.get('shards') or 4
        yield MapperPipeline("Set Default Theme",
                             set_default_page_mapper.__module__ + '.' + set_default_page_mapper.__name__,
                             BigQueryInputReader.__module__ + '.' + BigQueryInputReader.__name__,
                             None,
                             params={
                                 BigQueryInputReader.PROJECT_ID: project_id,
                                 BigQueryInputReader.JOB_ID: job_id
                             },
                             shards=shards)


def set_default_page_mapper(row):
    """
    Set the default page to the My Listing
    """
    msid = row.get('msid')
    pid = row.get('pid')

    entity = Microsite.get_by_msid(msid, pid)
    if entity:
        entity.phone_theme = constants.LOCATION_THEME
        entity.desktop_theme = constants.LOCATION_THEME
        yield op.counters.Increment('Setting default theme for site for %s.' % pid)
        yield op.db.Put(entity)
    else:
        yield op.counters.Increment('Site not found for msid {} and pid {}.'.format(msid, pid))


def start_fix_no_pages(project_id):
    """
    Start a pipeline to set the default microsite theme to the My Listing
    """
    if project_id not in ['microsite-dev', 'microsite-test', 'microsite-demo', 'microsite-prod']:
        raise ValueError('project_id is not valid')

    callback_pipeline_path = AddDefaultPagePipeline.__module__ + '.' + AddDefaultPagePipeline.__name__
    stage = BigQueryWorkflowPipeline(project_id, NO_PAGES_QUERY, callback_pipeline_path)
    stage.start()
    return stage.pipeline_id


class AddDefaultPagePipeline(CommonPipeline):
    """
    Set the default My Listing for sites that are unused
    """
    def run(self, project_id, job_id, **kwargs):
        """ run """
        shards = kwargs.get('shards') or 4
        yield MapperPipeline("Add default page",
                             add_default_page_mapper.__module__ + '.' + add_default_page_mapper.__name__,
                             BigQueryInputReader.__module__ + '.' + BigQueryInputReader.__name__,
                             None,
                             params={
                                 BigQueryInputReader.PROJECT_ID: project_id,
                                 BigQueryInputReader.JOB_ID: job_id
                             },
                             shards=shards)


def add_default_page_mapper(row):
    """
    Set the default page to the My Listing
    """
    msid = row.get('msid')
    pid = row.get('pid')
    site = Microsite.get_by_msid(msid, pid)
    if site:
        CreateSitePages.create_pages(site, [constants.DEFAULT_HOME_PAGE])
        yield op.counters.Increment('Default page added to site: msid {} and pid {}.'.format(msid, pid))
    else:
        yield op.counters.Increment('Site not found for msid {} and pid {}.'.format(msid, pid))


class AddReviewPagePipeline(CommonPipeline):
    """
    Add review page to every microsite
    """
    def run(self):
        """ run """
        yield mapreduce_pipeline.MapperPipeline(
            "Add review page",
             add_review_page_mapper.__module__ + '.' + add_review_page_mapper.__name__,
            "mapreduce.input_readers.DatastoreInputReader",
            None,
            params={
                "entity_kind": 'app.models.microsite.Microsite',
            },
            shards=16)


def add_review_page_mapper(site):
    """
    add the review page
    """
    yield op.counters.Increment('Review page being added')
    CreateSitePages.create_pages(site, [constants.DEFAULT_REVIEW_PAGE])


def update_reviews_tab_title_and_position_param_validator(params):
    """
    validates the mapreduce parameters
    """
    pid = params['namespace']
    partner = Partner.build_key(pid).get()
    if not partner:
        raise ValueError('Invalid namespace specified')

    position = params['position']
    if position:
        position = int(position)
        if position <= 0:
            raise ValueError('position must be an integer greater than 0')
        params['position'] = position

    title = params['title']
    if not title:
        raise ValueError('title must be provided')

    try:
        commit = int(params['commit']) != 0
    except ValueError:
        commit = False
    params['commit'] = commit


def update_reviews_tab_title_and_position(microsite):
    """
    updates the new Reviews tab to a custom title and tab position
    """
    params = context.get().mapreduce_spec.mapper.params
    commit = params['commit']
    position = params['position']
    title = params['title']
    namespace = params['namespace']

    page = ReviewsPage.query(namespace=namespace).filter(ReviewsPage.msid == microsite.msid).get()
    if not page:
        yield op.counters.Increment('page not found for msid %s' % microsite.msid)
    else:
        page.title = title
        microsite_domain = MicrositeDomain.from_model(microsite)
        if commit:
            microsite_domain.update_navigation('review', title)
            yield op.db.Put(page)
            yield op.counters.Increment('updated page title')
            if position:
                try:
                    navigation.update_navigation_entity(position, 'review', page.pageid, microsite_domain)
                    yield op.counters.Increment('updated tab position')
                except ValueError:
                    yield op.counters.Increment('no navigation item for review page found')
        else:
            yield op.counters.Increment('would have updated page title')
