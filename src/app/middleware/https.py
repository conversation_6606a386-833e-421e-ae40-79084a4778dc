"""
HTTPS Uplift Middleware Mixin
"""
from app.models.partner import Partner
from webob import Request, Response
from app.domain.cache import HostPidMappingCache

import urllib.parse

UPLIFT_DOMAINS = [
    "pdqs.mobi"  # *.pdqs.mobi is the grey label default domain vendasta provides on production.
]


class HttpsUpLiftMiddleware:
    """
    Uplift Middleware
    """

    def __init__(self, app):
        """
        Initialize
        """
        self.app = app

    def __call__(self, environ, start_response):
        """
        If the domain is our default domain and the request is not over ssl uplift. This should not occur
        for taskqueue or fantasm tasks
        """
        req = Request(environ)

        uplifted_url = HttpsUpLiftMiddleware.uplift_url(req.url, req.method)
        if uplifted_url is None:
            resp = req.get_response(self.app)
        else:
            resp = Response(status=301, location=uplifted_url)

        return resp(environ, start_response)

    @staticmethod
    def uplift_url(req_url, req_method):
        """
        Determines if the provided request URL should be uplifted to HTTPS and provides the new uplifted URL if so. If
          the URL should not be uplifted it returns None.

          req_url: string containing the url
          req_method: string containing the request method (get, post, etc)

        *.pdqs.mobi.com is the grey label default domain vendasta provides on PROD.
        *.microsite-demo.appspot.com is the DEMO vendasta domain.
        We currently do not uplift DEMO domain because it does not have proper
        security certificates in place.
        """

        if req_method.lower() == "post":
            return None

        parsed_url = urllib.parse.urlparse(req_url)

        if 'queue' in parsed_url.path or 'fantasm' in parsed_url.path or parsed_url.scheme == 'https':
            return None

        if any([d in parsed_url.netloc for d in UPLIFT_DOMAINS]):
            return urllib.parse.urlunparse(['https', parsed_url.netloc, parsed_url.path, parsed_url.params,
                                        parsed_url.query, parsed_url.fragment])

        host_pid_mapping = HostPidMappingCache().get(parsed_url.hostname)
        if host_pid_mapping is None:
            return None
        partner = Partner.get(host_pid_mapping.pid)

        if partner and partner.angular_app_https:
            return urllib.parse.urlunparse(['https', parsed_url.netloc, parsed_url.path, parsed_url.params,
                                        parsed_url.query, parsed_url.fragment])

        return None


class HttpsUpLiftMiddlewareMixin:
    """
    Https mixin
    """

    def before_dispatch(self, handler):
        """
        Log request
        :return: None
        """
        pass

    def dispatch(self):
        """
        Apply middleware to request
        """
        uplifted_url = HttpsUpLiftMiddleware.uplift_url(self.request.url, self.request.method)
        if uplifted_url is not None:
            webapp2.redirect(uplifted_url, abort=True, code=301)
        return super().dispatch()
