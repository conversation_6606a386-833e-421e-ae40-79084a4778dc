""" Models

Models should:
 - contain the db.Model definitions for accessing App Engine Datastore
 - contain all lookup methods and queries
 - communicate with the google.appengine.ext.ndb package
 
Models should not:
 - contain logic, other than logic to formulate a query
"""

import time
import logging
from google.appengine.api.namespace_manager import namespace_manager
from google.appengine.ext import ndb
from google.appengine.ext.ndb import Model as ndb_model

class QueryResult:
    """ The results of a query. """
    def __init__(self, name):
        """ Initialize. 
        
        @param name the symbolic name of the query, for stats logging
        """
        if not name:
            raise ValueError('name is required.')
        self.name = name
        self._results = []
        self.cursor = None
        self.has_more = True
        self._startTime = time.time()
        
    def __get_results(self):
        """ Returns the results. """
        return self._results
        
    def __set_results(self, results):
        """ Sets the results (and stops the timer and emits a log message). """
        endTime = time.time()
        self._results = results
        logging.debug('Query "%s" %dms.', self.name, (endTime - self._startTime)/1000)
        
    results = property(__get_results, __set_results)
    
    def __len__(self):
        """ The number of results. """
        return len(self._results)

    def __iter__(self):
        """ Returns an iterator over the results. """
        return iter(self._results)


class BaseNDBModel(ndb_model):
    """ Base Model Class for all models using the NDB data layer. """
    USE_NAMESPACE = True
    KEY_NAME_FIELDS = []

    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)

    @classmethod
    def _get_namespace(cls, pid):
        """ Returns the namespace for creating or retrieving entities. """
        namespace = ''
        if cls.USE_NAMESPACE:
            pid = pid or namespace_manager.get_namespace()
            if not pid:
                raise ValueError('%s requires a namespace; pid must be provided.' % cls.__name__)
            pid = pid.upper()
            namespace = pid
        return namespace

    @classmethod
    def build_key_name(cls, **kwargs):
        """ Builds the key_name for this model.

        raises ValueError if not KEY_NAME_FIELDS are provided.
        raises ValueError if a value for a KEY_NAME_FIELD is not provided.
        """
        if not cls.KEY_NAME_FIELDS:
            raise ValueError("No key name fields provided.")

        for field in cls.KEY_NAME_FIELDS:
            if field not in kwargs or not kwargs[field]:
                raise ValueError("%s is required" % field)

        return '-'.join([str(kwargs[field]).upper() for field in cls.KEY_NAME_FIELDS])

    @classmethod
    def build_key(cls, pid=None, **kwargs):
        """ Builds a key in the default namespace. """
        namespace = cls._get_namespace(pid)
        key = ndb.Key(pairs=[(cls.__name__, cls.build_key_name(**kwargs))], namespace=namespace)
        return key

