""" Model to manage Batch Deleting microsites. """

from datetime import datetime
from google.appengine.ext import ndb
from app.models import QueryResult


class BatchDeleteMeta(ndb.Model):
    """ Information about the batch delete file. """
    
    delete_id = ndb.StringProperty(required=True)
    pid = ndb.StringProperty(required=True)

    filename = ndb.StringProperty()

    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)

    @staticmethod
    def generate_new_key_name(pid):
        """ Generates a key_name using the supplied PID and the current UTC. """
        if not pid:
            raise ValueError('pid is required.')
        pid = pid.upper()
        date_str = datetime.utcnow().strftime('%Y%m%d-%H%M%S')
        return 'Delete-{}-{}'.format(pid, date_str)

    @classmethod
    def generate_new_key(cls, pid):
        """ Generates a key using the supplied PID and the current UTC. """
        key_name = cls.generate_new_key_name(pid)
        return cls.build_key(key_name)

    @classmethod
    def build_key(cls, key_name):
        """ Generates a key if supplied the key_name. """
        if not key_name:
            raise ValueError('key_name is required.')
        return ndb.Key(cls, key_name, namespace='')

    @classmethod
    def lookup_most_recent_query(cls):
        """ Query to lookup most recent entities, useful for Fantasm continuations. """
        return cls.query(namespace='').order(-cls.created)

    @classmethod
    def lookup_most_recent(cls, keys_only=False, count=25, cursor=None):
        """ Looks up recent BatchDeleteMeta jobs ordered by created date descending.

        @returns a QueryResult object.
        """
        qr = QueryResult('BatchDeleteMeta.lookup_most_recent')
        query = cls.lookup_most_recent_query()
        qr.results, qr.cursor, qr.has_more = query.fetch_page(count, keys_only=keys_only, start_cursor=cursor)
        return qr
