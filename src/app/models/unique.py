""" Model to build unique constraint. """

from google.appengine.api.namespace_manager.namespace_manager import get_namespace
from google.appengine.ext import ndb


class _Unique(ndb.Model):
    """ Implements a unique constraint leveraging key uniqueness. """

    @staticmethod
    def build_key_name(constraint_name, *values):
        """ Builds a key_name using the constaint_name and arbitrary args. """
        if not constraint_name:
            raise ValueError('constraint_name is required.')
        if len(values) == 0:
            raise ValueError('You must provide at least one value.')
        value_str = ':'.join(values)
        return '{}:{}'.format(constraint_name, value_str)


def set_unique(constraint_name, *values, **kwargs):
    """ Sets up a unique constraint.
    
    @param constaint_name the name of the constraint, used to partition the values
    @param *values a list of values that are part of the unique constraint
    @param **kwargs namespace the namespace to use instead of the namespace manager
    @raises UniqueConstraintViolatedException if the unique constaint is violated
    """
    @ndb.transactional
    def tx():
        """ Using a transaction to ensure that we win the insert race. """
        namespace = kwargs.get('namespace', get_namespace())
        key_name = _Unique.build_key_name(constraint_name, *values)
        key = ndb.Key(_Unique, key_name, namespace=namespace)
        existing_constraint = key.get()
        if existing_constraint:
            raise UniqueConstraintViolatedException('Constraint "%s" for values "%s" already exists.',
                                                    constraint_name,
                                                    ', '.join(values))
        unique = _Unique(key=key)
        unique.put()
    tx()


def clear_unique(constraint_name, *values, **kwargs):
    """ Clears a unique constraint. 

    @param constaint_name the name of the constraint, used to partition the values
    @param *values a list of values that are part of the unique constraint
    @param **kwargs namespace the namespace to use instead of the namespace manager
    """
    namespace = kwargs.get('namespace', get_namespace())
    key_name = _Unique.build_key_name(constraint_name, *values)
    key = ndb.Key(_Unique, key_name, namespace=namespace)
    key.delete()


class UniqueConstraintViolatedException(Exception):
    """ Raised if a unique constraint is violated. """
    pass
