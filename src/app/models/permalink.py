""" permalink for long form posts """
import hashlib
from google.appengine.ext import ndb

from app.models import BaseNDBModel

class PermalinkPostMapping(BaseNDBModel):
    """Permalink that is directly associated with a site long form post """
    USE_NAMESPACE = False
    KEY_NAME_FIELDS = ['permalink']

    pid = ndb.StringProperty(required=True)
    msid = ndb.StringProperty(required=True)
    post_id = ndb.StringProperty(required=True)
    permalink = ndb.TextProperty(required=True, indexed=False)

    publish_datetime = ndb.DateTimeProperty(required=True)

    @classmethod
    def build_key_name(cls, **kwargs):
        """ building the keyname based on given permalink """
        permalink = kwargs.get('permalink')
        if not permalink:
            raise ValueError('permalink is required to build key_name')

        return hashlib.sha256(permalink.encode('ascii', 'ignore').lower()).hexdigest()

