""" Models for accessing remote APIs. """

import datetime
import json
import logging
import urllib.parse
from google.appengine.api import urlfetch as google_urlfetch

import settings


class YouTubeRemoteApi:
    """ API end points for YouTube. """
    
    def __init__(self, urlfetch=None):
        """ Initializes, allowing urlfetch injection. """
        self.urlfetch = urlfetch or google_urlfetch
        
    @staticmethod
    def parse_videoid(url):
        """ Parses a videoid from a url. Returns None if none match. """
        (_, host, path, _, query_string, _) = urllib.parse.urlparse(url)
        if 'youtube.com' not in host.lower():
            return None
        # http://www.youtube.com/v/g9lJvt8KKHw?f=videos&app=youtube_gdata
        if path.startswith('/v/'):
            videoid = path[3:]
            if videoid.endswith('/'):
                videoid = videoid[:-1]
            return videoid.strip()
        # http://m.youtube.com/details?v=g9lJvt8KKHw
        # http://www.youtube.com/watch?v=g9lJvt8KKHw&feature=youtube_gdata_player
        qs_parts = query_string.split('&')
        for part in qs_parts:
            if part.startswith('v='):
                return part[2:].strip()
        return None

    @classmethod
    def get_full_url_from_shortened(cls, url):
        """ if the url is a 'youtu.be' style url fetch it, follow the redirect, and return the full url
        """
        (_, host, _, _, _, _) = urllib.parse.urlparse(url)
        if 'youtu.be' in host.lower():
            try:
                response = google_urlfetch.fetch(url, payload=None, method='HEAD', follow_redirects=True)
                if response.status_code == 200:
                    return response.final_url
                else:
                    logging.warning(
                        'YouTube does not recognize your shortened url (%s) so it cannot be lengthened', url
                    )
                    return url
            except Exception:
                # This is executed in a Fantasm machine and will automatically restart
                logging.exception('Could not fetch YouTube url for shortened(%s)', url)
        else:
            return url

    def get_video_details(self, videoid):
        """ Retries the video details. """
        if not videoid:
            raise ValueError('videoid is required.')
        url = 'https://www.googleapis.com/youtube/v3/videos?id=%s&key=%s&part=snippet,contentDetails' % \
              (videoid, settings.GOOGLE_API_KEY)
        try:
            response = self.urlfetch.fetch(url, deadline=10)
        except Exception as e:
            logging.error('Failure accessing YouTube.', exc_info=True)
            raise YouTubeRemoteApiFailure(inner_exception=e)

        if response.status_code == 404:
            return None
        if response.status_code >= 400:
            message = 'Bad status code %d from YouTube. %s' % (response.status_code, response.content)
            logging.error(message)
            raise YouTubeRemoteApiFailure(message)
            
        try:
            results = json.loads(response.content)
        except Exception:
            message = 'Failure to parse JSON response from YouTube. JSON: "%s"' % response.content
            logging.error(message, exc_info=True)
            raise YouTubeRemoteApiFailure('Failed to parse response from YouTube.')
        
        return RawYouTubeVideo(results)


class RawYouTubeVideo:
    """
    Parses interesting values from the video details.
    See api documentation for more info on parsing video details:
    https://developers.google.com/youtube/v3/docs/videos
    """
    
    def __init__(self, values_dict):
        """ Initialize. """
        if values_dict is None:
            raise ValueError('values_dict is required.')

        videos = values_dict.get('items', [])
        self.values_dict = videos[0] if len(videos) > 0 else {}

    @cached_property
    def caption(self):
        """ Caption """
        return self.values_dict.get('snippet', {}).get('description')
        
    @cached_property
    def title(self):
        """ Title """
        return self.values_dict.get('snippet', {}).get('title')
        
    @cached_property
    def duration(self):
        """ Human-readable duration. """
        duration = self.values_dict.get('contentDetails', {}).get('duration')
        if not duration:
            return None

        if duration.startswith('PT'):
            duration = duration[2:]

        hours, _, duration = duration.rpartition('H')
        hours = hours and int(hours) or 0
        minutes, _, duration = duration.rpartition('M')
        minutes = minutes and int(minutes) or 0
        seconds, _, duration = duration.rpartition('S')
        seconds = seconds and int(seconds) or 0

        duration_format = '%Ss'
        if hours:
            duration_format = '%H:%M:%S'
        elif minutes:
            duration_format = '%M:%S'

        duration_time = datetime.time(hours, minutes, seconds)
        return duration_time.strftime(duration_format)
        
    @cached_property
    def large_thumbnail_url(self):
        """ Large thumbnail """
        return self.values_dict.get('snippet', {}).get('thumbnails', {}).get('high', {}).get('url')
        
    @cached_property
    def small_thumbnail_url(self):
        """ Small thumbnail """
        return self.values_dict.get('snippet', {}).get('thumbnails', {}).get('default', {}).get('url')
        
    @cached_property
    def mobile_video_url(self):
        """ Url for linking to mobile video """
        videoid = self.values_dict.get('id')
        if videoid:
            return 'https://m.youtube.com/watch?v=' + videoid
        return None


class YouTubeRemoteApiFailure(Exception):
    """ Raised if YouTube remote APIs fail. """

    def __init__(self, message=None, inner_exception=None):
        """ Initialize """
        self.inner_exception = inner_exception
        if inner_exception:
            super().__init__(inner_exception.args[0])
        else:
            super().__init__(message)
