""" Models dealing with addon activations. """
from google.appengine.ext import ndb

from app.models import BaseNDBModel
from enum import Enum


class AddonActivationResolutionStatus(str, Enum):
    """ Potential states for an addon activation to be in. """
    SUCCEEDED = 'Succeeded'
    REJECTED = 'Rejected'
    PENDING = 'Pending'

class AddonActivationModel(BaseNDBModel):
    """ Model to save the status of an addon activation. """
    KEY_NAME_FIELDS = ['activation_id']

    account_group_id = ndb.StringProperty(required=True)
    activation_id = ndb.StringProperty(required=True)
    addon_id = ndb.StringProperty(required=True)
    resolution_status = ndb.StringProperty(choices=[s.value for s in AddonActivationResolutionStatus],
                                           default=AddonActivationResolutionStatus.PENDING.value)
    rejection_message = ndb.StringProperty(default="")

    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)
