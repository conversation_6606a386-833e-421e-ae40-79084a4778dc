""" Models for analytics reports. """

from datetime import date
from google.appengine.ext import ndb
from google.appengine.api.namespace_manager import namespace_manager
from app.models import QueryResult

FREQUENCY_MONTHLY = 'Monthly'
FREQUENCY_CHOICES = [FREQUENCY_MONTHLY]

class AnalyticsReport(ndb.Model):
    """ A compiled analytics report for a partner. """
    
    pid = ndb.StringProperty(required=True)
    start_date = ndb.DateProperty(required=True)
    end_date = ndb.DateProperty(required=True)
    blobkey = ndb.BlobKeyProperty(required=True)
    frequency = ndb.StringProperty(required=True, default=FREQUENCY_MONTHLY, choices=FREQUENCY_CHOICES)
    
    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)
    
    @staticmethod
    def build_key_name(start_date, frequency=FREQUENCY_MONTHLY):
        """ Builds a key_name. """
        if not start_date:
            raise ValueError('start_date is required.')
        if not isinstance(start_date, date):
            raise ValueError('start_date must be a datetime.date.')
        if not frequency:
            raise ValueError('frequency is required.')
        if frequency not in FREQUENCY_CHOICES:
            raise ValueError('frequency must be in %s.' % FREQUENCY_CHOICES)
        return 'AnalyticsReport-%s-%d-%02d-%02d' % (frequency, start_date.year, start_date.month, start_date.day)
        
    @classmethod
    def build_key(cls, start_date, frequency=FREQUENCY_MONTHLY, pid=None):
        """ Builds a key. """
        key_name = cls.build_key_name(start_date, frequency=frequency)
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('AnalyticsReport key cannot be built in default namespace "". pid argument must ' + \
                             'supplied.')
        pid = pid.upper()
        return ndb.Key(cls, key_name, namespace=pid)
        
    @classmethod
    def lookup_all_query(cls, pid=None):
        """ Query to lookup all AnalyticsReport entities for a pid. """
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('AnalyticsReport cannot be looked up in the default namespace "". pid argument must ' + \
                             'be supplied.')
        pid = pid.upper()
        return cls.query(namespace=pid).order(-cls.created)
        
    @classmethod
    def lookup_all(cls, pid=None, keys_only=False, count=100, cursor=None):
        """ Looks up all the AnalyticsReport entities for a pid. """
        qr = QueryResult('AnalyticsReport.lookup_all')
        query = cls.lookup_all_query(pid=pid)
        qr.results, qr.cursor, qr.has_more = query.fetch_page(count, keys_only=keys_only, start_cursor=cursor)
        return qr
