""" Models for posts. """
from datetime import datetime
from google.appengine.api.namespace_manager import namespace_manager
from google.appengine.ext import ndb

from app.models import BaseNDBModel
from app.models.blob_mappings import BlobMapping


class Post(BaseNDBModel):
    """ Long form post model. """
    KEY_NAME_FIELDS = ['post_id']

    post_id = ndb.StringProperty(required=True)
    msid = ndb.StringProperty(required=True)
    pid = ndb.StringProperty(required=True)

    title = ndb.StringProperty(required=True, indexed=False)
    content = ndb.TextProperty(indexed=False)
    hidden = ndb.BooleanProperty(required=False, default=False)

    publish_datetime = ndb.DateTimeProperty(required=True)
    post_slug = ndb.StringProperty(required=True) # year/month/day/slugified-title
    permalink = ndb.TextProperty(required=True)

    next_post_id = ndb.TextProperty()
    prev_post_id = ndb.TextProperty()

    @classmethod
    def build_key(cls, **kwargs):
        """ Builds a key for a Post, using the pid namespace (defaults to namespace manager's current namespace). """
        post_id = kwargs.get('post_id')
        if not post_id:
            raise ValueError('post_id is required.')
        pid = kwargs.get('pid') or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Post Key cannot be created in the default namespace; pid must be provided.')
        pid = pid.upper()
        return ndb.Key(cls.__name__, cls.build_key_name(post_id=post_id), namespace=pid)
        
    @classmethod
    def lookup_all_query(cls, pid=None):
        """ Returns the query for lookup_all, useful for Fantasm continuations. """
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Post cannot be retrieved in the default namespace.')
        pid = pid.upper()
        return cls.query(namespace=pid).order('-created')


class PostidBlobMapping(BlobMapping):
    """ A mapping between post and blob """
    msid = ndb.StringProperty(required=True)

    # The blob key for the image blob being used on the microsite post with the id post_id
    blobkey = ndb.BlobKeyProperty(required=True)

    # The post id for the microsite containing the image with the key blobkey
    # If this field is empty, it means core has not yet created the post and will be filled by the image mapping cron
    # job when it is available.
    post_id = ndb.StringProperty()

    # Core's social post id for core's post associated with this mapping
    social_post_id = ndb.StringProperty()

    # The date this mapping should become permanent.
    # - If the post it is associated with failed it will be removed
    # - If the post it is associated with succeeded then scheduled_date is cleared
    # - If the post it is associated with is still pending the scheduled date is updated to a future date
    scheduled_date = ndb.DateTimeProperty()

    @staticmethod
    def build_key_name():
        """ Builds random key_name for this model. """
        key_name = 'PBM-' + tinyid.TinyIDGenerator(namespace='PBM', tinyid_len=12).generate_tinyid().upper()
        return key_name

    @classmethod
    def build_key(cls, pid=None):
        """ Builds a key in the current namespace. Cannot be the default namespace. """
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Cannot create a PostidBlobMapping in the default namespace.')
        pid = pid.upper()
        key_name = cls.build_key_name()
        return ndb.Key(cls, key_name, namespace=pid)

    @classmethod
    def lookup_by_blobkey(cls, blobkey, pid=None, keys_only=False, limit=1000):
        """ Returns a list of mappings for a given blobkey. """
        if not blobkey:
            raise ValueError('blobkey is required.')
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Post and blob mapping cannot be retrieved in the default namespace.')
        pid = pid.upper()
        return cls.query(namespace=pid).filter(cls.blobkey == blobkey).fetch(limit=limit, keys_only=keys_only)

    @classmethod
    def lookup_by_post_id(cls, post_id, pid=None, keys_only=False, limit=1000):
        """ Returns a list of mappings for a given post_id. """
        if not post_id:
            raise ValueError('post_id is required.')
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Post and blob mapping cannot be retrieved in the default namespace.')
        pid = pid.upper()
        return cls.query(namespace=pid).filter(cls.post_id == post_id).fetch(limit=limit, keys_only=keys_only)

    @classmethod
    def lookup_by_social_post_id(cls, social_post_id, pid=None, keys_only=False, limit=1000):
        """ Returns a list of mappings for a given social_post_id. """
        if not social_post_id:
            raise ValueError('social_post_id is required.')
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Post and blob mapping cannot be retrieved in the default namespace.')
        pid = pid.upper()
        return cls.query(namespace=pid).filter(cls.social_post_id == social_post_id)\
            .fetch(limit=limit, keys_only=keys_only)


    @classmethod
    def lookup_by_passed_scheduled_date(cls, pid=None, keys_only=False, limit=1000):
        """ Returns a list of mappings . """
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Post and blob mapping cannot be retrieved in the default namespace.')
        pid = pid.upper()
        return cls.query(namespace=pid).filter(cls.scheduled_date != None)\
            .filter(cls.scheduled_date <= datetime.utcnow()).fetch(limit=limit, keys_only=keys_only)
