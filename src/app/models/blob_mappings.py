""" Models to manage Blob mappings. """

from google.appengine.api.namespace_manager import namespace_manager
from google.appengine.ext import ndb
from app.models import QueryResult
from app.constants import LOGO, FAVICON, SHORTCUT, DESKTOP_BACKGROUND, MOBILE_BACKGROUND

CATEGORIES = {
    LOGO : 'Logo (max 200x100)',
    FAVICON : 'Browser Favicon (16x16)',
    SHORTCUT : 'iPhone/Android Shortcut Icon (57x57)',
    DESKTOP_BACKGROUND : 'Desktop Background Image (min 1600x1600)',
    MOBILE_BACKGROUND : 'Mobile Background Image (min 500x500)'
}

PID_CATEGORIES = {
    FAVICON : 'Browser Favicon (16x16)',
    LOGO : 'Logo (max 200x100)',
    SHORTCUT : 'iPhone/Android Shortcut Icon (57x57)',
}

class BlobMapping(ndb.Model):
    """ Represents a mapping of an entity to a blobkey. """

    serving_url = ndb.StringProperty()
    width = ndb.IntegerProperty(indexed=False)
    height = ndb.IntegerProperty(indexed=False)

    # optional meta information for images that have been imported from external sources
    import_url = ndb.StringProperty()
    import_last_modified = ndb.StringProperty(indexed=False)
    import_etag = ndb.StringProperty(indexed=False)
    import_filename = ndb.StringProperty(indexed=False)
    import_md5_hash = ndb.StringProperty(indexed=False)

    uses_gcs = ndb.BooleanProperty(default=False)

    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)

    def to_dict(self):
        """ Returns a dictionary of all the properties of this model. """
        d = {}
        d['key'] = self.key
        for prop_name in self._properties:
            d[prop_name] = getattr(self, prop_name)
        return d

class MsidBlobMapping(BlobMapping):
    """ A mapping of msid/category to blobkeys."""

    blobkey = ndb.BlobKeyProperty(required=True)
    category = ndb.StringProperty(required=True, choices=list(CATEGORIES.keys()))
    msid = ndb.StringProperty(required=True)

    @staticmethod
    def build_key_name(msid, category):
        """ Builds the key_name for this model. """
        if not msid:
            raise ValueError('msid is required.')
        if not category:
            raise ValueError('category is required.')
        return msid.upper() + '-' + category.lower()

    @classmethod
    def build_key(cls, msid, category, pid=None):
        """ Builds a key in the current namespace. Cannot be the default namespace. """
        key_name = cls.build_key_name(msid, category)
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Cannot create a MsidBlobMapping in the default namespace.')
        pid = pid.upper()
        return ndb.Key(cls, key_name, namespace=pid)

    @staticmethod
    def get_msid_from_key(key):
        """ Returns the msid extracted from the key. """
        key_name = key.string_id()
        msid = key_name.rpartition('-')[0]
        return msid

    @classmethod
    def build_predictive_keys(cls, msid, pid=None):
        """ Builds a set of keys for each category. """
        keys = []
        for category in list(CATEGORIES.keys()):
            keys.append(cls.build_key(msid, category, pid=pid))
        return keys

    @classmethod
    def lookup_by_msid_query(cls, msid, pid=None):
        """ Returns the query for lookup_by_msid, useful for Fantasm continuations. """
        if not msid:
            raise ValueError('msid is required.')
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('MsidBlobMapping cannot be retrieved in the default namespace.')
        msid = msid.upper()
        pid = pid.upper()
        return cls.query(namespace=pid).filter(cls.msid == msid)

    @classmethod
    def lookup_by_msid(cls, msid, pid=None, keys_only=False, count=100, cursor=None):
        """ Lookup MsidBlobMapping entities by msid.

        @returns a QueryResult object
        """
        qr = QueryResult('MsidBlobMapping.lookup_by_msid')
        query = cls.lookup_by_msid_query(msid, pid=pid)
        qr.results, qr.cursor, qr.has_more = query.fetch_page(count, keys_only=keys_only, start_cursor=cursor)
        return qr

class PidBlobMapping(BlobMapping):
    """ A mapping of pid/category to blobkeys. """

    blobkey = ndb.BlobKeyProperty(required=True)
    category = ndb.StringProperty(required=True, choices=list(CATEGORIES.keys()))
    pid = ndb.StringProperty(required=True)

    @staticmethod
    def build_key_name(category):
        """ Builds the key_name for this model. """
        if not category:
            raise ValueError('category is required.')
        return category.lower()

    @classmethod
    def build_key(cls, category, pid=None):
        """ Builds a key . """
        key_name = cls.build_key_name(category)
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Cannot create a PidBlobMapping in the default namespace.')
        pid = pid.upper()
        return ndb.Key(cls, key_name, namespace=pid)

    @staticmethod
    def get_pid_from_key(key):
        """ Returns the pid extracted from the key. """
        return key.namespace()

    @classmethod
    def build_predictive_keys(cls, pid=None):
        """ Builds a set of keys for each category. """
        keys = []
        for category in list(PID_CATEGORIES.keys()):
            keys.append(cls.build_key(category, pid=pid))
        return keys
