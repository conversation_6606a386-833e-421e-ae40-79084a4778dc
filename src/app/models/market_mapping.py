"""
Market Mapping Model.
"""

from google.appengine.ext import ndb
from google.appengine.api.namespace_manager import namespace_manager
from app.models import QueryResult


class MarketMapping(ndb.Model):
    """ Market-specific information """

    pid = ndb.StringProperty(required=True)
    market_id = ndb.StringProperty(required=True)
    host = ndb.StringProperty(required=True)
    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)

    @staticmethod
    def build_key_name(market_id):
        """ Builds the key_name for this model. """
        if not market_id:
            raise ValueError('market_id is required.')
        return market_id.upper()

    @classmethod
    def build_key(cls, market_id, pid=None):
        """ Builds the key for this model. """
        key_name = cls.build_key_name(market_id)
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('MarketMapping Key cannot be created in the default namespace; pid must be provided.')
        pid = pid.upper()
        return ndb.Key(cls, key_name, namespace=pid)

    @classmethod
    def lookup_all_query(cls, pid=None):
        """ Returns the query for lookup_all, useful for Fantasm continuations. """
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('MarketMappings cannot be retrieved in the default namespace.')
        pid = pid.upper()
        return cls.query(namespace=pid).order(-cls.created)

    @classmethod
    def lookup_all(cls, pid=None, keys_only=False, count=100, cursor=None):
        """ Looks up all the market mappings in the given pid (or current namespace if no pid provided),
        ordered by created date descending.

        @returns a QueryResult object.
        """
        qr = QueryResult('MarketMapping.lookup_all')
        query = cls.lookup_all_query(pid=pid)
        qr.results, qr.cursor, qr.has_more = query.fetch_page(count, keys_only=keys_only, start_cursor=cursor)
        return qr

    @classmethod
    def get_by_host_and_partner_id(cls, host, partner_id):
        """ Returns MarketMapping that matches both provided host and partner_id """
        market_query = cls.query(cls.host == host, cls.pid == partner_id, namespace=partner_id)
        return market_query.get()
