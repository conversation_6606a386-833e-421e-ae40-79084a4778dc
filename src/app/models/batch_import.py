""" Model to manage Batch Imports """

from datetime import datetime
from google.appengine.ext import ndb
from app.models import QueryResult


class BatchImportMeta(ndb.Model):
    """ Tracks the state of a batch import file. """
    
    import_id = ndb.StringProperty(required=True)
    pid = ndb.StringProperty(required=True)
    filename = ndb.StringProperty()
    failed = ndb.StringProperty(name='failed', repeated=True)
    number_to_process = ndb.IntegerProperty(indexed=False, default=0)
    number_completed = ndb.IntegerProperty(indexed=False, default=0)
    info = ndb.IntegerProperty(indexed=False, default=0)
    warn = ndb.IntegerProperty(indexed=False, default=0)
    error = ndb.IntegerProperty(indexed=False, default=0)
    
    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)
    
    @staticmethod
    def generate_new_key_name(pid):
        """ Generates a key_name using the supplied PID and the current UTC. """
        if not pid:
            raise ValueError('pid is required.')
        pid = pid.upper()
        date_str = datetime.utcnow().strftime('%Y%m%d-%H%M%S')
        return 'Import-{}-{}'.format(pid, date_str)
        
    @classmethod
    def generate_new_key(cls, pid):
        """ Generates a key using the supplied PID and the current UTC. """
        key_name = cls.generate_new_key_name(pid)
        return cls.build_key(key_name)
        
    @classmethod
    def build_key(cls, key_name):
        """ Generates a key if supplied the key_name. """
        if not key_name:
            raise ValueError('key_name is required.')
        return ndb.Key(cls, key_name, namespace='')
        
    @classmethod
    def lookup_most_recent_query(cls):
        """ Query to lookup most recent entities, useful for Fantasm continuations. """
        return cls.query(namespace='').order(-cls.created)
        
    @classmethod
    def lookup_most_recent(cls, keys_only=False, count=25, cursor=None):
        """ Looks up recent BatchImportMeta jobs ordered by created date descending.
        
        @returns a QueryResult object.
        """
        qr = QueryResult('BatchImportMeta.lookup_most_recent')
        query = cls.lookup_most_recent_query()
        qr.results, qr.cursor, qr.has_more = query.fetch_page(count, keys_only=keys_only, start_cursor=cursor)
        return qr
        
class BatchImportLog(ndb.Model):
    """ A log of messages for a batch import. """
    import_id = ndb.StringProperty(required=True)
    linenum = ndb.IntegerProperty(required=True)
    pid = ndb.StringProperty(required=True)
    msid = ndb.StringProperty()
    pmsid = ndb.StringProperty() # using short name for index efficienct; 'name' didn't work with unit tests?!
    level = ndb.IntegerProperty(required=True, indexed=False)
    messages = ndb.StringProperty(repeated=True)
    
    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)
    
    @classmethod
    def build_key_name(cls, import_id, linenum):
        """ Builds the key name. """
        if not import_id:
            raise ValueError('import_id is required.')
        if linenum is None:
            raise ValueError('linenum is required.')
        linenum = int(linenum) # raises a ValueError for non-integer
        return 'ImportLog-%s-%d' % (import_id, linenum)
        
    @classmethod
    def build_key(cls, import_id, linenum):
        """ Builds a key. """
        key_name = cls.build_key_name(import_id, linenum)
        parent_key = BatchImportMeta.build_key(import_id)
        return ndb.Key(cls, key_name, namespace='', parent=parent_key)
        
    @classmethod
    def lookup_by_import_id_query(cls, import_id):
        """ Query for lookup_by_import_id. """
        if not import_id:
            raise ValueError('import_id is required.')
        parent_key = BatchImportMeta.build_key(import_id)
        return cls.query(namespace='', ancestor=parent_key).filter(cls.import_id == import_id).order(cls.linenum)
        
    @classmethod
    def lookup_by_import_id(cls, import_id, count=1000, keys_only=False, cursor=None):
        """ Lookup BatchImportLog entities by import_id. """
        qr = QueryResult('BatchImportLog.lookup_by_import_id')
        query = cls.lookup_by_import_id_query(import_id)
        qr.results, qr.cursor, qr.has_more = query.fetch_page(count, keys_only=keys_only, start_cursor=cursor)
        return qr


class BatchImport(ndb.Model):
    """ Keeps track of latest batch import status for a given pid/customerId. """
    
    pid = ndb.StringProperty(required=True)
    partner_microsite_identifier = ndb.StringProperty(required=True)
    last_import_id = ndb.StringProperty(required=True)
    msid = ndb.StringProperty(required=True)
    
    created = ndb.DateTimeProperty(auto_now_add=True)
    # no updated because we're using an overwriting put(), which will never update this field
    
    @staticmethod
    def build_key_name(pid, partner_microsite_identifier):
        """ Builds the key name for this model """
        if not pid:
            raise ValueError('pid is required.')
        if not partner_microsite_identifier:
            raise ValueError('partner_microsite_identifier is required.')
        return 'ImportMS-{}-{}'.format(pid.upper(), partner_microsite_identifier)
        
    @classmethod
    def build_key(cls, pid, partner_microsite_identifier):
        """ Builds a key in the default namespace. """
        key_name = cls.build_key_name(pid, partner_microsite_identifier)
        return ndb.Key(cls, key_name, namespace='')
