""" Models to manage URL mappings. """
import logging
from google.appengine.ext import ndb
from app import constants
from app.domain.core.location_page import remap_location_page_url, MicroSiteNotInDataStoreError
from app.domain.utils import slugify
from app.models import QueryResult


class HostPidMapping(ndb.Model):
    """ A mapping of host to partner ID."""
    host = ndb.StringProperty(required=True)
    pid = ndb.StringProperty(required=True)
    uses_slugs = ndb.BooleanProperty(default=True)
    is_default = ndb.BooleanProperty(default=False)
    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)

    @staticmethod
    def build_key_name(hostname):
        """ Builds the key_name for this model. """
        if not hostname:
            raise ValueError('hostname is required.')
        return hostname.lower()

    @classmethod
    def build_key(cls, hostname):
        """ Builds a key for this model. """
        key_name = cls.build_key_name(hostname)
        return ndb.Key(cls, key_name, namespace='')

    @classmethod
    def lookup_by_pid_query(cls, pid, uses_slugs=None):
        """ Returns the query for lookup_by_pid, useful for Fantasm continuations. """
        if not pid:
            raise ValueError('pid is required.')
        pid = pid.upper()
        query = cls.query(namespace='').filter(cls.pid == pid).order(-cls.created)
        if uses_slugs is not None:
            query = query.filter(cls.uses_slugs == uses_slugs)
        return query

    @classmethod
    def lookup_by_pid(cls, pid, uses_slugs=None, keys_only=False, count=100, cursor=None):
        """ Looks up mappings given a pid and an optional uses_slugs. """
        qr = QueryResult('HostPidMapping.lookup_by_pid')
        query = cls.lookup_by_pid_query(pid, uses_slugs=uses_slugs)
        qr.results, qr.cursor, qr.has_more = query.fetch_page(count, keys_only=keys_only, start_cursor=cursor)
        return qr

    @classmethod
    def get_default_host_for_partner_id(cls, partner_id):
        """ Looks up the default host for a given partner id """
        query = cls.query(namespace='').filter(cls.pid == partner_id, cls.is_default == True)
        return query.get()

    @classmethod
    def set_default_host_for_partner_id(cls, partner_id, new_default_host_name):
        """ Sets the default host for a given partner id to the new default host name"""
        new_default_host = cls.build_key(new_default_host_name).get()
        new_default_host.is_default = True

        hosts_to_put = [new_default_host]

        old_default_host = cls.get_default_host_for_partner_id(partner_id)
        if old_default_host:
            old_default_host.is_default = False
            hosts_to_put.append(old_default_host)

        return ndb.put_multi(hosts_to_put)

    @classmethod
    def lookup_by_host(cls, host):
        """
        :param host: The hostname to lookup
        :return: The host pid mapping entity
        """
        key = cls.build_key(host)
        return key.get()


class HostSlugMsidMapping(ndb.Model):
    """ A mapping of host and optional slug to microsite ID."""
    hostslug = ndb.StringProperty(required=True)
    pid = ndb.StringProperty(required=True)
    msid = ndb.StringProperty(required=True)

    redirect_hostslug = ndb.StringProperty(default=None)

    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)

    @staticmethod
    def build_key_name(host, slug=None):
        """ Builds the key_name for this model. """
        return HostSlugMsidMapping.compose_hostslug(host, slug=slug)

    @classmethod
    def build_key(cls, host, slug=None):
        """ Builds a key for this model. """
        key_name = cls.build_key_name(host, slug=slug)
        return ndb.Key(cls, key_name, namespace='')

    @staticmethod
    def compose_hostslug(host, slug=None):
        """ Builds a hostslug string given a host and an optional slug. """
        if not host:
            raise ValueError('host is required.')
        if not slug:
            return host.lower()
        return '{}/{}'.format(host.lower(), slug.lower())

    @classmethod
    def lookup_by_pid_and_msid_query(cls, pid, msid):
        """ Returns the query for lookup_by_pid_and_msid, useful for Fantasm continuations. """
        if not pid:
            raise ValueError('pid is required')
        if not msid:
            raise ValueError('msid is required')
        pid = pid.upper()
        msid = msid.upper()
        query = cls.query(ndb.AND(cls.pid == pid, cls.msid == msid), namespace='').order(-cls.created)
        return query

    @classmethod
    @ndb.tasklet
    def lookup_by_pid_and_msid_async(cls, pid, msid, keys_only=False, count=100, cursor=None):
        """ Returns a Future of a tuple of Keys and a cursor if no cursor is used the object will be None """
        qr = QueryResult('HostSlugMsidMapping.lookup_by_pid_and_msid')
        query = cls.lookup_by_pid_and_msid_query(pid, msid)
        qr.results, qr.cursor, qr.has_more = \
            yield query.fetch_page_async(count, keys_only=keys_only, start_cursor=cursor)
        raise ndb.Return(qr)

    @classmethod
    def lookup_by_pid_and_msid(cls, pid, msid, keys_only=False, count=100, cursor=None):
        """ Returns a tuple of Keys and a cursor if no cursor is used the object will be None """
        return cls.lookup_by_pid_and_msid_async(pid, msid, keys_only=keys_only, count=count, cursor=cursor).get_result()

    @classmethod
    def lookup_by_pid_query(cls, pid):
        """ Returns the query for lookup_by_pid, useful for Fantasm continuations. """
        if not pid:
            raise ValueError('pid is required.')
        pid = pid.upper()
        query = cls.query(namespace='').filter(cls.pid == pid).order(-cls.created)
        return query

    @classmethod
    def lookup_by_msid_query(cls, msid):
        """ Lookup by msid. """
        if not msid:
            raise ValueError('msid is required.')
        query = cls.query(namespace='').filter(cls.msid == msid).order(-cls.created)
        return query

    @classmethod
    def lookup_by_pid(cls, pid, keys_only=False, count=100, cursor=None):
        """ Looks up mappings given a pid and an optional uses_slugs. """
        qr = QueryResult('HostSlugMsidMapping.lookup_by_pid')
        query = cls.lookup_by_pid_query(pid)
        qr.results, qr.cursor, qr.has_more = query.fetch_page(count, keys_only=keys_only, start_cursor=cursor)
        return qr

    @classmethod
    def _build_potential_slug_candidates(cls, partner_host, slug_prefix, slug_suffixes):
        """
        :param partner_host: The partner host to create the slug for
        :param slug_prefix: The candidate slug prefix
        :param slug_suffixes: The candidate slug suffixes to attempt
        :return: A list of candidate slug tuples in the form of (key, slug)
        """
        logging.info('_build_potential_slug_candidates slug_prefix: %s', slug_prefix)
        initial_candidate = slugify(slug_prefix, max_length=constants.SLUG_MAX_LENGTH)
        logging.info('_build_potential_slug_candidates initial_candidate: %s', initial_candidate)
        logging.info('_build_potential_slug_candidates slug_suffixes: %s', slug_suffixes)

        candidates = [initial_candidate + '-' + slug_suffix for slug_suffix in slug_suffixes if slug_suffix]
        logging.info('_build_potential_slug_candidates candidates: %s', candidates)

        # The slug prefix should be tried before the slug_suffixes
        candidates.insert(0, initial_candidate)

        # slug reservations is a tuple of possible (key, slug)
        slug_reservations = []
        for potential in candidates:
            slug = slugify(potential, max_length=constants.SLUG_MAX_LENGTH)
            key = cls.build_key(partner_host, slug)
            slug_reservations.append((key, slug))

        return slug_reservations

    @classmethod
    def generate_slug(cls, partner_host, pid, msid, company_name, user_specified_slug, slug_suffixes):
        """
        :param partner_host: The partner host to create the slug for
        :param pid: The partner id
        :param company_name: The name of the company to use during slug generation
        :param msid: The microsite identifier
        :param user_specified_slug: The slug asked for by the user
        :param slug_suffixes: The slug suffixes to attempt
        :return: The slug that was created for the microsite
        """
        slug_prefix = user_specified_slug or company_name
        logging.info('generate_slug slug_prefix: %s', slug_prefix)
        if slug_prefix and slugify(slug_prefix, max_length=constants.SLUG_MAX_LENGTH):
            fall_back_slug = slugify("{}-{}".format(slug_prefix, msid[3:]), max_length=constants.SLUG_MAX_LENGTH)
            slug_reservations = cls._build_potential_slug_candidates(partner_host, slug_prefix, slug_suffixes)
        else:
            fall_back_slug = msid[3:]
            slug_reservations = [(HostSlugMsidMapping.build_key(partner_host, fall_back_slug), fall_back_slug)]

        # check for collisions
        collisions = ndb.get_multi([key for key, _ in slug_reservations])

        def txn():
            """
            :return: The host slug mapping entity
            """
            candidate_key, candidate_slug = (None, None)
            for index, candidate in enumerate(collisions):
                # if candidate is None, then there was not a collision
                if candidate is None:
                    # ensure that there was not a collision now that we are in a transaction
                    exists = slug_reservations[index][0].get()
                    if not exists:
                        candidate_key = slug_reservations[index][0]
                        candidate_slug = slug_reservations[index][1]
                    break

            if candidate_key is None:
                # use the fallback slug (msid)
                candidate_key = cls.build_key(partner_host, fall_back_slug)
                candidate_slug = fall_back_slug

            host_slug = cls.compose_hostslug(partner_host, slug=candidate_slug)
            entity = cls(key=candidate_key, hostslug=host_slug, pid=pid, msid=msid)
            entity.put()
            return entity

        # This must be a cross group transaction for the race condition case when get_multi does
        # not return a collision but the key.get within the transaction finds an entity
        return ndb.transaction(txn, xg=True)

    @staticmethod
    def update_mappings(old_mappings, hostname, slug=None):
        """ Update existing hostslug/msid mappings with new hostslug. """

        if not old_mappings:
            raise ValueError('mappings are required.')
        if not hostname:
            raise ValueError('hostname is required.')
        pids = {mapping.pid for mapping in old_mappings}
        if len(pids) != 1:
            raise ValueError('inconsistent pids %r' % pids)
        msids = {mapping.msid for mapping in old_mappings}
        if len(msids) != 1:
            raise ValueError('inconsistent msids %r' % msids)

        pid = list(pids)[0]
        msid = list(msids)[0]
        new_hostslug = HostSlugMsidMapping.compose_hostslug(hostname, slug=slug)

        try:
            remap_location_page_url(pid, msid, new_hostslug)
        except MicroSiteNotInDataStoreError:
            logging.exception("This host-slug does not have a matching MicroSite %r, %r, %r.", pid, msid, new_hostslug)
            return

        new_mappings = []
        for old_mapping in old_mappings:
            if old_mapping.hostslug == new_hostslug:
                continue

            old_mapping.redirect_hostslug = new_hostslug
            new_mappings.append(old_mapping)

        if len(new_mappings) > 0:
            key = HostSlugMsidMapping.build_key(hostname, slug=slug)
            new_mapping = HostSlugMsidMapping(key=key, pid=pid, msid=msid, hostslug=new_hostslug)
            new_mappings.append(new_mapping)

            return ndb.put_multi(new_mappings)
