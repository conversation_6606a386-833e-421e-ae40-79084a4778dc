""" Models for listings. """
from google.appengine.ext import ndb
from app.domain.validation import require_args


class SyndicationOrderSnapshotModel(ndb.Model):
    """
    Contains details for the latest syndication order accepted and seen by the user.
    """
    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)
    pid = ndb.ComputedProperty(lambda self: self.key.namespace())
    agid = ndb.ComputedProperty(lambda self: self.key.id())
    # pylint: disable=W0108
    # Lambda may not be necessary
    latest_order_id = ndb.ComputedProperty(lambda self: self.__class__.computePropertyORDERID(self))
    latest_syndication_order = ndb.JsonProperty()

    @classmethod
    @require_args
    def build_key(cls, agid, pid):
        """
        Build the key.
        """
        pid = pid.upper()
        return ndb.Key(cls, agid, namespace=pid)

    @classmethod
    def computePropertyORDERID(cls, syndication_order_snapshot):
        """
        :param syndication_order_snapshot:  SyndicationOrderSnapshotModel entity
        :return: If syndication order exists return its order id else None.
        """
        syndication_order = syndication_order_snapshot.latest_syndication_order or {}
        return syndication_order.get('orderId')
