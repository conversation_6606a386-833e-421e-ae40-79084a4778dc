""" Models for pages and navigation. """

from google.appengine.api.namespace_manager import namespace_manager
from google.appengine.ext import ndb
from google.appengine.ext.ndb import polymodel
from app.models.blob_mappings import BlobMapping
from app.constants import CUSTOM, TEMPLATES, \
    MAX_PAGEID_BLOB_MAPPINGS, MGS_PHOTOSWIPE, MOBILE_GALLERY_STYLES, \
    DGS_LIGHTBOX, DESKTOP_GALLERY_STYLES


class Page(polymodel.PolyModel):
    """ Parent class of all types of page templates. """
    pageid = ndb.StringProperty(required=True)
    title = ndb.StringProperty(indexed=False)
    h1 = ndb.StringProperty(indexed=False)  #pylint: disable=invalid-name
    meta_keywords = ndb.StringProperty(indexed=False)
    meta_description = ndb.StringProperty(indexed=False)
    template = ndb.StringProperty(required=True, default=CUSTOM, choices=TEMPLATES)
    top_content = ndb.TextProperty(indexed=False)
    bottom_content = ndb.TextProperty(indexed=False)
    msid = ndb.StringProperty(required=True)
    pid = ndb.StringProperty(required=True)
    
    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)
        
    @staticmethod
    def build_key_name(pageid):
        """ Builds a key_name using the pageid. """
        if not pageid:
            raise ValueError('pageid is required.')
        return pageid.upper()
        
    @classmethod
    def build_key(cls, pageid, msid, pid=None):
        """ Builds a key for a Page, using the pid namespace (defaults to namespace manager's current namespace). """
        key_name = cls.build_key_name(pageid)
        if not msid:
            raise ValueError('msid is required.')
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Page Key cannot be created in the default namespace; pid must be provided.')
        pid = pid.upper()
        return ndb.Key(cls, key_name, namespace=pid)

    @classmethod
    def lookup_all_query(cls, pid=None):
        """ Returns the query for lookup_all, useful for Fantasm continuations. """
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Page cannot be retrieved in the default namespace.')
        pid = pid.upper()
        return cls.query(namespace=pid).order(-cls.created)


class ReviewsPage(Page):
    """ A page template for review add. """
    pass  # no additional fields


class SingleReviewPage(Page):
    """ A page template for a single review. """
    pass  # no additional fields


class LongFormPage(Page):
    """ A page template with custom content. """
    pass  # no additional fields


class CustomPage(Page):
    """ A page template with custom content. """
    pass  # no additional fields


class ImagesPage(Page):
    """
    A page with images.

    The images for this page can be retrieved via the PagediBlobMapping model.
    """
    images = ndb.TextProperty(indexed=False, default="[]")
    mobile_gallery_style = ndb.StringProperty(required=True, default=MGS_PHOTOSWIPE, choices=MOBILE_GALLERY_STYLES)
    desktop_gallery_style = ndb.StringProperty(required=True, default=DGS_LIGHTBOX, choices=DESKTOP_GALLERY_STYLES)


class VideosPage(Page):
    """
    A page with videos.

    The video data is stored in a json format and is deserialized when creating the domain representation of the page.
    """
    videos = ndb.TextProperty(indexed=False, default="[]")


class CouponsPage(Page):
    """
    A page with coupons.

    The coupons data is stored in a json format and is deserialized when creating the domain representation of the page.
    """
    coupons = ndb.TextProperty(indexed=False, default="[]")


class ContactPage(Page):
    """ A page with contact information. The contacts are stored in json and deserialized when creating the
    domain representation of the page 
     """
    email = ndb.StringProperty()
    contacts = ndb.TextProperty(indexed=False, default="[]")


class PageidBlobMapping(BlobMapping):
    """ A mapping of pageid to blobkeys."""

    msid = ndb.StringProperty(required=True)
    # blobkey is not required for creating PageidBlobMappings
    blobkey = ndb.BlobKeyProperty()
    pageid = ndb.StringProperty(required=True)
    order = ndb.IntegerProperty(required=True, indexed=False)
    caption = ndb.StringProperty(indexed=False)
    alt_text = ndb.StringProperty(indexed=False)
    title = ndb.StringProperty(indexed=False)

    @staticmethod
    def build_key_name(pageid, index):
        """ Builds the key_name for this model. """
        if not pageid:
            raise ValueError('pageid is required.')
        if index < 0 or index > MAX_PAGEID_BLOB_MAPPINGS and not index:
            raise ValueError('index is required.')
        return pageid.upper() + '-' + str(index)

    @classmethod
    def build_key(cls, pageid, msid, index, pid=None):
        """ Builds a key in the current namespace. Cannot be the default namespace. """
        key_name = cls.build_key_name(pageid, index)
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Cannot create a PageidBlobMapping in the default namespace.')
        pid = pid.upper()
        parent_key = Page.build_key(pageid, msid, pid=pid)
        return ndb.Key(cls, key_name, namespace=pid, parent=parent_key)

    @classmethod
    def build_predictive_keys(cls, pageid, msid, pid=None):
        """ Builds a set of keys for the given pageid. """
        keys = []
        for index in range(MAX_PAGEID_BLOB_MAPPINGS):
            keys.append(cls.build_key(pageid, msid, index, pid=pid))
        return keys

    @classmethod
    def lookup_by_blobkey(cls, blobkey, pid=None, keys_only=False, limit=1000):
        """ Returns a list of mappings for a given blobkey. """
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Page cannot be retrieved in the default namespace.')
        pid = pid.upper()
        return cls.query(namespace=pid).filter(cls.blobkey == blobkey).fetch(limit=limit, keys_only=keys_only)
