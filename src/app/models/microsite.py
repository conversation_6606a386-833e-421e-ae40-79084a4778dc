""" Models for microsites. """

from google.appengine.api.namespace_manager import namespace_manager
from google.appengine.ext import ndb
from google.appengine.ext.deferred import deferred

from app.constants import MAX_NAVIGATION, PHONE_THEMES, DES<PERSON>TOP_THEMES, \
    PHONE_THEME, DESKTOP_THEME, LAYOUT_API_KEY, DEFAULT_LAYOUT, API_KEY_TO_LAYOUT, \
    DOCUMENTS_QUEUE, DEFERRED_ROOT, DEFAULT_COMPANY_NAME
from app.domain.constants import Keys
from app.domain.vbc import get_ms_pid
from app.layouts import LAYOUTS
from app.models import QueryResult
import vdatastore
from settings import ORIGIN_UNKNOWN
from vobject.objects.account_group import AccountGroup


class AccountGroupModel(vdatastore.VModelMixin, AccountGroup):
    """
    An account group vobject received from VBC's update-business-profile pubsub
    """

    account_group_id = vdatastore.StringProperty(indexed=True)
    partner_id = vdatastore.StringProperty(indexed=True, required=True)
    customer_identifier = vdatastore.StringProperty(indexed=True)
    social_profile_id = vdatastore.StringProperty(description='Social profile id')

    @property
    def key(self):
        """
        Returns our ndb.Key
        """
        return self.build_key(self.account_group_id)

    @classmethod
    def build_key(cls, account_group_id):
        """
        Build an account group key
        """
        return ndb.Key(cls.class_name(), account_group_id, namespace='')

    @classmethod
    def get(cls, account_group_id):
        """
        Returns the account group model
        """
        return cls.build_key(account_group_id).get()

    def post_put_hook(self, future):
        """
        Responsible for triggering the deletion of the account group if appropriate
        """
        if self.deleted_flag:
            self.key.delete()
        super().post_put_hook(future)


class Microsite(ndb.Model):
    """
    A model that represents the microsite product account.  Updated to pull data from the account group.
    """
    account_group = vdatastore.VObjectProperty(AccountGroupModel, required=True)
    account_origin = ndb.StringProperty(default=ORIGIN_UNKNOWN)
    pmsid = ndb.StringProperty()
    sso_token = ndb.StringProperty()
    msid = ndb.StringProperty(required=True)
    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)
    is_lite = ndb.BooleanProperty(default=False, indexed=False)
    product_edition = ndb.StringProperty()
    active_date_time = ndb.DateTimeProperty()
    use_website_for_full_site_link = ndb.BooleanProperty(default=True, indexed=False)
    address2 = ndb.StringProperty(indexed=False)
    # for account activities
    login_count = ndb.IntegerProperty(default=0)
    last_login = ndb.DateTimeProperty()
    login_history = ndb.DateTimeProperty(repeated=True)
    color = ndb.StringProperty(indexed=False)
    welcomed_flag = ndb.BooleanProperty(default=False)

    #NAP Data suppression for My Listings
    show_address = ndb.BooleanProperty(default=True, indexed=False)
    show_city = ndb.BooleanProperty(default=True, indexed=False)
    show_region = ndb.BooleanProperty(default=True, indexed=False)
    show_zip = ndb.BooleanProperty(default=True, indexed=False)
    show_phone_number = ndb.BooleanProperty(default=True, indexed=False)
    full_screen = ndb.BooleanProperty(default=False, indexed=False)

    announcement = ndb.StringProperty(indexed=False)

    billing_code = ndb.StringProperty(indexed=False)

    phone_theme = ndb.StringProperty(choices=PHONE_THEMES,
                                     default=LAYOUTS[API_KEY_TO_LAYOUT[DEFAULT_LAYOUT]][PHONE_THEME])
    desktop_theme = ndb.StringProperty(choices=DESKTOP_THEMES,
                                       default=LAYOUTS[API_KEY_TO_LAYOUT[DEFAULT_LAYOUT]][DESKTOP_THEME])

    # Letting microsites keep their hours of operation for now.  We updated the message they are sending to VBC.
    # Hopefully eventually we can swap over so they use the account group model
    hours_of_operation = ndb.TextProperty(indexed=False, default="{}")

    agid = ndb.ComputedProperty(lambda self: self.account_group.account_group_id)
    pid = ndb.ComputedProperty(lambda self: get_ms_pid(self.account_group.partner_id))
    spid = ndb.ComputedProperty(lambda self: self.account_group.social_profile_id)
    market_id = ndb.ComputedProperty(lambda self: self.account_group.market_id)
    name = ndb.ComputedProperty(lambda self: self.account_group.company_name) # site name, business name, etc.
    customer_identifier = ndb.ComputedProperty(lambda self: self.account_group.customer_identifier)
    tax_id = ndb.ComputedProperty(lambda self: self.account_group.tax_ids, repeated=True)

    hide_from_robots = ndb.BooleanProperty(default=False)
    chat_widget_id = ndb.StringProperty()

    @property
    def phone_work(self):  # pylint: disable=missing-docstring
        return self.account_group.work_number

    @property
    def phone(self):  # pylint: disable=missing-docstring
        return self.account_group.work_number[0] if self.account_group.work_number else None

    @property
    def phone_call_tracking(self):  # pylint: disable=missing-docstring
        return self.account_group.call_tracking_number

    @property
    def email(self):  # pylint: disable=missing-docstring
        return self.account_group.email

    @property
    def website(self):  # pylint: disable=missing-docstring
        return self.account_group.website

    @property
    def address1(self):  # pylint: disable=missing-docstring
        return self.account_group.address

    @property
    def city(self):  # pylint: disable=missing-docstring
        return self.account_group.city

    @property
    def state(self):  # pylint: disable=missing-docstring
        return self.account_group.state

    @property
    def zipcode(self):  # pylint: disable=missing-docstring
        return self.account_group.zip

    @property
    def country(self):  # pylint: disable=missing-docstring
        return self.account_group.country

    @property
    def place(self):  # pylint: disable=missing-docstring
        return self.account_group.place

    @property
    def blurb(self):  # pylint: disable=missing-docstring
        return self.account_group.tagline

    @property
    def geo(self):  # pylint: disable=missing-docstring
        return self.set_geo_point(self.account_group.latitude, self.account_group.longitude)

    @property
    def twitter_url(self):  # pylint: disable=missing-docstring
        return self.account_group.twitter_url

    @property
    def facebook_url(self):  # pylint: disable=missing-docstring
        return self.account_group.facebook_url

    @property
    def rss_url(self):  # pylint: disable=missing-docstring
        return self.account_group.rss_url

    @property
    def youtube_url(self):  # pylint: disable=missing-docstring
        return self.account_group.youtube_url

    @property
    def linkedin_url(self):  # pylint: disable=missing-docstring
        return self.account_group.linkedin_url

    @property
    def instagram_url(self):  # pylint: disable=missing-docstring
        return self.account_group.instagram_url

    @property
    def pinterest_url(self):  # pylint: disable=missing-docstring
        return self.account_group.pinterest_url

    @property
    def foursquare_url(self):  # pylint: disable=missing-docstring
        return self.account_group.foursquare_url

    @property
    def googleplus_url(self):  # pylint: disable=missing-docstring
        return self.account_group.googleplus_url

    @property
    def categories(self):  # pylint: disable=missing-docstring
        return self.account_group.seo_category

    def __init__(self, **kwargs):
        """
        Do some fancy initialization where we pass stuff to the account group object on the microsite instead of
        just directly to the microsite.
        """
        account_group_kwargs, kwargs = self.extract_account_group_kwargs(kwargs)
        ms_account_group = None
        if account_group_kwargs.get('account_group_id'):
            ms_account_group = AccountGroupModel.build_key(account_group_kwargs['account_group_id']).get()
            # ToDo: why rebuilding the AG?
            if ms_account_group:
                ms_account_group = AccountGroupModel(**ms_account_group.to_dict())
        if not ms_account_group:
            ms_account_group = AccountGroupModel(**account_group_kwargs)
            ms_account_group.version = 0
        kwargs['account_group'] = ms_account_group

        # ensure the account's sso token is equal to the msid if a token isn't specifically provided
        if not kwargs.get('sso_token'):
            kwargs['sso_token'] = kwargs.get('msid')

        super().__init__(**kwargs)

    @classmethod
    def extract_account_group_kwargs(cls, kwargs):
        """
        Extract the kwargs that are going to be passed to the account group
        :param kwargs:
        :return:
        """
        account_group_kwargs = {}
        if kwargs.get('geo'):
            kwargs['geo'] = {'lat': kwargs.get('geo').lat, 'lon': kwargs.get('geo').lon}
        for ms_name, account_group_name in ACCOUNT_GROUP_KWARG_MAPPING.items():
            if ms_name in list(kwargs.keys()):
                account_group_kwargs[account_group_name] = kwargs.pop(ms_name)
        agid = kwargs.pop('agid', None)
        if agid:
            account_group_kwargs['account_group_id'] = agid
        phone = kwargs.pop('phone', None)
        work_number = account_group_kwargs.get('work_number') or []
        if phone and phone not in work_number:
            work_number.insert(0, phone)
            account_group_kwargs['work_number'] = work_number
        if not account_group_kwargs.get('company_name'):
            account_group_kwargs['company_name'] = DEFAULT_COMPANY_NAME
        return account_group_kwargs, kwargs

    @staticmethod
    def set_geo_point(lat, lon):
        """
        Used for the computedProperty, to return a proper geo point for geo
        :param lat: latitude
        :param lon: longitude
        :return: an ndb GeoPt
        """
        if not lat or not lon:
            return None
        else:
            return ndb.GeoPt(lat, lon)

    @staticmethod
    def build_key_name(msid):
        """ Builds the key_name for this model. """
        if not msid:
            raise ValueError('msid is required.')
        return msid.upper()

    @classmethod
    def _get_kind(cls):
        """
        override _get_kind for transfer
        """
        return 'MicrositeModel'

    @classmethod
    def build_key(cls, msid, pid=None):
        """ Builds the key for this model. """
        key_name = cls.build_key_name(msid)
        pid = get_ms_pid(pid) or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Microsite Key cannot be created in the default namespace; pid must be provided.')
        pid = pid.upper()
        return ndb.Key(cls, key_name, namespace=pid)

    @property
    def key(self):
        """
        Returns our ndb.Key
        """
        return self.build_key(self.msid, self.pid)

    @classmethod
    def get_by_key(cls, key):
        """
        allows ut to look up a microsite without doing a key.get.. so we can do a lazy load.
        :return: a microsite associated with the given key
        """
        pid = key._Key__namespace
        msid = key._Key__pairs[0][1]
        return cls.get_by_msid(msid, pid)

    @classmethod
    def lookup_by_pmsid(cls, pmsid, pid=None):
        """ Returns a microsite with the given pmsid in the pid namespace """
        pid = get_ms_pid(pid) or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Microsite cannot be retrieved in the default namespace.')
        pid = pid.upper()

        return cls.query(namespace=pid).filter(cls.pmsid == pmsid).get()

    @classmethod
    def lookup_by_customer_identifier(cls, customer_identifier, pid=None):
        """ Returns a microsite with the given customer_identifier in the pid namespace """
        pid = get_ms_pid(pid) or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Microsite cannot be retrieved in the default namespace.')
        pid = pid.upper()

        return cls.query(namespace=pid).filter(cls.customer_identifier == customer_identifier).get()

    @classmethod
    def lookup_by_sso_token(cls, sso_token, pid=None):
        """ Returns a microsite with the given sso_token in the pid namespace """
        pid = get_ms_pid(pid) or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Microsite cannot be retrieved in the default namespace.')
        pid = pid.upper()

        return cls.query(namespace=pid).filter(cls.sso_token == sso_token).get()

    def to_dict(self):
        """ Returns a dictionary of all the properties of this model. """
        d = super().to_dict()
        d['phone_work'] = self.phone_work
        d['phone'] = self.phone
        d['phone_call_tracking'] = self.phone_call_tracking
        d['email'] = self.email
        d['website'] = self.website
        d['address1'] = self.address1
        d['address2'] = self.address2 or (self.account_group.address2 if self.account_group else None)
        d['city'] = self.city
        d['state'] = self.state
        d['zipcode'] = self.zipcode
        d['country'] = self.country
        d['place'] = self.place
        d['blurb'] = self.blurb
        d['geo'] = self.geo
        d['hide_from_robots'] = self.hide_from_robots
        d['twitter_url'] = self.twitter_url
        d['facebook_url'] = self.facebook_url
        d['rss_url'] = self.rss_url
        d['youtube_url'] = self.youtube_url
        d['linkedin_url'] = self.linkedin_url
        d['instagram_url'] = self.instagram_url
        d['pinterest_url'] = self.pinterest_url
        d['foursquare_url'] = self.foursquare_url
        d['googleplus_url'] = self.googleplus_url
        d['categories'] = self.categories
        d['layout'] = self.layout
        return d

    @classmethod
    def get_by_msid(cls, msid, pid=None):
        """ Get a microsite with the given msid in the pid namespace """
        key = cls.build_key(msid, pid=pid)
        return key.get()

    @property
    def layout(self):
        """
        :return: same standard as what expected on create API
        """
        layout_api_key = DEFAULT_LAYOUT
        for _, lo_dict in list(LAYOUTS.items()):
            if self.phone_theme == lo_dict[PHONE_THEME] and self.desktop_theme == lo_dict[DESKTOP_THEME]:
                layout_api_key = lo_dict[LAYOUT_API_KEY]
                break

        return layout_api_key

    @classmethod
    def lookup_all_query(cls, pid=None):
        """ Returns the query for lookup_all, useful for Fantasm continuations. """
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Microsite cannot be retrieved in the default namespace.')
        pid = pid.upper()
        return cls.query(namespace=get_ms_pid(pid)).order(-cls.created)

    @classmethod
    def get_microsite_count_for_partner(cls, pid=None):
        """ Retrieves a count of the total number of Microsites for a given partner """
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Microsite cannot be retrieved in the default namespace.')
        pid = pid.upper()
        return cls.query(namespace=get_ms_pid(pid)).count(limit=None, keys_only=True)

    @classmethod
    def lookup_by_agid(cls, agid, pid=None, keys_only=False):
        """ Returns a microsite with the given agid in the pid namespace """
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Microsite cannot be retrieved in the default namespace.')
        pid = pid.upper()

        return cls.query(namespace=get_ms_pid(pid)).filter(cls.agid == agid).get(keys_only=keys_only)

    @classmethod
    def lookup_all(cls, pid=None, keys_only=False, count=100, cursor=None):
        """ Looks up all the microsites in the given pid (or current namespace if no pid provided),
        ordered by created date descending.

        @returns a QueryResult object.
        """
        qr = QueryResult('Microsite.lookup_all')
        query = cls.lookup_all_query(pid=pid)
        qr.results, qr.cursor, qr.has_more = query.fetch_page(count, keys_only=keys_only, start_cursor=cursor)
        return qr

    @classmethod
    def get_new_microsite_count_for_partner_within_given_time_period(cls, starting_time, ending_time, pid=None):
        """ Retrieves number of microsite count for a given partner within given time period """
        pid = get_ms_pid(pid) or namespace_manager.get_namespace()
        if not pid:
            raise ValueError("Microsite cannot be retrieved in the default namespace")
        if not starting_time:
            raise ValueError("starting_time is required")
        if not ending_time:
            raise ValueError("ending_time is required")
        pid = pid.upper()

        return cls.query(namespace=pid)\
            .filter(cls.created >= starting_time).filter(cls.created < ending_time).count(limit=None, keys_only=True)

    def _post_put_hook(self, future):
        from app.domain.search_documents import add_microsite_search_document
        defer_url = Keys.DEFER_URL_ROOT + 'MicroSite/AddSearchDocument/'
        deferred.defer(add_microsite_search_document, self, _url=defer_url,
                       _queue=DOCUMENTS_QUEUE, _transactional=ndb.in_transaction())
        super()._post_put_hook(future)

    @classmethod
    def _post_delete_hook(cls, key, future):
        from app.domain.search_documents import remove_microsite_search_document
        defer_url = DEFERRED_ROOT + 'MicroSite/RemoveSearchDocument/'
        deferred.defer(remove_microsite_search_document, key.id(), key.namespace(), _url=defer_url, _queue='documents')


class MicrositeTombstone(ndb.Model):
    """ Records basic information about a deleted microsite """
    msid = ndb.StringProperty(required=True)
    account_group_id = ndb.StringProperty()
    pid = ndb.StringProperty(required=True)
    account_origin = ndb.StringProperty(default=ORIGIN_UNKNOWN)
    pmsid = ndb.StringProperty()  # the partner identifier for this microsite
    name = ndb.StringProperty(indexed=False)  # site name, business name, etc.
    created = ndb.DateTimeProperty(auto_now_add=True)  # indicates when this tombstone entity was created
    updated = ndb.DateTimeProperty(auto_now=True)

    is_lite = ndb.BooleanProperty(default=False, indexed=False)
    active_date_time = ndb.DateTimeProperty()

    # required for unified billing
    original_created = ndb.DateTimeProperty()  # indicates when the original microsite was created
    customer_identifier = ndb.StringProperty()  # (CustomerId in the UI)
    address1 = ndb.StringProperty(indexed=False)
    address2 = ndb.StringProperty(indexed=False)
    city = ndb.StringProperty(indexed=False)
    state = ndb.StringProperty(indexed=False)
    zipcode = ndb.StringProperty(indexed=False)
    country = ndb.StringProperty(indexed=False)
    email = ndb.StringProperty(indexed=False)
    billing_code = ndb.StringProperty(indexed=False)
    market_id = ndb.StringProperty(indexed=False)
    tax_id = ndb.StringProperty(indexed=False, repeated=True)
    phone_work = ndb.StringProperty(repeated=True)
    phone = ndb.ComputedProperty(lambda self: self.phone_work[0] if self.phone_work else None)
    # for account activities
    login_count = ndb.IntegerProperty(default=0)
    last_login = ndb.DateTimeProperty()
    login_history = ndb.DateTimeProperty(repeated=True)

    @staticmethod
    def build_key_name(msid):
        """ Builds the key_name for this model. """
        return Microsite.build_key_name(msid)

    @classmethod
    def build_key(cls, msid, pid=None):
        """ Builds the key for this model. """
        key_name = cls.build_key_name(msid)
        pid = get_ms_pid(pid) or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Microsite Key cannot be created in the default namespace; pid must be provided.')
        pid = pid.upper()
        return ndb.Key(cls, key_name, namespace=pid)

    @classmethod
    def get_count_for_partner(cls, pid):
        """
        Retrieves number of deleted microsites for a given partner
        """
        pid = get_ms_pid(pid) or namespace_manager.get_namespace()
        if not pid:
            raise ValueError("MicrositeTombstone cannot be retrieved in the default namespace")
        pid = pid.upper()
        return cls.query(namespace=pid).count(limit=None, keys_only=True)

    @classmethod
    def get_count_for_partner_within_given_time_period(cls, starting_time, ending_time, pid=None):
        """
        Retrieves number of deleted microsites for a given partner within a time period
        """
        pid = get_ms_pid(pid) or namespace_manager.get_namespace()
        if not pid:
            raise ValueError("Microsite cannot be retrieved in the default namespace")
        if not starting_time:
            raise ValueError("starting_time is required")
        if not ending_time:
            raise ValueError("ending_time is required")
        pid = pid.upper()

        return cls.query(namespace=pid)\
            .filter(cls.created >= starting_time).filter(cls.created < ending_time).count(limit=None, keys_only=True)


class Navigation(ndb.Model):
    """ Information to construct navigation elements for a microsite. """

    navigation_slug = ndb.StringProperty(required=True)
    pageid = ndb.StringProperty(required=True)
    name = ndb.StringProperty(required=True, indexed=False)
    order = ndb.IntegerProperty(required=True, indexed=False)
    msid = ndb.StringProperty(required=True)
    pid = ndb.StringProperty(required=True)
    icon = ndb.StringProperty(required=False)

    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)

    @staticmethod
    def build_key_name(msid, slot_num):
        """ Builds a key_name using the msid and the slot_num. Makes no checks to ensure the slot_num
        is available. The slot_num must be 1 <= slot_num <= MAX_NAVIGATION.
        """
        if not msid:
            raise ValueError('msid is required.')
        if not slot_num:
            raise ValueError('slot_num is required.')
        slot_num = int(slot_num)
        if slot_num < 1 or slot_num > MAX_NAVIGATION:
            raise ValueError('slot_num must be 1 <= slot_num <= %d.' % MAX_NAVIGATION)
        return '%s-N-%d' % (msid, slot_num)

    @classmethod
    def build_key(cls, msid, slot_num, pid=None):
        """ Builds a key object for msid and slot_num in the correct namespace with the correct parent.
        Makes no checks to ensure the slot_num is available. The slot_num must be 1 <= slot_num <= MAX_NAVIGATION.
        """
        key_name = cls.build_key_name(msid, slot_num)
        pid = pid or namespace_manager.get_namespace()
        if not pid:
            raise ValueError('Navigation key cannot be constructed in the default namespace.')
        return ndb.Key(cls, key_name, namespace=pid)

    @classmethod
    def build_predictive_keys(cls, msid, pid=None):
        """ Builds a set of predictive keys used to lookup Navigation entities in batch. """
        if not msid:
            raise ValueError('msid is required.')
        keys = []
        for n in range(1, MAX_NAVIGATION + 1):
            keys.append(cls.build_key(msid, n, pid=pid))
        return keys

    @classmethod
    def lookup_page_ids(cls, pid, msid):
        """ Returns the query for lookup_all"""
        pid = pid.upper()
        return cls.query(namespace=pid).filter(cls.msid == msid)


ACCOUNT_GROUP_KWARG_MAPPING = {
    'pid': AccountGroupModel.partner_id._name,
    'spid': AccountGroupModel.social_profile_id._name,
    'name': AccountGroupModel.company_name._name,
    'phone_work': AccountGroupModel.work_number._name,
    'phone_call_tracking': AccountGroupModel.call_tracking_number._name,
    'email': AccountGroupModel.email._name,
    'website': AccountGroupModel.website._name,
    'address1': AccountGroupModel.address._name,
    'city': AccountGroupModel.city._name,
    'state': AccountGroupModel.state._name,
    'country': AccountGroupModel.country._name,
    'zipcode': AccountGroupModel.zip._name,
    'place': AccountGroupModel.place._name,
    'twitter_url': AccountGroupModel.twitter_url._name,
    'facebook_url': AccountGroupModel.facebook_url._name,
    'rss_url': AccountGroupModel.rss_url._name,
    'youtube_url': AccountGroupModel.youtube_url._name,
    'linkedin_url': AccountGroupModel.linkedin_url._name,
    'instagram_url': AccountGroupModel.instagram_url._name,
    'pinterest_url': AccountGroupModel.pinterest_url._name,
    'foursquare_url': AccountGroupModel.foursquare_url._name,
    'googleplus_url': AccountGroupModel.googleplus_url._name,
    'customer_identifier': AccountGroupModel.customer_identifier._name,
    'market_id': AccountGroupModel.market_id._name,
    'tax_id': AccountGroupModel.tax_ids._name,
    'categories': AccountGroupModel.seo_category._name,
    'geo': AccountGroupModel.location._name,
    'blurb': AccountGroupModel.tagline._name,
}
