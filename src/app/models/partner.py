""" Models for partners. """
from datetime import datetime
from google.appengine.api import memcache
from google.appengine.ext import ndb
from app.domain import exceptions

from app.models import QueryResult, BaseNDBModel
from fantasm import startStateMachine
from settings import VSOCIAL_FB_APP_ID


class Partner(ndb.Expando):
    """ The top-level partner class. """
    pid = ndb.StringProperty(required=True)
    name = ndb.StringProperty(required=True)
    created = ndb.DateTimeProperty(auto_now_add=True)
    updated = ndb.DateTimeProperty(auto_now=True)

    website = ndb.StringProperty()
    contact_email_from = ndb.StringProperty(indexed=False)
    google_analytics_id = ndb.StringProperty()
    microsite_cache_time = ndb.IntegerProperty(default=0, indexed=False)
    strict_map = ndb.BooleanProperty(default=False, indexed=False)
    show_partner_footer = ndb.BooleanProperty(default=True, indexed=False)
    enable_sitemap = ndb.BooleanProperty(default=True, indexed=False)
    deleting = ndb.BooleanProperty(default=False, indexed=False)

    api_key = ndb.StringProperty()

    fb_app_id = ndb.StringProperty()
    spgid = ndb.StringProperty()

    angular_app_https = ndb.BooleanProperty(default=False, indexed=False)

    @staticmethod
    def build_key_name(pid):
        """ Builds the key_name for this model. """
        if not pid:
            raise ValueError('pid is required.')
        return pid.upper()

    @classmethod
    def build_key(cls, pid):
        """ Builds a key in the default namespace. """
        key_name = cls.build_key_name(pid)
        return ndb.Key(cls, key_name, namespace='')

    @classmethod
    def create(cls, pid, name, **kwargs):
        """ Creates a new partner. """
        if not pid:
            raise ValueError('pid is required.')
        if not name:
            raise ValueError('name is required.')
        pid = pid.upper()

        new_partner = cls.create_txn(pid, name, **kwargs)
        return new_partner

    @classmethod
    @ndb.transactional
    def create_txn(cls, pid, name, **kwargs):
        """ Creates a partner, ensuring that one with matching pid does not already exist. """
        key = Partner.build_key(pid)
        existing_partner = key.get()
        if existing_partner:
            raise exceptions.PartnerExistsException(pid)
        new_partner = cls(key=key, pid=pid, name=name, **kwargs)
        new_partner.put()
        return new_partner

    @classmethod
    @ndb.transactional
    def update(cls, pid, **kwargs):
        """ Updates an existing partner entity.

        @param pid: The id of the partner to update.
        @raises PartnerNotFoundException: If the partner is not found
        """
        if not pid:
            raise ValueError('pid is required.')
        pid = pid.upper()

        key = Partner.build_key(pid)
        partner = key.get()
        if not partner:
            raise exceptions.PartnerNotFoundException()

        for k, v in kwargs.items():
            setattr(partner, k, v)

        partner.put()
        #***********
        # NOTE: When the Partner is updated we need to flush memcache to ensure any cached Microsites are updated.
        # This should be a RARE occurrence otherwise an alternative solution should be devised.
        #***********
        memcache.flush_all()

        return partner

    @classmethod
    def get(cls, pid):
        """ get a partner via pid """
        return cls.build_key(pid).get()
        
    def to_dict(self):
        """ Returns a dictionary of all the properties of this model. """
        d = super().to_dict()
        if not self.spgid:
            d['spgid'] = self.social_profile_group_id
        if not self.fb_app_id:
            d['fb_app_id'] = VSOCIAL_FB_APP_ID
        return d

    @property
    def social_profile_group_id(self):
        """
        get spgid for a partner
        :return: String representation of a social profile group id
        """
        if not self.spgid:
            from app.domain.vbc import get_vbc_pid
            from app.domain.middleware.partner_whitelabel import get_partner_whitelabel_config
            vbc_pid = get_vbc_pid(self.pid)
            partner_config = get_partner_whitelabel_config(vbc_pid)
            if partner_config:
                self.spgid = partner_config.social_profile_group_id
        return self.spgid

    @classmethod
    def lookup_all_query(cls):
        """ Returns the query for lookup_all, useful for Fantasm continuations. """
        return cls.query(namespace='').order(-cls.created)

    @classmethod
    def lookup_all(cls, keys_only=False, count=100, cursor=None):
        """ Looks up all the partners, ordered by created date descending.

        @returns a QueryResult object
        """
        query = cls.lookup_all_query()
        qr = QueryResult('Partner.lookup_all')
        qr.results, qr.cursor, qr.has_more = query.fetch_page(count, keys_only=keys_only, start_cursor=cursor)

        return qr

    @classmethod
    def all_partners(cls, fetch_count=5000, keys_only=False):
        """
        returns all the partners
        """
        query = cls.lookup_all_query()
        qr = QueryResult('Partner.lookup_all')
        qr.results = [result for result in query.iter(limit=fetch_count, keys_only=keys_only)]
        return qr

    @classmethod
    def delete_partner(cls, pid):
        """ Deletes a partner along with all microsites and resources """
        if not pid:
            ValueError('PID is required')

        pid = pid.upper()
        partner_key = cls.build_key(pid)
        partner = partner_key.get()
        if not partner:
            raise ValueError('Partner with pid (%s) cannot be found', pid)
        partner.deleting = True
        partner.put()
        context = {'pid':pid}
        startStateMachine('RemoveFullPartner', context)

    @classmethod
    def validate_partner_api_key(cls, pid, apikey):
        """ This validates that the provided api_key matches the api_key on the given pid """
        partner = cls.get(pid)
        if partner and partner.api_key and apikey:
            return apikey == partner.api_key
        else:
            return False

    @classmethod
    def lookup_all_partners(cls, count=None, keys_only=False):
        """ Looks up up to 1000 partners and returns them as domain objects. """
        if count:
            result = cls.lookup_all(count=count, keys_only=keys_only)
        else:
            result = cls.all_partners(keys_only=keys_only)

        partners = result.results if result else []
        return partners


class PartnerBillingFile(BaseNDBModel):
    """ contains a list of blobstore files that contain monthly billing info for partners """
    KEY_NAME_FIELDS = ['pid', 'date_string']

    filename = ndb.StringProperty(required=True)
    billing_file_blob_key = ndb.BlobKeyProperty(required=True)
    num_accounts = ndb.IntegerProperty(required=True, indexed=False)
    creation_date = ndb.DateTimeProperty(required=True, indexed=False)

    @classmethod
    def build_key(cls, pid=None, **kwargs):
        """ Builds a key in the default namespace. """
        date_thing = kwargs.pop('creation_date')
        if isinstance(date_thing, datetime):
            date = date_thing.date()
        else:
            date = date_thing
        key = ndb.Key(pairs=[(cls.__name__, cls.build_key_name(pid=pid, date_string=str(date)))], namespace=pid)
        return key
