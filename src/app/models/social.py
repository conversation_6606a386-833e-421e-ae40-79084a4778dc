""" Sites social integration related models """

from google.appengine.ext import ndb

from app.models import BaseNDBModel


class MicrositeFBPageAssociation(BaseNDBModel):
    """ MicrositeFBPageAssociation records association between a given Site to FB page. """
    USE_NAMESPACE = False
    KEY_NAME_FIELDS = ['fb_page_id']
    # A given Facebook page can be associated with 1 and only 1 Site
    # therefore, using fb_page_id as key is a safe approach

    msid = ndb.StringProperty(required=True)
    fb_page_id = ndb.StringProperty(required=True)
    fb_tab_id = ndb.StringProperty()
    pid = ndb.StringProperty(required=True)

