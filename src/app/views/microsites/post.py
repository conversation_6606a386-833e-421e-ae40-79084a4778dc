""" Microsite Post page. """
from html.parser import <PERSON><PERSON><PERSON>ars<PERSON>
from datetime import datetime
from google.appengine.api.images import get_serving_url
from google.appengine.ext.blobstore import BlobInfo
from app.domain.middleware.msid_selector import MsidSelectorMiddleware
from app.domain.middleware.partner_whitelabel import GetPartnerWhitelabelDataMiddleware
from app.domain.middleware.pid_selector import PidSelectorMiddleware
from app.domain.post import lookup_post, create_post, get_image_content
from settings import GLOBAL_MIDDLEWARE
from webapp2 import cached_property

from app.constants import LONGFORM
from app.models.page import LongFormPage
from app.domain.page import LongFormPage as LongFormPageDomain
from app.domain.permalink import create_permalink_post_mapping
from app.domain.middleware.long_post_selector import PostIDSelectorMiddleware
from app.domain.middleware.user_agent_detector import UserAgentDetectorMiddleware
from app.domain.cache import PostPermalinkMappingCache
from app.views.microsites import MicrositeView


class ImgTagParser(HTMLParser):
    """
    Stops iterating on the first img tag src instance and returns the src value as the StopIteration message
    """
    def handle_starttag(self, tag, attrs):
        if tag == 'img':
            for attr_name, attr_value in attrs:
                if attr_name == 'src':
                    raise StopIteration(attr_value)


class MicrositePostView(MicrositeView):
    """ Post page for microsite. """
    middleware = GLOBAL_MIDDLEWARE + [PidSelectorMiddleware(), MsidSelectorMiddleware(), PostIDSelectorMiddleware(),
                  UserAgentDetectorMiddleware(), GetPartnerWhitelabelDataMiddleware()]

    def __init__(self, *args, **kwargs):
        """ initialize handler """
        super().__init__(*args, **kwargs)
        self.uses_slugs = None
        self.slug = None

    def dispatch(self):
        """ redirect to valid post link if current permalink is no longer valid """
        super().dispatch()
        self.redirect_to_valid_post_link()

    # W0613: Unused argument
    # pylint: disable=W0613
    def get(self, postslug=''):
        """  GET handler """
        # pylint: disable=W0613
        # MicrositePostView.get: Unused argument 'postslug'
        if not self.microsite:
            self.abort(404)
        else:
            context = {
                'page':                 self.page,
                'long_post':            self.long_post,
                'meta_permalink':       self.long_post.permalink,
                'meta_description':     self.long_post.title,
            }

            # Replace the standard meta image if the long form post contains an image so facebook will use it in a post
            try:
                ImgTagParser().feed(self.long_post.content)
            except StopIteration as e:
                if e.message:
                    context['meta_image_url'] = e.message

            if not self.long_post.hidden:
                # If the current post is hidden, it is probably just being made visible, so this is just a temporary
                # race condition.  If this is the case, disable the previous and next buttons because it won't be
                # possible to navigate back to the current post.
                prev_post_id = self.long_post.prev_post_id
                while prev_post_id:
                    post = lookup_post(prev_post_id, self.msid, pid=self.pid)
                    if post:
                        if post.hidden:
                            prev_post_id = post.prev_post_id
                        else:
                            context['prev_permalink'] = PostPermalinkMappingCache().get(prev_post_id, self.msid,
                                                                                        pid=self.pid)
                            break

                next_post_id = self.long_post.next_post_id
                while next_post_id:
                    post = lookup_post(next_post_id, self.msid, pid=self.pid)
                    if post:
                        if post.hidden:
                            next_post_id = post.next_post_id
                        else:
                            context['next_permalink'] = PostPermalinkMappingCache().get(next_post_id, self.msid,
                                                                                        pid=self.pid)
                            break

            return self.render_response('microsites/html/general.html', **context)

    @cached_property
    def page(self):
        """ The page object for this request. """
        if not self.msid:
            raise ValueError('msid (and thus microsite) is required.')
        if not self.pid:
            raise ValueError('pid is required.')

        page = LongFormPage(pageid='temp', template=LONGFORM, title=self.microsite.name, msid=self.msid, pid=self.pid)
        return LongFormPageDomain.from_model(page)

    @cached_property
    def current_nav_item(self):
        """ Returns the nav_item that corresponds to the current path. """
        return None

    def redirect_to_valid_post_link(self):
        """ if current permalink does have a valid host and slug, generate a valid permalink store it and redirect """
        if not isinstance(self.uses_slugs, bool) or not self.slug:
            ppm = create_permalink_post_mapping(self.msid, self.long_post.post_id, self.long_post.publish_datetime,
                    self.long_post.post_slug, pid=self.pid)
            url = "http://" + ppm.permalink
            return self.redirect(str(url), permanent=True)


class MicrositePreviewPostView(MicrositeView):
    """ Handler for previewing an uploaded image on a microsite. """

    # W0613: Unused argument
    # pylint: disable=W0613
    def get(self, blobkey, slug=''):
        """ GET """

        if not self.microsite:
            self.abort(404)

        blobinfo = BlobInfo.get(blobkey)
        if not blobinfo:
            self.abort(404)

        serving_url = get_serving_url(blobinfo)
        content = get_image_content(serving_url)
        long_post = create_post(self.msid, '', content, datetime.now(), pid=self.pid, put=False)

        context = {
            'page': self.page,
            'long_post': long_post,
            'meta_permalink': long_post.permalink,
            'meta_description': long_post.title,
            'meta_image_url': serving_url,
        }

        return self.render_response('microsites/html/general.html', **context)

    @cached_property
    def current_nav_item(self):
        """ Returns the nav_item that corresponds to the current path. """
        return None

    @cached_property
    def page(self):
        """ The page object for this request. """
        if not self.msid:
            raise ValueError('msid (and thus microsite) is required.')
        if not self.pid:
            raise ValueError('pid is required.')

        page = LongFormPage(pageid='temp', template=LONGFORM, title=self.microsite.name, msid=self.msid, pid=self.pid)
        return LongFormPageDomain.from_model(page)
