"""
review handlers
"""
from webapp2 import cached_property

import vapi
from app.domain.microsite.review import get_review_redirect_url, set_review_shared, get_total_review_aggregate, \
    GOOGLE_SOURCE
from app.views import BaseHandler
from vautil.collection import first

from app.constants import R<PERSON>VI<PERSON><PERSON>, SINGLEREVIEW
from app.domain import page as page_domain
from app.domain.microsite.objects import NavigationItem, NavigationWrapper
from app.models.page import ReviewsPage, SingleReviewPage
from app.views.microsites.general import MicrositeGeneralView


class ReviewView(MicrositeGeneralView):
    """
    base handler for review views
    """
    def __init__(self, *args, **kwargs):
        """ initialize handler """
        super().__init__(*args, **kwargs)
        self.uses_slugs = True

    @cached_property
    def current_nav_item(self):
        """ Returns the nav_item that corresponds to the current path. """
        review_nav = first(self.nav_items, lambda item: item.icon == REVIEWS.lower())
        if review_nav:
            return review_nav
        nav_wrapper = NavigationWrapper(NavigationItem(REVIEWS, 'review', REVIEWS.lower()), self.base_nav_url)
        if self.wl_review_generation_enabled:
            self.nav_items.append(nav_wrapper)
        return nav_wrapper


class MicrositeReviewsView(ReviewView):
    """
    add reviews handler
    """
    ua_profile = None
    ua_mobile_profile = None

    @cached_property
    def page(self):
        """ The page object for this request. """
        if not self.msid:
            raise ValueError('msid (and thus microsite) is required.')
        if not self.pid:
            raise ValueError('pid is required.')

        page = page_domain.get_page(self.current_nav_item.pageid, self.msid, self.pid)
        if page.pageid == page_domain.ERROR_404 and self.wl_review_generation_enabled:
            page = ReviewsPage(pageid='review', template=REVIEWS, title=self.microsite.name, msid=self.msid,
                               pid=self.pid)
            page = page_domain.ReviewsPage.from_model(page)
        return page

    def get(self, **kwargs):
        stage = self.request.GET.get('stage')
        customer_id = self.request.GET.get('customerId')
        message_uuid = self.request.GET.get('messageUuid')
        super().get(kwargs, stage_override=stage, customer_id=customer_id,
                                              message_uuid=message_uuid)


class MicrositeSingleReviewView(ReviewView):
    """
    single review handler
    """

    @cached_property
    def page(self):
        """ The page object for this request. """
        if not self.msid:
            raise ValueError('msid (and thus microsite) is required.')
        if not self.pid:
            raise ValueError('pid is required.')

        page = SingleReviewPage(pageid='temp', template=SINGLEREVIEW,
                                msid=self.msid, pid=self.pid)
        return page_domain.SingleReviewPage.from_model(page)


class MicrositeReviewerShareReviewView(BaseHandler):
    """
    Share a review to a social page. These shares are done by the reviewer.
    """

    def get(self, account_group_id=None, source_id=None, review_id=None):
        """
        Mark a review as shared by the reviewer and redirect to the source's review page.

        Args:
            account_group_id (str): The account group id
            source_id (str): The source id, such as 10010 for Google+.
            review_id (str): The review id for the review which is being shared.
        """
        if not account_group_id or not source_id or not review_id:
            return self.abort(400)

        set_review_shared(account_group_id, review_id)
        redirect_url = get_review_redirect_url(account_group_id, source_id)
        return self.redirect(redirect_url)


class RedirectToSourceReviewPageHandler(BaseHandler):
    """
    Redirect to a source's review page
    """

    def get(self, account_group_id=None, source_id=None):
        """
        GET
        """
        if not account_group_id or not source_id:
            return self.abort(400)
        use_review_url = int(source_id) == GOOGLE_SOURCE
        redirect_url = get_review_redirect_url(account_group_id, source_id, use_review_url=use_review_url)
        return self.redirect(redirect_url)


class GetReviewAggregateApi(vapi.NoAuthAjaxHandler):
    """ AJAX call to get the aggregate count of reviews """

    VERSION = '1.0'
    REQUIRES_HTTPS = False
    ALLOWED_METHODS = frozenset(['GET'])
    agid = vapi.StringProperty(required=True)

    def process(self, args):
        count, average = get_total_review_aggregate(self.agid)
        results = {"average" : average, "count" : count }
    
        return results
