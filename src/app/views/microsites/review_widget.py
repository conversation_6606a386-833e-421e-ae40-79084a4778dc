"""
Review Generation widget handler
"""
import cgi
import logging
from collections import namedtuple
import re
import json
import string
import urllib.request, urllib.parse, urllib.error

import datetime

from google.appengine.api.datastore_errors import Bad<PERSON>rgumentError, <PERSON><PERSON><PERSON>ueError

from account_group_media_sdk.api import AccountGroupMediaClient, ImageType
from webapp2 import cached_property

import settings
from app.domain.microsite.json_ld import build_jsonld_type, build_aggregate_rating, build_review
from app.domain.middleware.msid_selector import MicrositeRequiredMiddleware
from app.i18n import InternationalizationMixin
from app.models.microsite import Microsite
from app.views import BaseHandler
from app.domain.microsite.review import get_review_generation_dict, LOCATION_PAGE_REVIEW_SOURCE
from app.domain.microsite import review as review_domain
from app.domain.middleware.user_agent_detector import UserAgentDetectorMiddleware
from coresdk_v2.base import ReviewClient
from settings import VENDASTA_FRONTEND_HOST
from srsdk import AddonClient, ApiException
from vax.utils.env import env_from_app_engine_env
from bleach import clean

MAX_THIRD_PARTY_REVIEW_CONTENT_LENGTH = 160


class BaseReviewWidgetHandler(BaseHandler, InternationalizationMixin):
    """
    Base handler for review widgets containing common properties.
    """

    middleware = [MicrositeRequiredMiddleware()]

    DEFAULT_BACKGROUND_COLOR = '#09669A'
    DEFAULT_PRIMARY_TEXT_COLOR = '#057ec1'
    DEFAULT_SECONDARY_TEXT_COLOR = '#666666'
    DEFAULT_FONT_FAMILY = 'Open Sans'
    DEFAULT_WIDTH = '336px'
    DEFAULT_HEIGHT = '670px'

    @cached_property
    def pid(self):
        """ Partner Id """
        return self.request.GET.get('pid')

    @cached_property
    def agid(self):
        """ Account Group Id """
        return self.request.GET.get('agid')

    @cached_property
    def microsite(self):
        """ Microsite """
        try:
            microsite = Microsite.lookup_by_agid(self.agid, pid=self.pid)
        except (BadArgumentError, BadValueError, ValueError) as e:
            logging.warning(e.message)
            error_message = "An error occurred accepting the request to get the review widget for pid=%s and agid=%s"
            self.abort(400, error_message % (self.pid, self.agid))
        return microsite

    @cached_property
    def disable_actions(self):
        """ Disable Actions """
        return self.request.GET.get('disable_actions')

    @cached_property
    def stage_override(self):
        """ Stage Override """
        return self.request.GET.get('stage_override')

    @property
    def cursor(self):
        """ Cursor of pages reviews, defaulting at 0 to start """
        return self.request.GET.get('cursor', 0)

    @cached_property
    def background_color(self):
        """ Background Color """
        return _stop_injection(self.request.get('background', self.DEFAULT_BACKGROUND_COLOR))

    @cached_property
    def text_color(self):
        """ Text Color """
        return _stop_injection(self.request.get('text_color', self.DEFAULT_PRIMARY_TEXT_COLOR))

    @cached_property
    def secondary_text_color(self):
        """ Secondary Text Color """
        return _stop_injection(self.request.get('secondary_text_color', self.DEFAULT_SECONDARY_TEXT_COLOR))

    @cached_property
    def font_family(self):
        """ Font family """
        return _stop_injection(self.request.get('font_family', self.DEFAULT_FONT_FAMILY))

    @cached_property
    def width(self):
        """ Width """
        return _stop_injection(self.request.get('width', self.DEFAULT_WIDTH))

    @cached_property
    def height(self):
        """ Height """
        return _stop_injection(self.request.get('height', self.DEFAULT_HEIGHT))

    @cached_property
    def card_corners(self):
        """ Card Corners """
        return _stop_injection(self.request.get('card_corners'))

    @cached_property
    def border_style(self):
        """ Border Style """
        return _stop_injection(self.request.get('border_style'))

    @cached_property
    def icon_style(self):
        """ Icon Style """
        return _stop_injection(self.request.get('icon_style'))

    @cached_property
    def feed_height(self):
        """ Feed Height """
        return _stop_injection(self.request.get('feed_height'))

    @cached_property
    def shadows(self):
        """ Shadows """
        return _stop_injection(self.request.get('shadows'))

    @cached_property
    def lang(self):
        """ Language """
        return _stop_injection(self.request.get('lang'))

    def render_template(self, template, **context):
        """ Render the template with common context data. """
        context.update({
            'background': self.background_color,
            'text_color': self.text_color,
            'secondary_text_color': self.secondary_text_color,
            'width': self.width,
            'height': self.height,
            'card_corners': self.card_corners,
            'border_style': self.border_style,
            'icon_style': self.icon_style,
            'feed_height': self.feed_height,
            'shadows': self.shadows,
            'lang': self.lang,
            'disable_actions': self.disable_actions,
            'stage_override': self.stage_override,
            'microsite': self.microsite,
            'cursor': self.cursor,
            'font_family': self.font_family,
        })
        if self.lang:
            self.session['HTTP_ACCEPT_LANGUAGE'] = self.lang
        context['i18n'] = self.get_translations(self.request, session=self.session, partner_id=self.pid)
        context['use_24_hour_time_format'] = self.session.get('HTTP_ACCEPT_LANGUAGE') == 'cs'

        return super().render_template(template, **context)

    def get_all_review_addon_settings(self):
        """
        Returns whether the RM add-on for showing reviews from all sources in the review widget is enabled (boolean)
         and the setting for third party reviews to be published (integer)
        The setting ranges from 0 to 5, 1 to 5 being the rating in stars, and 0 indicating that reviews are being
         published manually
        """
        addon_client = AddonClient(
            settings.RI_API_USER,
            settings.RI_API_KEY,
            configuration=settings.ENVIRONMENT_NAME.lower()
        )
        try:
            return addon_client.getAllReviewAddonSettingsV3(self.agid)
        except ApiException as e:
            logging.warning(e.message)
            return False, 0

    def parse_review_data(self, reviews, jsonp=True):
        """
        Format the title, published date, and content of the list of reviews
        """
        for review in reviews:
            if review.get('source_id'):
                review['source_class'] = 'icon50-sourceId-{}'.format(review.get('source_id'))
            if review.get('title'):
                review['title'] = self.get_review_title(review)
            if review.get('published'):
                review['published'] = self.get_review_published(review, jsonp)
            if review.get('content'):
                review['content'] = self.get_snippetized_review_content(review)
        return reviews

    @staticmethod
    def get_review_published(review, is_jsonp):
        """
        Get the published date of review. If it's for jsonp,
        return the timestamp otherwise it's the public api, only return the date.
        """
        if is_jsonp:
            return datetime.datetime.strptime(review['published'], "%Y-%m-%dT%H:%M:%S.%fZ")
        review_datetime = datetime.datetime.strptime(review['published'], "%Y-%m-%dT%H:%M:%S.%fZ") \
            .isoformat(sep=' ')
        return review_datetime.split(' ', 1)[0]

    @staticmethod
    def get_review_title(review):
        """ Get the review title only if the content doesn't already start with it"""
        title = review.get('title')
        content = review.get('content')

        if title:
            title = title.replace('...', '')
        if not title:
            return ""
        if content and title:
            if content.startswith(title):
                return ""
        return title

    @staticmethod
    def get_snippetized_review_content(review):
        """ Truncate the review content to be 160 characters, unless it's a My Listing review (leave content as is)"""
        content = review.get('content')
        source = review.get('source_id')
        url = review.get('url')

        more_link = ''
        if url:
            more_link = f"<a target='_blank' href='{url}'>more</a>"

        if not content or len(content) < MAX_THIRD_PARTY_REVIEW_CONTENT_LENGTH or source == 12000:
            return clean(
                content,
                tags=['a', 'b', 'br', 'i', 'p']
            ) or ""
        return clean(
            content[:MAX_THIRD_PARTY_REVIEW_CONTENT_LENGTH],
            tags=['a', 'b', 'br', 'i', 'p']
        ) + "..." + more_link


class AddReviewWidgetHandler(BaseReviewWidgetHandler):
    """
    Serves the widget embedded on a user's website.
    """
    middleware = [MicrositeRequiredMiddleware(), UserAgentDetectorMiddleware()]
    ua_profile = None
    ua_mobile_profile = None
    DEFAULT_PRIMARY_TEXT_COLOR = '#FFF'

    @cached_property
    def secondary_text_color(self):
        """
        Secondary Text Color
        Uses self.text_color as default.
        """
        return _stop_injection(self.request.get('secondary_text_color', self.text_color))

    def get(self):
        """
        GET parameter (default)
        """
        review_gen_dict = get_review_generation_dict(
            self.agid, self.pid
        )
        listings = review_gen_dict.get('listings', [])

        context = {
            'highlighted_background': _get_highlighted_color(self.background_color),
            'title': self.microsite.name,
            'listings': listings,
            'positive_review_message': review_gen_dict.get('positive_review_message'),
            'negative_review_message': review_gen_dict.get('negative_review_message'),
            'show_reviews': False,
            'in_widget': True,
        }
        return self.render_response('microsites/html/public/review-widget.html', **context)


class ReviewDisplayWidgetHandler(BaseReviewWidgetHandler):
    """
    Serves the Review Display widget on a user's website
    """

    def get(self):
        """ GET """
        item_reviewed_types = review_domain.get_item_reviewed_types_from_tax_ids(self.microsite.tax_id)

        context = {
            'item_reviewed_types': item_reviewed_types,
        }
        return self.render_response('microsites/html/public/review-display-widget.html', **context)


class ReviewDisplayWidgetJsonpHandler(BaseReviewWidgetHandler):
    """
    Serves the Review Display jsonp widget on a user's website
    """

    def build_json_ld(self, review_count, average_rating, feed_data, image_url=None):
        """
        Build the json ld dictionary
        """
        json_ld_type = build_jsonld_type(name=self.microsite.name, tax_id=self.microsite.tax_id)
        aggregate_rating = build_aggregate_rating(rating_value=average_rating, review_count=review_count)
        if aggregate_rating:
            json_ld_type['aggregateRating'] = aggregate_rating

        json_ld_type['image'] = {}
        if image_url:
            json_ld_type['image'] = image_url

        reviews = []
        for review in feed_data:
            r = build_review(
                source_id=review.get('source_id'),
                name=review.get('title'),
                review_body=review.get('content'),
                author=review.get('reviewer_name'),
                date_published=str(review.get('published')) if review.get('published') else None,
                review_rating=review.get('original_stars'),
            )
            if r:
                reviews.append(r)
        if reviews:
            json_ld_type['review'] = reviews
        return json_ld_type

    def get(self):
        """ GET """
        callback = self.request.GET.get('callback')
        all_sources_addon_enabled, all_review_publish_setting = self.get_all_review_addon_settings()
        review_client = ReviewClient(
            settings.VSOCIAL_USER, settings.VSOCIAL_API, configuration=settings.ENVIRONMENT_NAME.lower()
        )

        cursor = self.cursor
        feed_data = []
        item_reviewed_types = []
        source_id = LOCATION_PAGE_REVIEW_SOURCE if not all_sources_addon_enabled else None
        if self.stage_override != 'empty':
            response = review_client.listWidgetReviewsV2(
                accountGroupId=self.agid, sourceId=source_id, sort_published=True, offset=cursor, limit=25,
                thirdPartyPublishRating=all_review_publish_setting
            )
            feed_data = response.get('reviews', [])
            cursor = response.get('cursor', 0)
            if not feed_data and self.stage_override == 'demo':
                feed_data = self.build_demo_data(all_review_publish_setting)
            feed_data = self.parse_review_data(feed_data, jsonp=True)

            item_reviewed_types = review_domain.get_item_reviewed_types_from_tax_ids(self.microsite.tax_id)

        review_stats = review_client.totalReviewAggregateV2(
            accountGroupId=self.agid, sourceId=LOCATION_PAGE_REVIEW_SOURCE
        )

        review_count = review_stats.get('review_count')
        average_rating = review_stats.get('average_rating')

        image_url = None
        account_group_media_client = AccountGroupMediaClient(env_from_app_engine_env())
        if account_group_media_client:
            image = account_group_media_client.list_images(self.agid, '', 1, image_types=ImageType.LOGO)
            if image.images:
                image_url = 'https://media-{}.apigateway.co/image/get/{}'.format(env_from_app_engine_env().value,
                                                                                  image.images[0].image_id)

        context = {
            'json_ld': cgi.escape(json.dumps(self.build_json_ld(review_count, average_rating, feed_data,
                                                                image_url=image_url))),
            'feed_data': feed_data,
            'item_reviewed_types': item_reviewed_types,
            'review_count': review_count,
            'average_rating': average_rating
        }
        response_data = {
            'html': self.render_template('microsites/html/public/review-display-widget-jsonp.html', **context),
            'cursor': cursor,
        }
        jsonp_content = "{} ( {} )".format(callback, json.dumps(response_data))

        self.response.content_type = 'application/javascript'
        self.response.write(jsonp_content.encode('utf-8', errors='ignore'))

    @staticmethod
    def build_demo_data(third_party_enabled):
        """
        Make demo data for when there are no reviews but we want to show some examples
        """
        reviews = [
            {
                'source_id': 12000,
                'title': 'Example Review 1',
                'published': '2017-08-19T12:00:00.000000Z',
                'content': 'Lorem ipsum dolor sit amet, consectetur adipiscing elit',
                'rating': [5]
            },
            {
                'source_id': 12000,
                'title': 'Example Review 2',
                'published': '2017-08-17T12:00:00.000000Z',
                'content': 'Lorem ipsum dolor sit amet, consectetur adipiscing elit',
                'rating': [4]
            }
        ]
        if third_party_enabled:
            reviews.append(
                {
                    'source_id': 10050,
                    'title': 'Example Review 3',
                    'published': '2017-07-19T12:00:00.000000Z',
                    'content': 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor '
                               'incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud '
                               'exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
                    'rating': [5]
                }
            )
        return reviews


class ReviewDisplayWidgetScriptHandler(BaseReviewWidgetHandler):
    """
    Serves dynamic review-display-widget.js script.
    """

    def get(self):
        """ GET """
        context = {
            'vff_host': VENDASTA_FRONTEND_HOST,
            'ms_host': self.request.host_url
        }
        response = self.render_response('microsites/js/review-display-widget.js', **context)
        response.content_type = 'application/javascript'
        return response


class ReviewWidgetReviewsJson(BaseReviewWidgetHandler):
    """
    Serves the raw JSON for the list of reviews.
    """

    def get(self):
        """ GET """
        all_sources_addon_enabled, all_review_publish_setting = self.get_all_review_addon_settings()
        review_client = ReviewClient(
            settings.VSOCIAL_USER, settings.VSOCIAL_API, configuration=settings.ENVIRONMENT_NAME.lower()
        )

        cursor = self.cursor
        source_id = LOCATION_PAGE_REVIEW_SOURCE if not all_sources_addon_enabled else None
        page_size = self.request.GET.get('pageSize') or 25

        response = review_client.listWidgetReviewsV2(
            accountGroupId=self.agid, sourceId=source_id, sort_published=True, offset=cursor, limit=page_size,
            thirdPartyPublishRating=all_review_publish_setting
        )

        feed_data = response.get('reviews', [])
        cursor = response.get('cursor', 0)
        feed_data = self.parse_review_data(feed_data, jsonp=False)
        item_reviewed_types = review_domain.get_item_reviewed_types_from_tax_ids(self.microsite.tax_id)

        json_output = json.dumps({
            'review_data': feed_data,
            'item_reviewed_types': item_reviewed_types,
            'cursor': cursor
        })
        if self.request.GET.get('callback'):
            callback = self.request.GET.get('callback')
            self.response.content_type = 'application/javascript'
            response_content = f'{callback}({json_output})'
            self.response.write(response_content)
        else:
            self.response.content_type = 'application/json'
            self.response.write(json_output)


class ReviewWidgetLocationJson(BaseReviewWidgetHandler):
    """
    Serves the raw JSON for the location.
    """

    def get(self):
        """ GET """
        json_output = json.dumps({
            'company_name': self.microsite.name,
            'address': self.microsite.address1,
            'city': self.microsite.city,
            'state': self.microsite.state,
            'country': self.microsite.country,
            'website': self.microsite.website,
            'zip': self.microsite.zipcode,
            'work_number': self.microsite.phone,
            'call_tracking_number': self.microsite.phone_call_tracking,
            'linkedin_url': self.microsite.linkedin_url,
            'foursquare_url': self.microsite.foursquare_url,
            'twitter_url': self.microsite.twitter_url,
            'facebook_url': self.microsite.facebook_url,
            'rss_url': self.microsite.rss_url,
            'youtube_url': self.microsite.youtube_url,
            'instagram_url': self.microsite.instagram_url,
            'pinterest_url': self.microsite.pinterest_url,
            'latitude': self.microsite.geo.lat,
            'longitude': self.microsite.geo.lon,
            'tagline': self.microsite.blurb,
            'categories': self.microsite.categories,
        })
        if self.request.GET.get('callback'):
            callback = self.request.GET.get('callback')
            self.response.content_type = 'application/javascript'
            response_content = f'{callback}({json_output})'
            self.response.write(response_content)
        else:
            self.response.content_type = 'application/json'
            self.response.write(json_output)


TROUBLESOME_CSS_CHARS = [';', '!', '{', '}']


def _stop_injection(css_string):
    """
    Cuts off the given css string at the first ;, {, }, or !.  This prevents CSS injection. For example, if:
        !important; } body { -webkit-transform:rotateY(180deg);
    were passed in as the 'secondary_text_color', the whole widget would be flipped horizontally in webkit browsers.

    It's possible that JavaScript could be injected in the same way.
    """
    if not css_string:
        return None

    def substring_up_to_stop_character(whole_string, stop_character):
        """
        Grabs the first part of a string up to the given character
        """
        split = whole_string.split(stop_character)
        stopped = next(iter(split), '')
        return stopped

    unescaped_css_string = urllib.parse.unquote(css_string)

    result = unescaped_css_string
    for character in TROUBLESOME_CSS_CHARS:
        result = substring_up_to_stop_character(result, character)

    return result.strip()


_FALLBACK_COLOR = 'rgba(90,90,90,1)'

_Color = namedtuple('Color', 'r g b a')


def _get_highlighted_color(color_string):
    """
    Returns a darker version of the color represented by the given HTML color string.  This string may be formatted:
        rgb(0,0,0)
        rgba(0,0,0,0)
        ABCDEF (or #ABCDEF or abcdef or #abcdef)
        ABCDEFAA (or #ABCDEFAA or abcdefaa or #abcdefaa)
        BDF (or #BDF or #bdf or bdf)
    """
    if not color_string:
        return None

    color = _get_rgba_from_rgba(color_string)

    if not color:
        color = _get_rgba_from_rgb(color_string)

    if not color:
        color = _get_rgba_from_hex(color_string)

    if not color:
        return _FALLBACK_COLOR

    if [color.r, color.g, color.b] == [0, 0, 0]:
        return _FALLBACK_COLOR

    highlight = lambda x: str(int(float(x) * 0.90))
    r, g, b, a, = color.r, color.g, color.b, color.a

    return 'rgba(%s)' % ','.join([highlight(r), highlight(g), highlight(b), str(a)])


def _get_rgba_from_hex(hex_color_string):
    """
    Gets the red, green, blue, and alpha from a color string having the format '#000', '#000000', or '#00000000'
    """
    if '#' == hex_color_string[0]:
        no_hash_hex_color_string = hex_color_string[1:]
    else:
        no_hash_hex_color_string = hex_color_string

    if _does_hex_string_contain_invalid_characters(no_hash_hex_color_string):
        return None

    return _get_rgba_from_standard_hex(no_hash_hex_color_string) or _get_rgba_from_short_hex(no_hash_hex_color_string)


_VALID_HEX_CHARS = [str(i) for i in range(0, 10, 1)] + list(string.uppercase[:6]) + list(string.lowercase[:6])


def _does_hex_string_contain_invalid_characters(no_hash_hex_color_string):
    """
    Returns False if the given string contains characters other than abcdefABCDEF123456
    """
    return False in (char in _VALID_HEX_CHARS for char in list(no_hash_hex_color_string))


def _get_rgba_from_standard_hex(no_hash_hex_color_string):
    """
    Gets the red, green, blue, and alpha from a color string having the format '#123456' or '#12345678'
    """
    if len(no_hash_hex_color_string) > 5:
        r = int(no_hash_hex_color_string[:2], 16)
        g = int(no_hash_hex_color_string[2:4], 16)
        b = int(no_hash_hex_color_string[4:6], 16)
        a = 1

        if len(no_hash_hex_color_string) > 7:
            a = int(no_hash_hex_color_string[6:8], 16) / 255.0

        return _Color(r, g, b, a)
    return None


def _get_rgba_from_short_hex(no_hash_hex_color_string):
    """
    Gets the red, green, blue, and alpha from a color string having the format '#000'
    """
    if len(no_hash_hex_color_string) == 3:
        r = int(no_hash_hex_color_string[:1] * 2, 16)
        g = int(no_hash_hex_color_string[1:2] * 2, 16)
        b = int(no_hash_hex_color_string[2:3] * 2, 16)
        a = 1

        return _Color(r, g, b, a)

    return None


def _get_rgba_from_rgba(rgba_color_string):
    """
    Gets the red, green, blue, and alpha from a color string having the format 'rgba(0,0,0,0)'
    """
    if 'rgba(' in rgba_color_string[:5]:
        m = re.search('rgba\\(\\s*(\\d*),\\s*(\\d*),\\s*(\\d*),\\s*(\\d*)\\s*\\)', rgba_color_string)
        if m:
            return _Color(m.group(1), m.group(2), m.group(3), m.group(4))
    return None


def _get_rgba_from_rgb(rgb_color_string):
    """
    Gets the red, green, blue, and alpha (always 1) from a color string having the format 'rgb(0,0,0)'
    """
    if 'rgb(' in rgb_color_string[:4]:
        m = re.search('rgb\\(\\s*(\\d*),\\s*(\\d*),\\s*(\\d*)\\s*\\)', rgb_color_string)
        if m:
            return _Color(m.group(1), m.group(2), m.group(3), 1)
    return None
