""" Admin Microsite pages. """
import json
import urllib.parse
import logging

import settings
import vauth.foundational_products
import vconfig
from app.layouts import LOCALIZED_LAYOUTS

from app.domain.social import SocialSync
from app.domain.microsite import get_microsite, add_microsite_activity, \
    get_business_profile_completeness
from app.domain.middleware.pid_selector import PidSelectorMiddleware
from app.domain.middleware.partner_whitelabel import get_partner_whitelabel_config
from app.domain.url_mappings import lookup_hostslug_msid_mapping_for_msid, get_market_mapping
from app.domain.analytics_v3 import  get_location_dashboard_data
from app.domain.vbc import get_vbc_pid, get_ms_pid
from app.models.microsite import Microsite as MicrositeModel, AccountGroupModel
from app.views import BaseHandler
from app.views.admin import AuthenticatedEditSiteView

from settings import GLOBAL_MIDDLEWARE, AUTH_PARTY_PARTNER_CENTRAL, BACK_TO_PARTNER_CENTRAL_TEXT,\
    MICROSITE_PARTNER_PID_MAP
from webapp2 import uri_for


class DefaultView(AuthenticatedEditSiteView):
    """ Used to determine the default view according to what is enabled in whitelabel data """

    def get(self, account_group_id):
        """ GET """
        if self.wl_enable_overview_page:
            return self.redirect(uri_for('edit-overview', account_group_id=account_group_id))
        return self.redirect(uri_for('gmb-insights', account_group_id=account_group_id))
        # TODO: MS-2535 This needs to be replaced with the ability for a partner to chose a MS landing page


class OverviewHandler(AuthenticatedEditSiteView):
    """ Display overview page """

    def get(self, account_group_id):
        """ GET """
        if not self.wl_enable_overview_page:
            return self.redirect(uri_for('gmb-insights', account_group_id=account_group_id))
        # TODO: MS-2535 This needs to be replaced with the ability for a partner to chose a MS landing page

        context = {
            'account_group_id': account_group_id,
            'page_title': 'Overview'
        }
        return self.render_response('admin/html/dashboard.html', tab='overview', **context)


class GetProfileCompletedStat(AuthenticatedEditSiteView):
    """ Get the business profile completed data for the overview page """

    def get(self, account_group_id): # pylint: disable=unused-argument
        """ GET """
        return self.render_json(get_business_profile_completeness(self.microsite))


class GetLocationDataStat(AuthenticatedEditSiteView):
    """ Get the location dashboard data for the overview page """

    def get(self, account_group_id): # pylint: disable=unused-argument
        """ GET """
        return self.render_json(get_location_dashboard_data(self.microsite.msid, self.partner.google_analytics_id))


class GetSocialStat(AuthenticatedEditSiteView):
    """ Get the social stats for the overview page """

    def get(self, account_group_id):
        """ GET """
        account_group = AccountGroupModel.get(account_group_id)

        # We are sending the provider as an int for whitelabel reasons
        # https://github.com/vendasta/listing-builder-client#listing-builder-is-a-white-labeled-project
        social_sync = SocialSync(self.microsite.msid, account_group_id)

        return self.render_json({
            'agid': account_group.account_group_id,
            'lsp_active_for_account': account_group.listing_sync_pro and \
                                      account_group.listing_sync_pro.is_active,
            'number_synced_profiles': social_sync.get_number_synced_profiles(),
        })


class EditMicrositeView(AuthenticatedEditSiteView):
    """ Edit a microsite by redirecting to global settings """

    def get(self, account_group_id):
        """ GET """
        pid = get_vbc_pid(self.pid)
        redirect_url = self.uri_for('sidebar-redirect',
                                    product_id=vauth.foundational_products.ProductKeys.GLOBAL_SETTINGS,
                                    product_pid=pid,
                                    account_id=account_group_id,
                                    referrer_url=self._get_referrer_url())
        self.redirect(redirect_url)

    def _get_referrer_url(self):
        """ get the referrer url for where the user should go back to after editing their business profile """
        referrer = self.request.referrer
        referrer_host = urllib.parse.urlparse(self.request.referrer).hostname if referrer else None
        current_host = urllib.parse.urlparse(self.request.url).hostname
        if not referrer or referrer_host != current_host:
            referrer = self.uri_for('edit-overview', account_group_id=self.account_group_id)
        return referrer


class RedirectToSignedURLHandler(BaseHandler):
    """ Handler that redirects to a signed edit URL.
        can provide a "next" parameter that is the url to redirect to
        can provide a "next_route" to pick which url to redirect to
        if you don't provide a next or next_route (or provide both) it will redirect to the edit microsite page
    """
    middleware = GLOBAL_MIDDLEWARE + [PidSelectorMiddleware()]

    def get(self, msid):
        """ GET """
        next_default = self.request.get('next')
        next_route = self.request.get('next_route')

        if next_route and not next_default:
            next_url = self.build_url_for(next_route, msid)
        elif next_default and not next_route:
            next_url = next_default
        else:
            next_url = self.build_edit_url_for(self.pid, msid)

        if "http" not in next_url:
            next_url = "http://{}{}".format(self.request.host, next_url)

        back_url_text = self.request.get(vauth.foundational_products.UrlKeys.BACK_URL_TEXT)
        if not back_url_text:
            auth_party = self.request.get(vauth.foundational_products.UrlKeys.AUTH_PARTY)
            if auth_party == AUTH_PARTY_PARTNER_CENTRAL:
                back_url_text = BACK_TO_PARTNER_CENTRAL_TEXT

        kwargs = {vauth.foundational_products.UrlKeys.BACK_URL: self.request.get(
            vauth.foundational_products.UrlKeys.BACK_URL),
                  vauth.foundational_products.UrlKeys.BACK_URL_TEXT: back_url_text,
                  vauth.foundational_products.UrlKeys.IMPERSONATION: self.request.get(
                      vauth.foundational_products.UrlKeys.IMPERSONATION)}
        pid = self.pid or self.request.get(vauth.foundational_products.UrlKeys.PID)
        signed_url = vauth.foundational_products.generate_signed_url(next_url, msid, pid, **kwargs)
        add_microsite_activity(msid, pid)
        self.response.set_status(302)
        self.response.location = signed_url
        return self.response

    def build_url_for(self, route, msid, host=None):
        """
        returns mobile sites edit url with scheme and host
        """
        host = host or self.request.host
        path = self.uri_for(route, msid=msid)
        next_url = urllib.parse.urlunparse([self.request.scheme, host, path, '', '', ''])
        return next_url

    def build_edit_url_for(self, pid, msid):
        """
        returns mobile sites edit url using hostslugs or market host
        """
        host = self.request.host
        host_slugs = lookup_hostslug_msid_mapping_for_msid(pid, msid)
        host_slugs = [hs for hs in host_slugs if hs.redirect_hostslug is None]
        if len(host_slugs):
            host = host_slugs[0].host
        else:
            microsite = get_microsite(msid, pid)
            if microsite.market_id and pid:
                market_mapping = get_market_mapping(pid, microsite.market_id)
                if market_mapping is not None:
                    host = market_mapping.host
        host = "{}:{}".format(host, self.request.server_port) if self.request.server_port else host
        return self.build_url_for('edit-microsite', msid, host)


class SSORedirectToSignedURLHandler(BaseHandler):
    """
    Handler that validates incoming request and redirect to correct partner host if validated
    """

    def get(self):
        """
        GET handler
        """
        msid = self.request.get(vauth.foundational_products.UrlKeys.SSO_TOKEN)
        partner_id = get_ms_pid(self.request.get(vauth.foundational_products.UrlKeys.PID))

        microsite_model = MicrositeModel.get_by_msid(msid, pid=partner_id)

        if microsite_model is None:
            logging.warning(
                'Did not find microsite_model with matching msid (%s) pid (%s) to determine mobile site URL',
                msid, partner_id)
            self.abort(404)
        elif vauth.foundational_products.validate_url(self.request.url):
            next_url = self.get_mobile_site_url(partner_id, msid,
                                                self.request.get(vauth.foundational_products.UrlKeys.NEXT_URL))
            if not next_url:
                logging.warning(
                    "No matching host with matching site for msid (%s) pid (%s) was found. Redirecting back to (%s).",
                    partner_id, msid, self.request.referrer)
                self.response.set_status(302)
                self.response.location = self.request.referrer
                return self.response

            back_url = self.request.get(vauth.foundational_products.UrlKeys.BACK_URL)
            back_url_text = self.request.get(vauth.foundational_products.UrlKeys.BACK_URL_TEXT)
            microsite = get_microsite(msid, pid=partner_id)
            vbc_pid = get_vbc_pid(partner_id)
            whitelabel_data = get_partner_whitelabel_config(vbc_pid, market_id=microsite.market_id)

            if not back_url and whitelabel_data:
                back_url = whitelabel_data.ms_exit_link_url
            if not back_url_text and whitelabel_data:
                back_url_text = whitelabel_data.ms_exit_link_text

            kwargs = {vauth.foundational_products.UrlKeys.BACK_URL: back_url,
                      vauth.foundational_products.UrlKeys.BACK_URL_TEXT: back_url_text,
                      vauth.foundational_products.UrlKeys.IMPERSONATION: self.request.get(
                          vauth.foundational_products.UrlKeys.IMPERSONATION)}

            signed_url = vauth.foundational_products.generate_signed_url(next_url, msid, partner_id, **kwargs)
            add_microsite_activity(msid, partner_id)
            logging.info("Redirecting to vauth signed url(%s)", signed_url)
            self.response.set_status(302)
            self.response.location = signed_url
            return self.response
        else:
            logging.warn("Invalid auth signed url found. Redirecting back to (%s)", self.request.referrer)
            self.response.set_status(302)
            self.response.location = self.request.referrer
            return self.response

    def get_mobile_site_url(self, partner_id, msid, next_url=None):
        """
        returns mobile sites edit url with scheme and partner whitelabel host
        """
        if not partner_id:
            raise ValueError('partner_id is required.')

        host = self.request.host
        microsite = get_microsite(msid, partner_id)
        path = next_url or self.uri_for('edit-microsite', account_group_id=microsite.agid)
        # getting host from host slug mapping
        host_slugs = lookup_hostslug_msid_mapping_for_msid(partner_id, msid)
        host_slugs = [hs for hs in host_slugs if hs.redirect_hostslug is None]

        if len(host_slugs):
            host = host_slugs[0].host
        elif microsite.market_id:
            market_mapping = get_market_mapping(partner_id, microsite.market_id)
            if market_mapping is not None:
                host = market_mapping.host

        if not host:
            logging.error('Unable to find host for partner id: "%s".', partner_id)
            return None
        else:
            next_url = urllib.parse.urlunparse([self.request.scheme, host, path, '', '', ''])
            return next_url


class WebsiteAdminView(AuthenticatedEditSiteView):
    """ View for the website admin area """

    def render_response(self, template, **context):
        if not context:
            context = {}

        pages_tab = {
            'text': context.get('i18n', {}).get("MY_LISTING", {}).get("TABS", {}).get("PAGES", ''),
            'url': '#pages',
        }
        themes_tab = {
            'text': context.get('i18n', {}).get("MY_LISTING", {}).get("TABS", {}).get("THEMES", ''),
            'url': '#templates',
        }
        branding_tab = {
            'text': context.get('i18n', {}).get("MY_LISTING", {}).get("TABS", {}).get("BRANDING", ''),
            'url': '#logos',
        }
        domains_tab = {
            'text': context.get('i18n', {}).get("MY_LISTING", {}).get("TABS", {}).get("DOMAINS", ''),
            'url': '#domains',
        }
        campaign_tracking_tab = {
            'text': context.get('i18n', {}).get("MY_LISTING", {}).get("TABS", {}).get("CAMPAIGN_TRACKING", ''),
            'url': '#seo',
        }
        analytics_tab = {
            'text': context.get('i18n', {}).get("MY_LISTING", {}).get("TABS", {}).get("ANALYTICS", ''),
            'url': '#analytics',
        }
        context['page_tabs'] = [pages_tab, themes_tab, branding_tab, domains_tab, campaign_tracking_tab,
                                analytics_tab]

        super().render_response(template, **context)

    def get(self, account_group_id):  # pylint: disable=unused-argument
        """ GET """
        market_id = self.microsite.market_id
        partner_id = MICROSITE_PARTNER_PID_MAP.get(self.pid, self.pid)
        client = vconfig.ConfigurationClient(environment=settings.ENVIRONMENT_NAME)
        feature_client = vconfig.FeatureClient(environment=settings.ENVIRONMENT_NAME)

        config_future = client.get_config_async(partner_id, market_id=market_id)
        listing_directory_enabled_future = feature_client.is_feature_enabled_async('listing_directory',
                                                                                   self.pid, market_id)

        config = config_future.get_result()
        listing_directory_enabled = listing_directory_enabled_future.get_result()

        listing_directory_enabled = listing_directory_enabled and config.business_directory_enabled

        locale = self.get_locale(self.request, self.session)
        context = {'i18n': self.get_translations(self.request, session=self.session, partner_id=self.pid)}
        context.update({
            'slug_mappings': lookup_hostslug_msid_mapping_for_msid(self.pid, self.microsite.msid),
            'tab': 'location',
            'import_vform': True,
            'layouts': json.dumps(LOCALIZED_LAYOUTS.get(locale, LOCALIZED_LAYOUTS.get('en', {}))),
            'page_title': context.get('i18n', {}).get("MY_LISTING", {}).get("MY_LISTING", ''),
            'listing_directory_enabled': listing_directory_enabled
        })
        return self.render_response('admin/html/website.html', **context)
