""" Internal handler for added social users. """
import logging
import urllib.request, urllib.parse, urllib.error
from webapp2 import uri_for

from app.domain.middleware.msid_selector import MsidArgumentRequiredMiddleware
from app.domain.social import SitesVSocial, SocialSync
from app.views.admin import AuthenticatedEditSiteView, AUTHENTICATE_EDIT_SITE_MIDDLEWARE

from coresdk.keys import API_KEY as CS_KEY


class SocialUserAddedHandler(AuthenticatedEditSiteView):
    """
    After associating a service (adding a facebook user, twitter user, foursquare user/page etc) to your social profile
    via core services, core redirects the user back here, so MS can redirect to list of pages/venue for facebook/
    foursquare/google+ and to the sync page for twitter
    """

    middleware = AUTHENTICATE_EDIT_SITE_MIDDLEWARE + [MsidArgumentRequiredMiddleware()]

    def get(self, account_group_id):
        """
        Called upon return from Core Services' addService API.
        If a twitter account was added, redirect to sync page.
        If a facebook user was added, redirects to the social service/page add page.
        If a foursquare user was added, redirects to the social service/venue add page.
        If a google+ user was added, redirects to the social service/page add page.
        :return:
        """
        service_id = self.request.GET.get(CS_KEY.META_MESSAGE)
        vsocial = SitesVSocial(self.msid)

        added_service = vsocial.get_service(service_id)
        if not added_service:
            error_message = service_id  # Core overloads the message param
            user_error_message = 'Your account did not add properly. Please try again.'
            self.set_flash(user_error_message, "Error")
            logging.error("Account did not add. %s msid=%s", error_message, self.msid)
            next_url = self.uri_for('profile-sync', account_group_id=account_group_id)
        else:
            service_type = added_service.get(CS_KEY.SOCIAL_SERVICE_TYPE)
            next_url = uri_for(
                'profile-sync',
                account_group_id=account_group_id,
                msid=self.msid,
                sync_dialog_service_id=added_service.get(CS_KEY.SOCIAL_SERVICE_ID)
            )
            if service_type in [CS_KEY.FB_USER, CS_KEY.GP_USER]:
                add_pages_url_params = {
                    SocialSync.MSID: self.msid,
                    SocialSync.SERVICE_USER_ID: service_id,
                }

                if service_type == CS_KEY.FB_USER:
                    new_service_type = CS_KEY.FB_PAGE
                elif service_type == CS_KEY.GP_USER:
                    new_service_type = 'GMB_LOCATION'
                    add_pages_url_params['origin'] = self.request.GET.get('origin')

                add_pages_url_params[SocialSync.SERVICE] = new_service_type

                # redirect to add pages
                next_url = uri_for('ms-general', _full=True)
                next_url = '{}edit/account/{}/app/social-account-connect?{}'.format(
                    next_url, account_group_id, urllib.parse.urlencode(add_pages_url_params))
        self.response.location = next_url
        self.response.status_int = 302
