""" Admin view for microsite analytics. """
from datetime import datetime

from app.i18n import InternationalizationMixin
from app.views.admin import AuthenticatedEditSiteView, AUTHENTICATE_EDIT_SITE_MIDDLEWARE
from app.domain.middleware.msid_selector import MsidArgumentRequiredMiddleware
from app.domain.analytics_v3 import GoogleAnalyticsV3GraphData
from vapi.handler import ApiHandler


class ViewAnalytics(AuthenticatedEditSiteView, InternationalizationMixin):
    """ Request handler for viewing analytics about a given microsite. """

    middleware = AUTHENTICATE_EDIT_SITE_MIDDLEWARE + [MsidArgumentRequiredMiddleware()]

    def get(self, account_group_id):  # pylint: disable=unused-argument
        """ GET """
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')

        translations = self.get_translations(self.request, session=self.session, partner_id=self.pid)
        date_format_error = translations.get('MY_LISTING', {}).get('ANALYTICS', {}).get('DATE_FORMAT_ERROR', '')
        if start_date:
            try:
                start_date = datetime.strptime(start_date, ApiHandler.ISO_DATE_FORMAT_STR).date()
            except ValueError:
                return self.response.write(date_format_error.format(date_field='start_date'))

        if end_date:
            try:
                end_date = datetime.strptime(end_date, ApiHandler.ISO_DATE_FORMAT_STR).date()
            except ValueError:
                return self.response.write(date_format_error.format(date_field='end_date'))

        ga = GoogleAnalyticsV3GraphData(self.microsite.msid, google_analytics_id=self.partner.google_analytics_id,
                                        start_date=start_date, end_date=end_date)
        data = ga.get_data()

        params = {
            'traffic_data': False,
            'event_data': False,
            'start_date': ga.start_date,
            'end_date': ga.end_date
        }
        if data:
            params.update({
                'traffic_data': data['traffic'],
                'event_data': data['events'],
                'microsite': self.microsite,
            })
        return self.render_response('admin/html/microsite/view-analytics.html', **params)
