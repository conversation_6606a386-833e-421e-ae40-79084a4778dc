""" SSO integration views. """
import logging
from urllib.parse import urlparse, urlunparse

import vauth.foundational_products
import vauth_configuration
from app.domain.vbc import get_vbc_pid
from app.models.microsite import AccountGroupModel, Microsite
from app.views.admin import AuthenticatedEditSiteView
from vauth.foundational_products.constants import Url<PERSON><PERSON><PERSON>

try:
    from urllib.parse import parse_qs
except ImportError:
    from cgi import parse_qs
from urllib.parse import urlencode
from app.views import BaseHandler
from app.domain.url_mappings import get_host_pid_mapping
from google.appengine.api.namespace_manager.namespace_manager import set_namespace


class SsoIntegrationHandler(BaseHandler):
    """ Use the SSO token from Vauth to retrieve the msid """
    
    def get(self):
        """ GET """
        partner = get_host_pid_mapping(self.request.host)
        if not partner:
            self.abort(404)
        set_namespace(partner.pid)

        sso_token_from_vauth = vauth.foundational_products.get_sso_token()
        sso_token_from_request = self.request.GET.get('sso_token')

        # If no sso token on current request, redirect to edit view (user authorized for single account).
        authorized_single_account = not sso_token_from_request and sso_token_from_vauth

        # If sso token on request matches vauth sso token, redirect to edit view (user is authorized for multiple
        # accounts and specific account determined by query parameter on request).
        authorized_correct_account = sso_token_from_vauth == sso_token_from_request

        if authorized_single_account or authorized_correct_account:
            microsite_account_id = vauth_configuration.get_valid_account_id()
            microsite_account = Microsite.get_by_msid(microsite_account_id, pid=partner.pid)
            account_group_id = microsite_account.account_group.account_group_id
            self.redirect_to('edit-microsite', account_group_id=account_group_id)
        else:
            # Expire session (user is not authorized or authorized for multiple accounts 
            # but session in vauth does not match session on request)
            vauth.foundational_products.sign_out()

            # Redirect to VBC to reauthorize
            next_url = vauth.foundational_products.VAuthMiddleware.build_legacy_sso_auth_url(self.request,
                                                                                             sso_token_from_request)
            scheme, netloc, path, params, query, fragment = urlparse(next_url)
            query_params = {key: value[0] for key, value in parse_qs(query).items()}
            if not UrlKeys.SSO_TOKEN in query_params:
                query_params.update({UrlKeys.SSO_TOKEN: sso_token_from_request})

            auth_url = urlunparse([scheme, netloc, path, params, urlencode(query_params), fragment])
            self.redirect(str(auth_url))


class RedirectToSocialMarketingHandler(AuthenticatedEditSiteView):
    """
    Redirect the user to their Social Marketing account.
    The Social Marketing account is determined by the account group the Microsite belongs to.
    """

    def get(self, account_group_id):
        """ GET """
        account_group = AccountGroupModel.build_key(account_group_id=account_group_id).get()
        social_marketing_account_id = account_group.smid
        if not social_marketing_account_id:
            logging.error('Unable to find Social Marketing account for Microsite account group "%s" and pid "%s". '
                          'User will not be redirected.', account_group_id, self.microsite.pid)
            return self.abort(404)

        sm_pid = get_vbc_pid(self.pid)
        next_url = '/account/settings/'
        signed_url = vauth.foundational_products.generate_sso_link(
            social_marketing_account_id, sm_pid, vauth.foundational_products.ProductKeys.SOCIAL_MARKETING,
            next_url=next_url)
        return self.redirect(signed_url)


class RedirectToRepManCitationsHandler(AuthenticatedEditSiteView):
    """
    Redirect the user to the citations page of their RepMan account.
    The RepMan account is determined by the account group the Microsite belongs to.
    """

    def get(self, account_group_id):
        """ GET """
        account_group = AccountGroupModel.build_key(account_group_id=account_group_id).get()
        repman_account_id = account_group.srid
        if not repman_account_id:
            logging.error('Unable to find RepMan account for Microsite account group "%s" and pid "%s". '
                          'User will not be redirected.', account_group_id, self.pid)
            return self.abort(404)

        next_url = '/visibility/citations/'
        # RepInt currently does not support next_url so adding additional sso_path
        signed_url = vauth.foundational_products.generate_sso_link(repman_account_id, account_group.partner_id,
                                                                   vauth.foundational_products.ProductKeys.REP_INTEL,
                                                                   next_url=next_url, sso_path=next_url)
        return self.redirect(signed_url)
