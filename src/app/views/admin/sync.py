""" Admin views for sync settings """
import json
import logging

from webapp2 import uri_for

import settings
from app.constants import LSP_SERVICE_TYPE_UBERALL, PUBLIC_REPUTATION_PARTNER_ID
from app.domain.core.listing_sync_pro import get_listing_sync_pro_directory_statuses_for_activated_lsp
from app.domain.google_my_business import GoogleMyBusinessSync
from app.models.microsite import AccountGroupModel
from app.views.api.ajax import BaseAjaxHandler
from coresdk.base import CSApiException

from app.domain.social import SitesVSocial, SocialSync
from app.domain.sales_tool import get_sales_person_details
from app.views.admin import AuthenticatedEditSiteView
from coresdk.keys import API_KEY as CS_KEY
from routes import V1_AJAX_URL
from vconfig import FeatureClient


class ProfileSync(AuthenticatedEditSiteView):
    """ Profile sync page """

    @staticmethod
    def get_hide_facebook_for_uberall_lsp(social_sync_profiles, service_type):
        """
        :param msid: microsite id
        :param account_group_id: account group id
        :param sync_dialog_service_id: sync dialog service id
        :param service_type: Yext or Uberall
        :return:
        """
        hide_facebook_for_uberall_lsp = False
        if service_type == LSP_SERVICE_TYPE_UBERALL:
            for service in social_sync_profiles:
                if service.get('name') == 'Facebook':
                    for account in service.get('accounts'):
                        if account.get('socialSyncFlag'):
                            hide_facebook_for_uberall_lsp = True
                            break
                    break

        return hide_facebook_for_uberall_lsp

    @staticmethod
    def add_instagram_to_social_profiles(social_sync_profiles):
        """
        Add the info for Instagram to the Facebook social profile record.
        :param social_sync_profiles: List of social profile dicts.
        """
        for service in social_sync_profiles:
            if service.get('name') == 'Facebook':
                service.update({'secondary_account': 'Instagram'})
                for account in service.get('accounts'):
                    link = 'https://www.instagram.com/explore/locations/{}/'.format(account.get('facebookPageId'))
                    account.update({'secondary_account_link': link})

        return social_sync_profiles

    def get(self, account_group_id):
        """ GET """
        account_group = AccountGroupModel.get(account_group_id)

        # When removing this flag, don't forget stuff in the javascript and html
        feature_client = FeatureClient(settings.ENVIRONMENT_NAME)
        show_public_reputation_feature_flag = feature_client.is_feature_enabled_async('show_pr_for_lsp',
                                                                                      account_group.partner_id,
                                                                                      account_group.market_id,
                                                                                      )

        sync_dialog_service_id = self.request.GET.get(SocialSync.SYNC_DIALOG_SERVICE_ID)

        sales_person_details = get_sales_person_details(account_group_id)

        listing_sync_pro_active_for_account = True if account_group.listing_sync_pro and \
                account_group.listing_sync_pro.is_active else False

        if not listing_sync_pro_active_for_account and account_group.partner_id == PUBLIC_REPUTATION_PARTNER_ID:
            # Want to make sure Yext has listings for the account before faking them having LSP.
            listing_sync_pro_active_for_account = \
                bool(get_listing_sync_pro_directory_statuses_for_activated_lsp(account_group_id))

        listing_sync_pro_purchase_date = account_group.listing_sync_pro.purchase_date \
            if listing_sync_pro_active_for_account else None

        social_sync = SocialSync(self.msid, account_group_id, sync_dialog_service_id=sync_dialog_service_id)
        social_sync_profiles = social_sync.get_social_sync_profiles()

        social_sync_profiles = ProfileSync.add_instagram_to_social_profiles(social_sync_profiles)

        gmb_sync_profile = social_sync.get_gmb_sync_profile(origin='listing-sync')
        social_token_broken = False
        if len(gmb_sync_profile['accounts']) > 0:
            gmb_location = GoogleMyBusinessSync(self.microsite.spid).get_connected_location()
            social_token_broken = gmb_location['isTokenBroken'] if 'isTokenBroken' in gmb_location else False

        context = {
            SocialSync.SOCIAL_PROFILES: json.dumps(social_sync_profiles),
            'tab': 'profile-sync',
            'page_title': 'Listing Sync',
            'sales_person_json': sales_person_details,
            'show_price': self.wl_listing_sync_pro_show_sell_price,
            'monthly_price': self.wl_listing_sync_pro_sell_price_monthly,
            'annual_price': self.wl_listing_sync_pro_sell_price_annual,
            'num_synced_accounts': SocialSync(self.microsite.msid, account_group_id).get_number_synced_profiles(),
            'listing_sync_pro_active_for_account': listing_sync_pro_active_for_account,
            'listing_sync_pro_purchase_date': listing_sync_pro_purchase_date,
            # When removing this flag, don't forget stuff in the javascript and html
            'show_public_reputation_feature_flag': show_public_reputation_feature_flag.get_result(),
            'business_profile_url': uri_for('edit-business-profile', account_group_id=account_group.account_group_id),
            'social_token_broken': social_token_broken,
        }
        if social_token_broken:
            context['reconnect_link'] = gmb_sync_profile['connect_link']
        return self.render_response('admin/html/profile-sync.html', **context)

    def post(self, account_group_id):
        """
        POST
        takes an msid, syncing_ssid, and service and turns syncing on for only syncing_ssid
        if syncing_ssid is missing or null then all syncing is turned off for the service type given
        """

        # Get variables from user's data
        service_type = self.request.params.get(SocialSync.SERVICE)
        syncing_ssid = self.request.params.get(SocialSync.SYNCING_SSID)

        SocialSync(self.msid, account_group_id).set_sync_flag(service_type, syncing_ssid=syncing_ssid)

        return self.render_json(SocialSync.MESSAGE_SYNC_UPDATE_SUCCESS)


class AddSocialService(AuthenticatedEditSiteView):
    """ Handler that adds Social Service - Facebook Page, Google+ page"""

    def _get_available_services(self, can_search):
        """
        Get available services
        """
        sites_vsocial = SitesVSocial(self.msid)
        service_user_id = self.request.get(SocialSync.SERVICE_USER_ID)
        service_type = self.request.get(SocialSync.SERVICE)
        search = self.request.get('search')

        service_info_map = {
            CS_KEY.FB_PAGE: sites_vsocial.fetch_facebook_page_infos,
        }

        try:
            services = service_info_map.get(service_type, lambda x: [])(service_user_id)
        except CSApiException:
            self.set_flash('An error occurred retrieving your information', 'Error')
            services = {}

        validServices = {}
        if can_search and search:
            for serviceItem in services:
                if search.lower() in services[serviceItem]['name'].lower():
                    item = {serviceItem: services[serviceItem]}
                    validServices.update(item)
            return validServices
        return services

    def _next_url(self, account_group_id, sync_dialog_service_id=None):
        """ The url to redirect to after pages have been added. """
        if sync_dialog_service_id:
            return self.uri_for('profile-sync', account_group_id=account_group_id,
                                msid=self.msid, sync_dialog_service_id=sync_dialog_service_id)
        else:
            return str(self.request.get(SocialSync.NEXT_URL) or self.uri_for('profile-sync',
                                                                             account_group_id=account_group_id))

    def _validate(self, service_type, service_user_id, account_group_id):
        """
        :return: True or False
        """
        if not service_user_id or not service_type:
            self.set_flash('An error occurred retrieving the User. Please try again.', 'Error')
            return self.redirect(self._next_url(account_group_id))

    def _setup_facebook_context(self, account_group_id, context):
        """
        Set up context for Facebook
        """
        self.crumbtrail = [
            ('Profile Sync', self.uri_for('profile-sync', account_group_id=account_group_id)),
            ('Add Facebook Page', None)
        ]
        context['create_url'] = 'http://www.facebook.com/pages/create.php'
        context['administrator_url'] = 'https://www.facebook.com/help/***************'
        context['service_user_type'] = 'Page'
        context['service_company'] = 'Facebook'

    def get(self, account_group_id):
        """ GET """
        self._validate(
            self.request.get(SocialSync.SERVICE), self.request.get(SocialSync.SERVICE_USER_ID), account_group_id
        )

        context = {
            'create_url': '',
            'administrator_url': '',
            'service_user_type': '',
            'service_company': '',
            'service_type': self.request.get(SocialSync.SERVICE),
            'service_user_id': self.request.get(SocialSync.SERVICE_USER_ID),
            'tab': 'profile-sync',
            'social_profile_id': self.account_group.social_profile_id,
            'msid': self.msid,
            'search': self.request.get('search', '')
        }

        if self.request.get(SocialSync.SERVICE) == CS_KEY.FB_PAGE:
            context['get_load_url'] = V1_AJAX_URL.FACEBOOK_PAGES_FETCH

        service_type = self.request.get(SocialSync.SERVICE)
        if service_type == CS_KEY.FB_PAGE:
            self._setup_facebook_context(account_group_id, context)

        context['page_title'] = 'Add {} {}'.format(context.get('service_company'), context.get('service_user_type'))

        return self.render_response('admin/html/social-service-add.html', **context)

    def post(self, account_group_id):
        """ POST """
        service_type = self.request.get('serviceType')
        service_user_id = self.request.get('serviceUserId')
        service = self.request.get('service')

        if not service_type or not service_user_id or not service:
            return self.abort(400)

        try:
            sites_vsocial = SitesVSocial(self.msid)
            if service_type == CS_KEY.FB_PAGE:
                result = sites_vsocial.add_facebook_pages_by_id(service_user_id,
                                                                service)
            else:
                raise ValueError('Unrecognized service type "%s". Service will not be added.',
                                 service_type)
            return self.render_json({
                'url': self._next_url(account_group_id, sync_dialog_service_id=result.get(CS_KEY.SOCIAL_SERVICE_ID))
            })
        except Exception:
            logging.exception("An error occurred while adding %s service with id '%s' for user '%s'",
                              service_type, service,
                              service_user_id)
            self.set_flash('An error occurred adding the service. Please try again.', 'Error')
            self.redirect(uri_for('profile-sync', msid=self.msid))


class FetchPageInfoBaseAjaxHandler(BaseAjaxHandler):
    """ Handler for fetching facebook page info """

    REQUIRES_HTTPS = False
    ALLOWED_METHODS = frozenset(['GET', 'POST'])

    REQUIRED_ARGS = ['msid', 'social_profile_id', 'serviceUserId']
    ALLOWED_ARGS = ['nextPageCursor', 'search']

    def process(self, args):
        """ Fetch facebook page """
        # set page size based on if this is a fetch for display, or a search
        page_size = 100
        if args.get('search'):
            page_size = 500
        core_response = self._fetch_page_info_from_core(args.get('msid'), args.get('social_profile_id'),
                                                        args.get('serviceUserId'), args.get('nextPageCursor'),
                                                        page_size=page_size, search=args.get('search'))
        raw_pages = core_response.get('pages')
        cursor = core_response.get('cursor')
        processed_pages = self._get_page_info_data(raw_pages)
        return {'pages': processed_pages, 'cursor': cursor}

    def _get_page_info_data(self, raw_pages):
        """
        :return: A list of formatted page json objects
        """
        page_data = []
        if raw_pages:
            for page in raw_pages:
                _page = {
                    'page_id': page['id'],
                    'name': page.get('name') or page.get('displayName'),
                    'location': page.get('location'),
                    'url': page.get('url') or page.get('link')
                }
                page_data.append(_page)
        return page_data

    def _fetch_page_info_from_core(self, msid, social_profile_id, service_user_id, next_page_cursor, page_size, search):
        """ See method name """
        raise NotImplementedError()

class FetchFacebookPageInfoAjaxHandler(FetchPageInfoBaseAjaxHandler):
    """ Handler for fetching facebook page info """

    VERSION = '1.0'
    URL = V1_AJAX_URL.FACEBOOK_PAGES_FETCH
    DOC_NAME = 'Fetch Facebook page info for user'

    def _fetch_page_info_from_core(self, msid, social_profile_id, service_user_id, next_page_cursor, page_size, search):
        """ See method name """
        sites_vsocial = SitesVSocial(msid)
        return sites_vsocial.fetch_paged_facebook_page_infos(social_profile_id, service_user_id,
                                                             cursor=next_page_cursor, page_size=page_size,
                                                             search=search)


class DisconnectSocialService(AuthenticatedEditSiteView):
    """
    Handler for removing the MS client tag from a service.
    """

    def post(self):
        """ POST """
        ssid = self.request.POST.get(SocialSync.SSID)
        client_tags = json.loads(self.request.POST.get('clientTags'))
        if not ssid:
            self.abort(400)
        vsocial = SitesVSocial(self.msid)
        vsocial.remove_service_from_social_profile(ssid, client_tags)
        return self.render_json(SocialSync.MESSAGE_SOCIAL_SERVICE_DISCONNECT_SUCCESS)
