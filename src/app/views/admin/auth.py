""" Handlers for admin auth. """
from app.views import BaseHandler
from settings import GL<PERSON><PERSON>L_MIDDLEWARE
import vauth.foundational_products


class LogoutHandler(BaseHandler):
    """ Handler for admin logout requests. """

    middleware = GLOBAL_MIDDLEWARE
    
    def get(self):
        """ GET """
        vauth.foundational_products.flush_current_session()
        return self.redirect(self.uri_for('admin-home'))

class LogoutAPIHandler(BaseHandler):
    """ Handler for SSO logout requests that does not redirect to another view. """

    middleware = GLOBAL_MIDDLEWARE
    def get(self):
        """ GET """
        vauth.foundational_products.flush_current_session()
        return self.render_response('html/logout.html')
