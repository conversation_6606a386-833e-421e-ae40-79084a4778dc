""" Admin views for seo """
from app.domain.url_mappings import lookup_hostslug_msid_mapping_for_msid
from app.views.admin import AuthenticatedEditSiteView


class SeoView(AuthenticatedEditSiteView):
    """ View for the SEO tab """

    def get(self, account_group_id):  # pylint: disable=unused-argument
        """ GET """

        context = {
            'slug_mappings': lookup_hostslug_msid_mapping_for_msid(self.pid, self.microsite.msid),
        }
        return self.render_response('admin/html/microsite/seo.html', **context)
