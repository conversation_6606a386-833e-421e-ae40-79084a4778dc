""" Add resource to microsite. """
from google.appengine.ext import blobstore
from webapp2 import Response
import vform
from app.constants import PHONE_THEME, DESKTOP_THEME
from app.layouts import LAYOUTS
from app.views.admin import AuthenticatedEditSiteView
from app.domain.blob_mappings import delete_blob_mapping
from app.domain.exceptions import MicrositeNotFoundException
from app.models.microsite import Microsite

MAPPING_WIDTHS = {
    'logo': 200,
    'shortcut': 57,
    'desktop_background': 240,
    'mobile_background': 100,
}


class GenerateUploadUrlView(AuthenticatedEditSiteView):
    """ New Handler that simply returns the URL for uploads """

    def get(self):
        """ Returns simply the URL to post to for the upload of a microsite resource """
        self.response.headers['Content-Type'] = 'text/plain'
        # add __vformcmd__='fileupload' so vform knows this is a file upload
        url = self.uri_for('form-branding-edit', pid=self.pid, msid=self.microsite.msid,
                           __vformcmd__=vform.handler.VFileUploadHandler.COMMAND_FILE_UPLOAD)
        bloburl = blobstore.create_upload_url(url)
        self.response.write(bloburl)
        return self.response


class DeleteMicrositeResourceView(AuthenticatedEditSiteView):
    """ Post handler to delete MsidBlobMapping. """

    def get(self):
        """ GET """
        self.abort(405)

    def post(self, category):
        """ POST """
        delete_blob_mapping(self.microsite.msid, category, pid=self.pid)
        return Response()


class SelectMicrositeThemeView(AuthenticatedEditSiteView):
    """ View for selecting a microsite theme. """

    def get(self):
        """ GET out of here"""
        self.abort(405)

    def post(self):
        """ POST """
        self.update_layout(self.microsite.msid, self.pid, self.request.POST.get('layout_id'))
        return Response()

    def update_layout(self, msid, pid, layout_id):
        """ Updates the microsite theme with the given layout id """
        if not layout_id:
            raise ValueError('layout_id is required')

        selected_layout = LAYOUTS.get(layout_id)
        if not selected_layout:
            raise ValueError("Unable to get layout definition for id(%s)" % layout_id)

        key = Microsite.build_key(msid, pid=pid)
        ms_model = key.get()
        if not ms_model:
            raise MicrositeNotFoundException('Microsite with msid: "%s" does not exist' % msid)

        ms_model.phone_theme = selected_layout[PHONE_THEME]
        ms_model.desktop_theme = selected_layout[DESKTOP_THEME]
        ms_model.put()
