""" Views for Google My Business syncing """
import logging

import vapi

from app.domain.google_my_business import GoogleMyBusinessSync, GoogleMyBusinessInsightsData
from app.domain.social import SocialSync
from app.views.admin import AuthenticatedEditSiteView

MAX_GOOGLE_PAGE_SIZE = 100

class GoogleMyBusinessInsightsHandler(AuthenticatedEditSiteView):
    """
    Show some stats from Google regarding the business' report insights
    """
    def get(self, account_group_id):
        """ GET """
        gmb_sync_profile = SocialSync(self.msid, account_group_id).get_gmb_sync_profile(origin='gmb-insights')

        start_date = has_gmb_location = False
        is_gmb_verified = False
        social_token_broken = False
        if len(gmb_sync_profile['accounts']) > 0:  # Connected locations will be in the accounts list here
            has_gmb_location = True
            start_date = GoogleMyBusinessInsightsData(self.microsite.spid).get_stats_start_date()
            gmb_location = GoogleMyBusinessSync(self.microsite.spid).get_connected_location()
            is_gmb_verified = gmb_location['isVerified'] if 'isVerified' in gmb_location else False
            social_token_broken = gmb_location['isTokenBroken'] if 'isTokenBroken' in gmb_location else False

        context = {
            'has_gmb_location': has_gmb_location,
            'is_gmb_verified': is_gmb_verified,
            'tab': 'gmb-insights',
            'gmb_info': gmb_sync_profile,
            'social_profile_id': self.microsite.spid,
            'page_title': 'Google My Business Insights',
            'start_date': start_date,
            'social_token_broken': social_token_broken,
            'reconnect_link': gmb_sync_profile['connect_link']
        }

        return self.render_response('admin/html/google-my-business-overview.html', **context)


class GetGMBInsightsDataHandler(vapi.AjaxHandler):
    """ Proxy API to the Core endpoint for this """
    VERSION = '1.0'
    ALLOWED_METHODS = frozenset([vapi.GET_METHOD])
    REQUIRES_HTTPS = False
    URL = '/_ajax/v1/get-gmb-insights-data/'

    social_profile_id = vapi.StringProperty(required=True)
    start = vapi.DateTimeProperty(required=True)
    end = vapi.DateTimeProperty(required=True)

    def process(self, args):
        return GoogleMyBusinessInsightsData(self.social_profile_id).get_data_for_chart(
            self.start,
            self.end
        )

    def check_credentials(self):
        return True


class SelectGMBLocationHandler(AuthenticatedEditSiteView):
    """
    Select a Google My Business location that the specified Google user has access to.

    The handler loads a list of accounts that the user has access to. Specifically, if the user has only one account,
    it loads the locations associated with that account, too; otherwise, the locations will only be loaded when the
    user selects an account from the list.
    """

    def get(self, account_group_id):
        """ GET """

        google_user_id = self.request.get(SocialSync.SERVICE_USER_ID)
        origin = self.request.get('origin')
        search = self.request.get('search')

        if origin == 'gmb-insights':
            tab = origin
            breadcrumbs_text = 'Google Insights'
            search = ''
        else:
            tab = 'profile-sync'
            breadcrumbs_text = 'Profile Sync'

        context = {
            'googleUserId': google_user_id,
            'tab': tab,
            'search': search,
            'page_title': 'Add Google My Business Location',
            'page_breadcrumbs': [{
                'text': breadcrumbs_text, 'url': self.uri_for(tab, account_group_id=account_group_id)
            }]
        }

        return self.render_response('admin/html/google-my-business.html', **context)


class GMBLocationWithSearchMixin:
    """
    Mixin for searching through all of the location results and returning ones that match the search string
    """
    def get_locations_with_search(self, gmb_sync, google_user_id, account_path_name, page_size, cursor, search):
        """ Returns all locations for the account that match the passed in search """
        result_locations = []

        locations = gmb_sync.get_locations_associated_with_account(
            google_user_id,
            account_path_name,
            cursor=cursor,
            page_size=page_size
        )
        for location in locations['locations']:
            if search.lower() in location['displayName'].lower():
                result_locations.append(location)

        if locations['cursor']:
            result_locations += self.get_locations_with_search(gmb_sync, google_user_id, account_path_name,
                                                               page_size, locations['cursor'], search)
        return result_locations


class GetGMBAccountsHandler(AuthenticatedEditSiteView, GMBLocationWithSearchMixin):
    """
    AJAX handler for getting paged GMB Accounts
    """
    def get(self):
        """ GET """
        google_my_business_sync = GoogleMyBusinessSync(self.microsite.spid)
        google_user_id = self.request.get('googleUserId')
        cursor = self.request.get('cursor')
        page_size = self.request.get('pageSize', None)
        search = self.request.get('search')
        if not google_user_id:
            return self.abort(400)

        accounts = google_my_business_sync.get_accounts_associated_with_google_user(
                google_user_id,
                cursor=cursor,
                page_size=page_size
        )
        if not search:
            return self.render_json(accounts)

        count = 1

        while accounts['cursor'] is not None:
            count += 1
            logging.info("MS-2693 Number of times we have called to get GMB accounts in this request: %s", count)
            more_accounts = google_my_business_sync.get_accounts_associated_with_google_user(
                google_user_id,
                cursor=accounts['cursor'],
                page_size=page_size
            )
            accounts['accounts'].append(more_accounts['accounts'])
            accounts['cursor'] = more_accounts['cursor']

        result_accounts = {
            'accounts': [],
            'cursor': None
        }
        for account in accounts['accounts']:
            result_locations = self.get_locations_with_search(
                google_my_business_sync,
                google_user_id,
                account['accountPathName'],
                MAX_GOOGLE_PAGE_SIZE,
                cursor,
                search,
            )
            if len(result_locations) > 0:
                account['locations'] = result_locations
                result_accounts['accounts'].append(account)
        return self.render_json(result_accounts)


class GetGMBLocationsForAccountHandler(AuthenticatedEditSiteView, GMBLocationWithSearchMixin):
    """ Get the GMB locations associated with the GMB account """
    def get(self):
        """ GET """
        google_my_business_sync = GoogleMyBusinessSync(self.microsite.spid)
        google_user_id = self.request.get('googleUserId')
        account_path_name = self.request.get('accountPathName')
        cursor = self.request.get('cursor')
        page_size = self.request.get('pageSize', None)
        search = self.request.get('search')

        if not google_user_id or not account_path_name:
            return self.abort(400)

        if not search:
            return self.render_json(google_my_business_sync.get_locations_associated_with_account(
                google_user_id,
                account_path_name,
                cursor=cursor,
                page_size=page_size
            ))

        result_locations = {
            'locations': self.get_locations_with_search(
                google_my_business_sync,
                google_user_id,
                account_path_name,
                page_size,
                cursor,
                search,
            ),
            'cursor': None
        }
        return self.render_json(result_locations)


class ConnectGMBLocationHandler(AuthenticatedEditSiteView):
    """ Handler to add a GMB location to Core """
    def post(self):
        """ POST """
        google_my_business_sync = GoogleMyBusinessSync(self.microsite.spid)
        location_path_name = self.request.get('locationPathName')
        google_user_id = self.request.get('googleUserId')
        origin = self.request.get('origin')
        if not location_path_name or not google_user_id:
            return self.abort(400)
        google_my_business_sync.connect_location_to_social_profile(location_path_name, google_user_id)

        if origin == 'gmb-insights':
            redirect_url = self.uri_for(origin, account_group_id=self.microsite.agid)
        else:
            redirect_url = self.uri_for(
                'profile-sync',
                account_group_id=self.microsite.agid,
                msid=self.msid,
                sync_dialog_service_id=location_path_name
            )
        return self.render_json({'url': redirect_url})


class DisconnectGMBLocationHandler(AuthenticatedEditSiteView):
    """ Handler to remove GMB location from core if disconnected """
    def post(self):
        """ POST """
        google_my_business_sync = GoogleMyBusinessSync(self.microsite.spid)
        location_path_name = self.request.get('ssid')
        google_user_id = self.request.get('service_user_id')
        if not location_path_name or not google_user_id:
            return self.abort(400)
        google_my_business_sync.disconnect_location_from_social_profile(
            location_path_name,
            google_user_id
        )
        return self.render_json(SocialSync.MESSAGE_SOCIAL_SERVICE_DISCONNECT_SUCCESS)
