""" Admin views for listings management """
from app.domain.middleware.msid_selector import MsidArgumentRequiredMiddleware
from app.domain.listing import get_listing_history_category_changes
from app.views.admin import AuthenticatedEditSiteView, AUTHENTICATE_EDIT_SITE_MIDDLEWARE


class GetListingHistoryHandler(AuthenticatedEditSiteView):
    """
    Handler to get listing history
    """

    middleware = AUTHENTICATE_EDIT_SITE_MIDDLEWARE + [MsidArgumentRequiredMiddleware()]

    def post(self):
        """
        POST
        """
        lid = self.request.params.get("lid")
        if not lid:
            self.abort(400)

        activated_date = self.request.params.get("activatedDate")
        history = get_listing_history_category_changes(lid, activated_date)

        return self.render_json(history)
