""" Admin microsite page views. """
import logging
import re
import webapp2
from google.appengine.api.images import <PERSON><PERSON><PERSON> as ImageError
from google.appengine.ext import blobstore

from werkzeug.wrappers import Response
from werkzeug.utils import import_string

from app.i18n import InternationalizationMixin
from app.constants import MAX_NAVIGATION, NAVIGATION_SLUG_MAX_LENGTH, TEMPLATES_EDITABLE, TEMPLATES_COMPLEX_PAGES, \
    REVIEWS
from app.domain.middleware.msid_selector import MsidArgumentRequiredMiddleware
from app.domain.page import get_page
from app.domain.pageid_blob_mapping import process_temp_blobinfo
from app.domain.url_mappings import lookup_hostslug_msid_mapping_for_msid
from app.domain.utils import slugify
from app.domain.workflow.page import create_page, process_page, update_page
from app.domain.workflow.navigation import UpdateNavigationForm
from app.domain.navigation import update_navigation_entity
from app.views import <PERSON><PERSON><PERSON><PERSON>
from app.views.admin import AuthenticatedEditSiteView, AUTHENTICATE_EDIT_SITE_MIDDLEWARE
from app.views.blobstore import BlobstoreUploadMixin


class PagesView(AuthenticatedEditSiteView):
    """ View for displaying the pages of a microsite. """

    middleware = AUTHENTICATE_EDIT_SITE_MIDDLEWARE + [MsidArgumentRequiredMiddleware()]

    def get(self, account_group_id):  # pylint: disable=unused-argument
        """ GET """

        pages = [(nav, get_page(nav.pageid, self.msid, pid=self.pid)) for nav in self.microsite.main_navigation
                 if self.wl_review_generation_enabled or nav.icon.lower() != REVIEWS.lower()]
        context = {
            'max_navigation': MAX_NAVIGATION,
            'page_templates': TEMPLATES_EDITABLE - {None if self.wl_review_generation_enabled else REVIEWS},
            'pages': pages,
            'tab': 'location',
        }

        return self.render_response('admin/html/microsite/page/index.html', **context)


class CreatePageView(AuthenticatedEditSiteView, InternationalizationMixin):
    """ Create page for microsite in admin view. """

    middleware = AUTHENTICATE_EDIT_SITE_MIDDLEWARE + [MsidArgumentRequiredMiddleware()]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.page_template = None
        self.translations = None

    def get(self, account_group_id):
        """ GET """
        self.translations = self.get_translations(self.request,
                                                  session=self.session,
                                                  partner_id=self.pid).get('MY_LISTING', {})
        self.page_template = self.request.get('template')

        translated_template_type = self.translations.get('PAGES', {}).get(self.page_template, '')
        context = {
            'form': self.form,
            'page_template': self.page_template,
            'tab': 'location',
            'page_title': self.translations.get('CREATE_PAGE', {}).get('TITLE', '{page_type}').format(
                page_type=translated_template_type),
            'page_breadcrumbs': [{
                'text': self.translations.get('TABS', {}).get('PAGES', ''),
                'url': self.uri_for('edit-website', account_group_id=account_group_id, _fragment='pages')
            }],
        }

        if self.page_template in TEMPLATES_COMPLEX_PAGES:
            context['page_form_template'] = 'admin/html/microsite/page/form_templates/%s.html' % self.page_template

        return self.render_response('admin/html/microsite/page/create.html', **context)

    def post(self, account_group_id):
        """ POST """
        self.translations = self.get_translations(self.request,
                                                  session=self.session,
                                                  partner_id=self.pid).get('MY_LISTING', {})

        msid = self.microsite.msid
        self.page_template = self.request.POST.get('template')
        is_field_required_error = False

        if self.form.h1.data:
            # set the Title and Tab Name to the Header value
            self.form.title.data = self.form.h1.data
            # set the Slug to the slugified Tab Name value - leave room for '-#' to be appended if necessary
            self.form.slug.data = slugify(self.form.tab_name.data, max_length=(NAVIGATION_SLUG_MAX_LENGTH - 3))
        elif self.page_template == REVIEWS:
            self.form.title.data = self.microsite.name
            self.form.slug.data = 'review'

        if self.form.validate():
            pageid = create_page(self.form, self.microsite)
            errors = process_page(self.form, pageid, msid, pid=self.pid)

            if errors:
                self.set_flash(errors, self.ERROR_TITLE)
                return self.redirect_to('edit-site-page', account_group_id=account_group_id, msid=msid, pageid=pageid)

            self.set_flash(self.translations.get('CREATE_PAGE', {}).get('PAGE_ADDED_SUCCESSFULLY', ''),
                           self.translations.get('SUCCESS', ''))
            return self.redirect_to('edit-website', account_group_id=account_group_id, msid=msid)
        else:
            for error_messages in self.form.errors.values():
                is_field_required_error = is_field_required_error or len(['required' in e for e in error_messages])

        error_message = self.translations.get('CREATE_PAGE', {}).get('FORM_CONTAINS_ERRORS', '')
        if is_field_required_error:
            error_message = self.translations.get('CREATE_PAGE', {}).get('REQUIRED_FIELDS_MISSING', '')
        self.set_flash(error_message, self.translations.get('ERROR', ''))

        return self.get(account_group_id)

    @webapp2.cached_property
    def form(self):
        """ Create page form based on provided template. """
        form_class = import_string('app.domain.workflow.page.%sPageForm' % self.page_template)
        form_class.template.type = 'HiddenField'
        form = form_class(self.request.POST, template=self.page_template)

        # set these fields here instead of rendering as hidden fields to avoid spoofing
        form.msid.data = self.msid
        form.pid.data = self.pid

        translations = self.translations.get('PAGE_FORMS', {})
        form.order.label.text = translations.get('TAB_POSITION', '')
        form.h1.label.text = translations.get('HEADING', '')
        form.tab_name.label.text = translations.get('TAB_NAME', '')
        form.meta_keywords.label.text = translations.get('META_KEYWORDS', '')
        form.meta_description.label.text = translations.get('META_DESCRIPTION', '')
        form.top_content.label.text = translations.get('TOP_CONTENT', '')
        form.bottom_content.label.text = translations.get('BOTTOM_CONTENT', '')
        if self.page_template == 'Images':
            form.images.label.text = translations.get('IMAGES', '')
            form.mobile_gallery_style.label.text = translations.get('MOBILE_GALLERY_STYLE', '')
            form.mobile_gallery_style.description = translations.get('MOBILE_GALLERY_STYLE_DESCRIPTION', '')
            form.desktop_gallery_style.label.text = translations.get('DESKTOP_GALLERY_STYLE', '')
            form.desktop_gallery_style.description = translations.get('DESKTOP_GALLERY_STYLE_DESCRIPTION', '')
        if self.page_template == 'Videos':
            form.videos.label.text = translations.get('VIDEOS', '')
        if self.page_template == 'Contact':
            form.email.label.text = translations.get('EMAIL', '')
            form.email.description = translations.get('EMAIL_DESCRIPTION', '')
        if self.page_template == 'Coupons':
            form.coupons.label.text = translations.get('COUPONS', '')

        return form


class EditPageView(AuthenticatedEditSiteView, InternationalizationMixin):
    """ Edit page in admin view """

    middleware = AUTHENTICATE_EDIT_SITE_MIDDLEWARE + [MsidArgumentRequiredMiddleware()]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.translations = None

    # pylint: disable=W0613
    # Unused argument 'pageid' is actually used for self.page
    def get(self, account_group_id, pageid):
        """ GET """
        self.translations = self.get_translations(self.request,
                                                  session=self.session,
                                                  partner_id=self.pid).get('MY_LISTING', {})
        translated_template_type = self.translations.get('PAGES', {}).get(self.page.template, '')

        context = {
            'form': self.form,
            'nav_form': self.nav_form,
            'page': self.page,
            'tab': 'location',
            'page_title': self.translations.get('EDIT_PAGE', {}).get('TITLE', '{page_type}').format(
                page_type=translated_template_type),
            'page_breadcrumbs': [{
                'text': self.translations.get('TABS', {}).get('PAGES', ''),
                'url': self.uri_for(
                    'edit-website', account_group_id=account_group_id, msid=self.microsite.msid, _fragment='pages'
                )
            }],
        }

        if self.page.h1:
            subtitle = self.translations.get('EDIT_PAGE', {}).get('SUBTITLE', '{page_type}').format(
                page_type=self.page.h1)
            context['page_subtitle'] = subtitle
            context['crumb_title'] = subtitle

        if self.page.template in TEMPLATES_COMPLEX_PAGES:
            context['page_form_template'] = 'admin/html/microsite/page/form_templates/%s.html' % self.page.template

        return self.render_response('admin/html/microsite/page/edit.html', **context)

    def post(self, account_group_id, pageid):
        """ POST """
        self.translations = self.get_translations(self.request,
                                                  session=self.session,
                                                  partner_id=self.pid).get('MY_LISTING', {})

        msid = self.microsite.msid
        if self.form.h1.data:
            # set the title to the Header value
            self.form.title.data = self.form.h1.data
            self.nav_form.navigation_slug.data = self.get_navigation_slug()
        elif self.page.template == REVIEWS:
            self.form.title.data = self.microsite.name
            self.nav_form.navigation_slug.data = 'review'

        # ensures both forms get validated even if one of them has errors
        is_valid = self.form.validate()
        is_valid = self.nav_form.validate() and is_valid

        if is_valid:
            update_navigation_entity(self.nav_form.data['order'],
                                     self.nav_form.data['navigation_slug'],
                                     pageid,
                                     self.microsite)

            update_page(self.form, self.microsite)
            errors = process_page(self.form, pageid, msid, pid=self.pid)
            if errors:
                self.set_flash(errors, self.translations.get('ERROR', ''))
            else:
                self.set_flash(self.translations.get('EDIT_FORM', {}).get('PAGE_UPDATED', ''),
                               self.translations.get('SUCCESS', ''))
                return self.redirect_to('edit-website', account_group_id=account_group_id, msid=msid)
        else:
            logging.info("Form validation failed. Form Errors: %s, any nav_form errors: %s", self.form.errors,
                         self.nav_form.errors)

            is_field_required_error = False
            error_message = self.translations.get('CREATE_FORM', {}).get('FORM_CONTAINS_ERRORS', '')

            for error_messages in self.form.errors.values():
                is_field_required_error = is_field_required_error or len(['required' in e for e in error_messages])

            if is_field_required_error:
                error_message = self.translations.get('CREATE_FORM', {}).get('REQUIRED_FIELDS_MISSING', '')
            elif self.nav_form.errors.get('navigation_slug'):
                error_message = self.translations.get('EDIT_FORM', {}).get('PAGE_ALREADY_EXISTS', '')

            self.set_flash(error_message, self.translations.get('ERROR', ''))

        return self.get(account_group_id, pageid)

    @webapp2.cached_property
    def pageid(self):
        """ Return the pageid we are editing. """
        return self.request.get('pageid') or self.request.route_kwargs['pageid']

    @webapp2.cached_property
    def page(self):
        """ Return the page being edited. """
        return get_page(self.pageid, self.msid, pid=self.pid)

    @webapp2.cached_property
    def nav_item(self):
        """ Return the nav item for the page being edited. """
        return self.microsite.get_navigation_for_pageid(self.pageid)

    @webapp2.cached_property
    def form(self):
        """ Return the appropriate update form for the page being edited. """
        form_class = import_string('app.domain.workflow.page.Update%sPageForm' % self.page.template)
        nav_item = self.microsite.get_navigation_for_pageid(self.pageid)
        form = form_class(self.request.POST, obj=self.page, tab_name=nav_item.name)

        # set these fields here instead of rendering as hidden fields to avoid spoofing
        form.template.data = self.page.template
        form.pageid.data = self.pageid
        form.msid.data = self.msid
        form.pid.data = self.pid

        translations = self.translations.get('PAGE_FORMS', {})
        form.h1.label.text = translations.get('HEADING', '')
        form.tab_name.label.text = translations.get('TAB_NAME', '')
        form.meta_keywords.label.text = translations.get('META_KEYWORDS', '')
        form.meta_description.label.text = translations.get('META_DESCRIPTION', '')
        form.top_content.label.text = translations.get('TOP_CONTENT', '')
        form.bottom_content.label.text = translations.get('BOTTOM_CONTENT', '')
        if self.page.template == 'Images':
            form.images.label.text = translations.get('IMAGES', '')
            form.mobile_gallery_style.label.text = translations.get('MOBILE_GALLERY_STYLE', '')
            form.mobile_gallery_style.description = translations.get('MOBILE_GALLERY_STYLE_DESCRIPTION', '')
            form.desktop_gallery_style.label.text = translations.get('DESKTOP_GALLERY_STYLE', '')
            form.desktop_gallery_style.description = translations.get('DESKTOP_GALLERY_STYLE_DESCRIPTION', '')
        if self.page.template == 'Videos':
            form.videos.label.text = translations.get('VIDEOS', '')
        if self.page.template == 'Contact':
            form.email.label.text = translations.get('EMAIL', '')
            form.email.description = translations.get('EMAIL_DESCRIPTION', '')
        if self.page.template == 'Coupons':
            form.coupons.label.text = translations.get('COUPONS', '')
        return form

    @webapp2.cached_property
    def nav_form(self):
        """ Return a navigation form for this pageid. """
        form = UpdateNavigationForm(self.request.POST, self.nav_item, order=self.get_page_order())

        # set these fields here instead of rendering as hidden fields to avoid spoofing
        form.pageid.data = self.pageid
        form.msid.data = self.msid
        form.pid.data = self.pid

        return form

    def get_navigation_slug(self):
        """
        Return the navigation slug
        If user didn't update the tab name, same slug as before is returned as its already unique
        otherwise, a new slug based of the new tab name value is generated
        """
        return self.nav_item.navigation_slug if self.nav_item.name == self.form.tab_name.data else \
            slugify(self.form.tab_name.data, max_length=(NAVIGATION_SLUG_MAX_LENGTH-3))

    def get_page_order(self):
        """ Return the order of the current page being edited. """
        order = 0
        for nav in self.microsite.main_navigation:
            order += 1
            if nav.pageid == self.pageid:
                return order

        raise ValueError(self.translations.get('EDIT_PAGE', {}).get('UNABLE_TO_RETRIEVE_ORDER', '{page_id}').format(
            page_id=self.pageid
        ))


class DeletePageView(AuthenticatedEditSiteView):
    """ Allows post request to delete specified page. """

    middleware = AUTHENTICATE_EDIT_SITE_MIDDLEWARE + [MsidArgumentRequiredMiddleware()]

    def post(self, pageid):
        """ POST """
        new_slugs = [nav_item.navigation_slug for nav_item in self.microsite.main_navigation
                     if nav_item.pageid != pageid]
        self.microsite.match_navigation(new_slugs)

        return Response('Success')


class GenerateUploadUrlView(AuthenticatedEditSiteView):
    """ New Handler that simply returns the URL for uploads """

    def get(self):
        """ Returns simply the URL to post to for the upload of a microsite resource """
        self.response.headers['Content-Type'] = 'text/plain'
        url = self.uri_for('upload-page-resource')
        bloburl = blobstore.create_upload_url(url)
        self.response.write(bloburl)
        return self.response


class BaseResourceUploadHandlerView(BlobstoreUploadMixin):
    """ Base handler to upload Blobs into the Blobstore """

    MAX_FILE_SIZE = 5000000  # 5MB
    IMAGE_TYPES = re.compile('image/(gif|p?jpeg|(x-)?png)')

    def validate(self, blobinfo):
        """ Validate the upload file has the correct type and size. """
        error = None
        if not self.IMAGE_TYPES.match(blobinfo.content_type):
            error = 'Filetype "%s" is not allowed. Please upload an gif, jpeg, or png image.' % blobinfo.content_type
        elif blobinfo.size > self.MAX_FILE_SIZE:
            error = 'File is too big. Size must be less than %dMB' % (self.MAX_FILE_SIZE/1000000)

        return error


class ResourceUploadHandlerView(AuthenticatedEditSiteView, BaseResourceUploadHandlerView):
    """ Handles the upload of Blobs into the Blobstore """

    def post(self):
        """ post gets called by the Blobstore via """
        upload_files = self.get_uploads()
        blobinfo = upload_files[0]

        error = self.validate(blobinfo)
        if error:
            response = {
                'error': error
            }
            self.response.status_int = 400
            return self.render_json(response, 'text/plain')

        image_info = process_temp_blobinfo(blobinfo, self.microsite.msid, pid=self.pid)
        response = {
            'blobkey': str(blobinfo.key()),
            'filename': blobinfo.filename,
            'md5_hash': blobinfo.md5_hash,
            'serving_url': image_info.url,
            'width': image_info.width,
            'height': image_info.height
        }
        return self.render_json(response, 'text/plain')


class InternalResourceUploadHandlerView(BaseHandler, BaseResourceUploadHandlerView):
    """ Handles the upload of Blobs into the Blobstore """

    def post(self, pid, msid):
        """ post gets called by the Blobstore via """
        upload_files = self.get_uploads()

        if upload_files:
            blobinfo = upload_files[0]
            error = self.validate(blobinfo)
            if error:
                self.response.status_int = 400
        else:
            error = "no uploads found"
            self.response.status_int = 404

        try:
            image_info = process_temp_blobinfo(blobinfo, msid, pid=pid)
        except ImageError as e:
            error = e.message
            self.response.status_int = 400

        if error:
            response = {
                'error': error
            }
            return self.render_json(response, 'text/plain')

        preview_url = None
        mappings = [m for m in lookup_hostslug_msid_mapping_for_msid(pid, msid) if not m.redirect_hostslug]
        if len(mappings):
            mapping = mappings[0]
            preview_url = mapping.hostslug + '/post/preview/%s/' % str(blobinfo.key())
            if not preview_url.startswith('http'):
                preview_url = "http://" + preview_url

        response = {
            'blobkey': str(blobinfo.key()),
            'filename': blobinfo.filename,
            'md5_hash': blobinfo.md5_hash,
            'serving_url': image_info.url,
            'preview_url': preview_url,
            'width': image_info.width,
            'height': image_info.height
        }
        return self.render_json(response, 'text/plain')
