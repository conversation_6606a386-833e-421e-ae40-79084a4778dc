""" Admin pages. """
import json
import logging
import urllib.parse

from webapp2 import cached_property

from domain_sdk.v1 import DomainAPI
import settings
import vauth.foundational_products
import vauth_configuration
from vautil import jsonify
import vconfig
from vax.utils.env import env_from_app_engine_env
import statsd

from app.domain.jinja_context_to_angular_app.lsp_page import sanitize_jinja_context
from app.domain.listing import is_partner_using_marketplace_for_ld
from app.domain.middleware.msid_selector import MsidArgumentRequiredMiddleware
from app.country_state import is_country_usa
from app.domain.middleware.partner_whitelabel import GetPartnerWhitelabelDataMiddleware
from app.domain.middleware.pid_selector import PidSelectorMiddleware
from app.domain.url_mappings import lookup_hostslug_msid_mapping_for_msid
from app.domain.vbc import get_vbc_pid
from app.i18n import InternationalizationMixin
from app.models.microsite import AccountGroupModel
from app.models.partner import Partner
from app.views import <PERSON>Handler
from app.views.blobstore import BlobstoreUploadMixin

AUTH_ADMIN_MIDDLEWARE = settings.GLOBAL_MIDDLEWARE + [GetPartnerWhitelabelDataMiddleware()]
# need to run MsidArgumentRequiredMiddleware before GetPartnerWhitelabelDataMiddleware
AUTHENTICATE_EDIT_SITE_MIDDLEWARE = settings.GLOBAL_MIDDLEWARE + [
    PidSelectorMiddleware(),
    MsidArgumentRequiredMiddleware(),
    GetPartnerWhitelabelDataMiddleware()
]


class AuthenticatedAdminView(BaseHandler, InternationalizationMixin):
    """ Parent class for all admin pages that require a login. """

    middleware = AUTH_ADMIN_MIDDLEWARE
    crumbtrail = None

    def __init__(self, *args, **kwargs):
        """
        initialization for authenticatedAdminView
        """
        super().__init__(*args, **kwargs)
        self.wl_logo = None
        self.wl_favicon = None
        self.wl_mobile_shortcut = None
        self.wl_products = {}

    @cached_property
    def partner(self):
        """ Returns the Partner object corresponding to self.pid. """
        return Partner.get(self.pid)

    @cached_property
    def logo_url(self):
        """ This returns the partner logo url. """
        if self.wl_logo:
            return self.wl_logo

    @cached_property
    def favicon_url(self):
        """ Returns the favicon_url for this partner. """
        if self.wl_favicon:
            return self.wl_favicon

    @cached_property
    def shortcut_url(self):
        """ Returns the shortcut_url for this partner. """
        if self.wl_mobile_shortcut:
            return self.wl_mobile_shortcut

    @cached_property
    def back_url(self):
        """ back url """
        return vauth.foundational_products.get_back_url()

    @cached_property
    def back_url_text(self):
        """ back url text """
        return vauth.foundational_products.get_back_url_text()

    @cached_property
    def create_site_url(self):
        """ Url to create a new microsite. """
        return urllib.parse.urljoin(settings.PARTNER_CENTRAL_HOST, '/manage-accounts/create/step1/')

    @property
    def is_listing_distribution_enabled(self):
        """ Returns whether or not listing distribution should be enabled or not """
        country = self.microsite.country if hasattr(self, 'microsite') else None
        if self.pid and is_partner_using_marketplace_for_ld(self.pid) and is_country_usa(country):
            return True
        return False

    @cached_property
    def account_group_id(self):
        """ Returns the account_group_id from the request """
        return self.request.account_group_id

    @cached_property
    def account_group(self):
        """ Returns the associated account group from the request """
        if self.account_group_id:
            return AccountGroupModel.build_key(self.account_group_id).get()
        return None

    @property
    def vbc_host(self):
        """
        The VBC host for this account.
        For example, https://vendasta-training.pdqs.mobi for a VUNI account.
        """
        try:
            env = env_from_app_engine_env()
            domain_api = DomainAPI(env)
            domain_result = domain_api.get_domain_by_identifier(
                f'/application/VBC/partner/{self.pid}'
            )
            vbc_host_string = domain_result.primary.domain
        except Exception as e:
            error_message = f'Failed to get VBC host from domain microservice: {e.message}'
            logging.error(error_message)
            vbc_host_string = None

        if vbc_host_string:
            statsd.tick_metric(statsd.StatsDMetric('vbc_domain_lookup', tags=['result:success']))
        else:
            statsd.tick_metric(statsd.StatsDMetric('vbc_domain_lookup', tags=['result:error']))
        return vbc_host_string

    def inflate_admin_context(self, context):
        """ Adds standard info to context """

        if 'session' not in context:
            context['session'] = self.session
        context['flashes'] = self.get_flashes()
        context['pid'] = self.pid
        context['partner'] = self.partner
        context['partner_name'] = self.partner.name
        context['crumbtrail'] = self.crumbtrail
        context['pc_header_host_and_api_path'] = urllib.parse.urljoin(settings.PARTNER_CENTRAL_HOST,
                                                                  '/api/universal-header/?selected_tab=ms')

        context['logo_url'] = self.logo_url
        context['favicon_url'] = self.favicon_url
        context['shortcut_url'] = self.shortcut_url

        context['uses_legacy_sso'] = vauth_configuration.does_partner_use_legacy_sso(get_vbc_pid(self.pid))

        # pass the root of the request path for determining currently selected tab
        context['request_path'] = self.request.path
        if self.request.path.count('/') > 4:
            context['request_path'] = self.request.path[:self.request.path.rstrip('/').rindex('/')]

        market_id = None
        if hasattr(self, 'microsite'):
            context['msid'] = self.microsite.msid
            context['microsite'] = self.microsite
            market_id = self.microsite.market_id
        market_id = market_id or getattr(self, 'market_id', None)

        # Pass Whitelabel data into templates
        context['back_url'] = self.back_url
        context['back_url_text'] = self.back_url_text

        if hasattr(self, 'wl_primary_color') and self.wl_primary_color:
            context['wl_primary_color'] = self.wl_primary_color
        if hasattr(self, 'wl_product_name') and self.wl_product_name:
            context['wl_product_name'] = self.wl_product_name
        if hasattr(self,
                   'wl_exit_link_url') and self.wl_exit_link_url and not \
                vauth.foundational_products.is_impersonation_session():
            context['back_url'] = self.wl_exit_link_url
        if hasattr(self, 'wl_exit_link_text') and self.wl_exit_link_text:
            context['back_url_text'] = self.wl_exit_link_text

        context['vff_host'] = settings.VENDASTA_FRONTEND_HOST

        # Used to determine whether or not listing distribution is enabled/disabled
        context['enable_listing_distribution'] = self.is_listing_distribution_enabled

        feature_client = vconfig.FeatureClient(environment=settings.ENVIRONMENT_NAME)

        context['show_listing_expiry'] = self.wl_ld_show_expiry
        context['enable_location_page'] = self.wl_enable_location_page
        context['enable_citations_page'] = self.wl_enable_citations_page
        context['enable_overview_page'] = self.wl_enable_overview_page
        context['is_disrupt_account'] = feature_client.is_feature_enabled(
            'activate_editions_disrupt_2019', self.pid, market_id=market_id
        )
        context['vbc_host'] = self.vbc_host
        context['link_to_view_my_listing'] = self._get_link_to_view_my_listing()

        if settings.IS_DEV_APPSERVER and settings.ENABLE_ALL_ADMIN_PAGES:
            context['enable_listing_distribution'] = True

            context['show_listing_expiry'] = True
            context['enable_location_page'] = True
            context['enable_citations_page'] = True
            context['enable_overview_page'] = True

        return context

    def _get_link_to_view_my_listing(self):
        """ Get the URL to view the My Listing page, instead of edit it. """
        slug_mappings = lookup_hostslug_msid_mapping_for_msid(self.pid, self.microsite.msid)
        for mapping in slug_mappings:
            if not mapping.redirect_hostslug:
                return f'https://{mapping.hostslug}/'

    def render_response(self, template, **context):
        """ Augments the context. """
        context = self.inflate_admin_context(context)
        if 'i18n' not in context:
            context['i18n'] = self.get_translations(self.request, session=self.session, partner_id=self.pid)

        if self.request.headers.get('x-app-v2'):
            context['microsite'] = context.get('microsite').get_common_data()
            context['partner'] = context.get('partner').to_dict()
            account_group = AccountGroupModel.get(self.microsite.agid)
            context['serviceAreaBusiness'] = True if account_group.service_area_business_flag else False
            self.response.content_type = 'application/json'
            if self.microsite and self.microsite.agid and self.microsite.agid == "AG-RQBC54K5FD":
                logging.info("LB-????: %s: %s", self.microsite.agid, context)
            context = sanitize_jinja_context(context)
            if self.microsite and self.microsite.agid and self.microsite.agid == "AG-RQBC54K5FD":
                logging.info("LB-????: %s: %s", self.microsite.agid, context)
            return self.response.write(json.dumps(jsonify.dumps(context)))

        return super().render_response(template, **context)


class AuthenticatedEditSiteView(AuthenticatedAdminView, BlobstoreUploadMixin):
    """AuthenticatedEditSiteView
    This view is used as base view for site editing screens that are authenticated through VAuth."""
    middleware = AUTHENTICATE_EDIT_SITE_MIDDLEWARE
