"""
Handler for update-account-group messages from vPubSub
"""
import logging

from google.appengine.ext import ndb
from google.appengine.ext.deferred import deferred, taskqueue, SingularTaskFailure, PermanentTaskFailure
from vpubsub import VMessageHandler
from vpubsub.messages import UpdateBusinessProfileMessage

from app.constants import DEFERRED_ROOT
from app.domain.microsite import delete_microsite
from app.models.microsite import Microsite, AccountGroupModel, MicrositeTombstone
from vax.errors import TransportException
from listing_products_sdk.api import ListingProductsClient


def call_update_listing_profile(env, account_group_id, grh):
    """
    Wrapper for calling LP client for testing
    """
    try:
        ListingProductsClient.set_environment(env)
        ListingProductsClient.update_listing_profile(account_group_id, [grh])
    except TransportException as e:
        if e.status_code == 404:
            return True
        elif e.status_code == 429:
            return True
        else:
            logging.error('Error while creating account.', exc_info=True)
    return False


class UpdateBusinessProfileHandler(VMessageHandler):
    """
    Handler for update-business-profile notifications from vPubSub
    """
    MESSAGE_CLASS = UpdateBusinessProfileMessage

    def process(self, message, labels=None):
        """ process """
        defer_url = DEFERRED_ROOT + 'MicroSite/handle_business_profile_update/'
        defer_name = 'account-update-{}-{}'.format(
            message.account_group_id,
            message.version
        )

        try:
            logging.info('deferring account update for agid: %s', message.account_group_id)
            deferred.defer(_handle_business_profile_update, message,
                           _url=defer_url,
                           _name=defer_name,
                           _queue='account-update')
        except (taskqueue.TombstonedTaskError, taskqueue.TaskAlreadyExistsError):
            logging.info('Already enqueued the task %s', defer_name)


def _handle_business_profile_update(message):
    """handles deferred pubsub updates to business profile (account groups)"""
    _update_account_group(message)

    def tx():
        """ instantiate and save the MS account """
        entity = AccountGroupModel(message)
        microsite = _get_account_and_check_account_group(entity)
        if microsite and microsite.account_group.version < entity.version:
            if entity.deleted_flag:
                logging.info('Deleting microsite with AGID %s', entity.account_group_id)
                delete_microsite(microsite.msid, pid=entity.partner_id)
            else:
                logging.info('Updating microsite entity from update-business-profile notification.')
                microsite.account_group = entity
                microsite.put()

        return

    return ndb.transaction(tx, xg=True)


def _update_account_group(account_group_message):
    """ update the account group model in MS """

    def tx():
        """ instantiate and save the account group """
        existing_account_group = AccountGroupModel.build_key(account_group_message.account_group_id).get()
        if existing_account_group and existing_account_group.version >= account_group_message.version:
            # logging.info('got a stale/duplicate account group pubsub. Received version %s but already have %s',
            #              account_group_message.version, existing_account_group.version)
            raise PermanentTaskFailure

        entity = AccountGroupModel(account_group_message)
        entity.apply_changes()

        return entity

    return ndb.transaction(tx)


def _get_account_and_check_account_group(account_group):
    """
    Get account with the account group from pubsub message,
    If the account group from pubsub message has msid,
        if we can find the corresponding Microsite entity, we will return the Microsite entity;
        Otherwise, we will check the AccountGroupModel in MS,
            if the AccountGroupModel in MS is newer than the AG from pubsub, PermanentTaskFailure will be raised;
            otherwise, SingularTaskFailure will be raised and the task will be retried later.
    If the account group from pubsub message does not have msid,
        return None and succeed since the AG has no MS model
    """
    agid = account_group.account_group_id
    if not account_group.msid:
        return None

    account = Microsite.get_by_msid(account_group.msid, account_group.partner_id)

    if account:
        return account

    tombstone_account = MicrositeTombstone.build_key(account_group.msid, account_group.partner_id).get()
    existing_account_group = AccountGroupModel.build_key(agid).get()

    if not tombstone_account and existing_account_group and existing_account_group.version <= account_group.version:
        logging.warn('No MS account found for agid %s, but the account group in pubsub has greater version %s.'
                     'Task will be retried.', agid, account_group.version)
        raise SingularTaskFailure
    logging.warn('Got stale account group for agid %s. Received version %s but already have %s. '
                 'Task will not be retried.', agid, account_group.version, existing_account_group.version)

    raise PermanentTaskFailure
