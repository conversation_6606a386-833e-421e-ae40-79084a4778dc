""" Pub sub handler for importing external reviews into My Listings"""
import json
import logging

from google.appengine.api.taskqueue import TombstonedTaskError, TaskAlreadyExistsError
from google.appengine.ext.deferred import deferred
from webapp2 import <PERSON><PERSON><PERSON><PERSON><PERSON>

from app.domain.constants import Keys
from app.domain.microsite.review import create_core_review_for_account
from app.models.microsite import Microsite


class MyListingsReviewImportPubSubHandler(RequestHandler):
    """
    Handle pubsub to import a review into My Listings
    """

    def post(self):
        """ POST """
        message_body = json.loads(self.request.body)
        message_attributes = message_body.get('message', {}).get('attributes', {})
        partner_id = message_attributes.get('PartnerId')
        account_group_id = message_attributes.get('AccountGroupId')
        if not partner_id:
            logging.error('missing partner id')
            return
        if not account_group_id:
            logging.error('missing account group id')
            return

        try:
            microsite = Microsite.lookup_by_agid(account_group_id, partner_id)
        except Exception as e:
            logging.error('failed to find microsite for account group %s with error: %s', account_group_id, e)
            return

        if not microsite:
            logging.error('no microsite found for account group %s', account_group_id)
            return

        try:
            deferred.defer(create_core_review_for_account,
                           microsite.msid,
                           partner_id,
                           message_attributes.get('OriginalStars'),
                           message_attributes.get('ReviewerName'),
                           message_attributes.get('ReviewerEmail'),
                           title=message_attributes.get('Title'),
                           content=message_attributes.get('Content'),
                           send_follow_up=False,
                           _queue='default',
                           _url=Keys.DEFER_URL_ROOT + 'microsite/review-import',
                           _name='MY-LIS-IMPORT-{}'.format(message_attributes.get('UniqueId')))
        except (TombstonedTaskError, TaskAlreadyExistsError):
            logging.info('task already exists %s', message_attributes.get('UniqueId'))
