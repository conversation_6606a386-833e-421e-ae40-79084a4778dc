""" Handlers for custom error pages. 

Configure a new error handler in main.py.
"""

from calendar import timegm
from functools import wraps
import sys
import logging
import traceback
from app.models.partner import Partner
from webapp2 import cached_property
from app.domain.microsite import get_microsite
from app.domain.url_mappings import get_host_pid_mapping_for_request, get_hostslug_msid_mapping_for_request
from app.views import BaseHandler
from settings import DEBUG


def process_request(func):
    """ Processes the request of the given function. """
    @wraps(func)
    def inner_decorator(handler, request, response, exception):
        """ Attempts to retrieve, and add to the request, as much microsite information as is available. """
        handler.slug = None
        handler.msid = None
        handler.pid = None
        hpm = get_host_pid_mapping_for_request(request)
        if hpm:
            handler.pid = hpm.pid
            try:
                hsmm = get_hostslug_msid_mapping_for_request(request, uses_slugs=hpm.uses_slugs)
                if hsmm:
                    handler.slug = hsmm.slug
                    handler.msid = hsmm.msid
            except ValueError:
                handler.ERROR_CODE = 404

        return func(handler, request, response, exception)
    return inner_decorator

class ExceptionHandler(BaseHandler):
    """ Generic exception handler. """

    @cached_property
    def partner(self):
        """ Returns the partner if it can be retrieved. """
        if not self.pid:
            return None

        return Partner.get(self.pid)

    @cached_property
    def microsite(self):
        """ Returns the Microsite object corresponding to self.msid. """
        if not self.msid or not self.pid:
            return None
        
        return get_microsite(self.msid, pid=self.pid)

    @cached_property
    def home_page_url(self):
        """ Returns the relative url for the home page. """
        if self.slug:
            return self.uri_for('ms-general-hybrid', hybridslug=self.slug)
        else:
            return '/' # TODO: can probably use url_for when we have full custom domain stuff working

    @cached_property
    def css_url(self):
        """ Returns the url for the Microsite css. """
        if not self.microsite or not self.slug:
            return None

        version = str(timegm(self.microsite.updated.utctimetuple()))
        if self.slug:
            return self.uri_for('ms-css', slug=self.slug, version=version)
        else:
            return self.uri_for('ms-css', version=version)

    @process_request
    def __call__(self, request, response, exception):
        """ Renders the appropriate template and logs the exception, if appropriate. """

        context = {
            'css_url':          self.css_url,
            'debug':            DEBUG,
            'home_page_url':    self.home_page_url,
            'google_analytics_id':    self.partner and self.partner.google_analytics_id,
            'microsite':        self.microsite,
            'msid':             self.msid,
            'pid':              self.pid,
            'platform':         'microsites/html/platforms/desktop.html',
            'theme':            'microsites/html/themes/desktop_theme1.html',
            'title':            self.microsite and self.microsite.name
        }

        if exception:
            logging.exception(exception)
            limit = None
            typ, value, tb = sys.exc_info()
            context['exception'] = exception
            context['exception_message'] = ' '.join(traceback.format_exception_only(typ, value))
            context['exception_details'] = ' '.join(traceback.format_tb(tb, limit))

        self.response = response
        self.response.set_status(self.ERROR_CODE)
        response = self.render_response('html/error_handlers/%d.html' % self.ERROR_CODE, **context)
        return response

class Handle404(ExceptionHandler):
    """ Handles 404 exceptions. """
    ERROR_CODE = 404

class Handle500(ExceptionHandler):
    """ Handles 500 exceptions. """
    ERROR_CODE = 500
