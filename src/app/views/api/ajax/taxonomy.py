""" used to retrieve taxonomy data """
import settings
from app.views.api.ajax import BaseAjaxHandler
from coresdk import vtax
from vform.helpers import flatten_taxonomy


class ListTaxonomiesHandler(BaseAjaxHandler):
    """
    List taxonomies
    """
    VERSION = '1.0'
    URL = '/ajax/v1/taxonomies/'
    REQUIRES_HTTPS = False
    ALLOWED_METHODS = frozenset(['GET'])
    DOC_NAME = 'List Taxonomies'

    def process(self, args):
        mgr = vtax.VTaxManager(settings.VSOCIAL_USER, settings.VSOCIAL_API, target=settings.VSOCIAL_TARGET)
        return flatten_taxonomy(mgr.getFullTaxonomy())
