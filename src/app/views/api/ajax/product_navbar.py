""" Product Navbar """

import json
import logging

import vapi
import vauth.foundational_products

from app.vendor_center import get_product_navbar_data
from app.vendor_center.product_navbar import ProductNavbarGetFailedException
from app.views.api.ajax import BaseAjaxHandler


class GetProductNavbarDataAjaxHandler(BaseAjaxHandler):
    """ Authenticates through developer central to retrieve the product navbar data """

    REQUIRES_HTTPS = False
    ALLOWED_METHODS = frozenset(['GET'])

    account_group_id = vapi.StringProperty(required=True, name="accountId")
    app_id = vapi.StringProperty(required=True)

    def process(self, args):
        """ process """
        user_id = vauth.foundational_products.get_user_id()

        try:
            response = get_product_navbar_data(self.account_group_id, self.app_id, user_id)
        except ProductNavbarGetFailedException as pngfe:
            logging.error('Failed to GET Developer Central product_navbar_data, error: %s', pngfe.message)
            return
        response_json_dict = json.loads(response.content)
        return response_json_dict.get('data')
