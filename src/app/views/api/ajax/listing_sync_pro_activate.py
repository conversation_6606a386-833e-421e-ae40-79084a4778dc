""" Handler for making account hot after user shows interest in listing sync pro"""

from app.models.microsite import AccountGroupModel
from app.domain.interest_in_listing_sync_pro import user_interested_in_listing_sync_pro
import vapi


class InterestInListingSyncProHandler(vapi.NoAuthAjaxHandler):
    """ Handler for making account hot an account hot """

    VERSION = '1.1'
    URL = '/_ajax/v1/edit/listing-sync/list-sync-activate/'
    REQUIRES_HTTPS = False
    ALLOWED_METHODS = frozenset(['POST', 'GET'])
    DOC_NAME = 'Interest in Listing Sync Pro'

    account_group_id = vapi.StringProperty(required=True, name='account_group_id')
    current_user_id = vapi.StringProperty(name='current_user_id')
    message = vapi.StringProperty(required=True, name='message')

    def process(self, args):
        """ process """

        account_group = AccountGroupModel.build_key(account_group_id=self.account_group_id).get()
        user_interested_in_listing_sync_pro(account_group.partner_id, account_group.market_id,
                                            self.account_group_id, self.message, self.current_user_id)

        return {'success': True}
