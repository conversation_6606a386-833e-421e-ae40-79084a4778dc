"""
VBC Executive Report gathering Listing stats and sending to exec report
"""
from datetime import timedelta

from google.appengine.api.taskqueue import TaskRetryOptions
from google.appengine.ext.deferred import defer, logging

from app.domain.constants import Keys
from app.domain.google_my_business import get_gmb_insights
from app.vendor_center import send_report_data
from app.execreporthelpers import generate_report_data, build_executive_report_payload, ReportFrequencies, \
    get_most_recent_report_date


# Mapping of functions to collect executive report data * note: will be displayed in order
_REPORT_STAT_FUNCTIONS = [
    get_gmb_insights
]

def _generate_and_send_executive_report(report_stat_functions, account_group_id, social_profile_id, company_name,
                                        report_frequency, report_date):
    """
    Build and send payload for Vendor Centre request
    """
    # list of report stat functions needs to go as first parameter to generate_report_data
    report_data = generate_report_data(
        report_stat_functions,
        account_group_id,
        social_profile_id,
        company_name,
        report_frequency,
        report_date
    )
    if not report_data:
        return None

    payload = build_executive_report_payload(report_date, account_group_id, report_data, 'LISTINGS')
    logging.info("generate_and_send_executive_report payload for %s %s %s: %s",
                 account_group_id, report_frequency, report_date, payload)

    send_report_data(payload, report_frequency)


def generate_and_send_executive_report(account_group_id, social_profile_id, company_name,
                                       report_frequency, report_date):
    """
    Generate the executive report data, and send the data to the executive report. Called from scheduler
    on Mondays (weekly) or 1st (monthly), so need to get the most recent previous report_date.
    """
    if report_frequency not in ReportFrequencies.all():
        raise ValueError('report_frequency must be one of %s', ReportFrequencies.all())
    if not report_date:
        report_date = get_most_recent_report_date(report_frequency)

    retry_options = TaskRetryOptions(task_age_limit=timedelta(days=1).total_seconds())

    defer(
        _generate_and_send_executive_report,
        _REPORT_STAT_FUNCTIONS,
        account_group_id,
        social_profile_id,
        company_name,
        report_frequency,
        report_date,
        _queue='submit-exec-report-data',
        _url=Keys.DEFER_URL_ROOT + 'executive_report/push_executive_report',
        _retry_options=retry_options,
    )

