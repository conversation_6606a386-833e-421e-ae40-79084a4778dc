"""
module for pipeline
"""
from app.migration.pipelines import BasicPipeline
from app.models.microsite import Microsite, MicrositeTombstone
from app.models.partner import Partner
from mapreduce import operation
from mapreduce.mapper_pipeline import MapperPipeline


def put_entity(entity):
    """ Puts an entity. """
    yield operation.db.Put(entity)


class PutAllMicrositesPipeline(BasicPipeline):
    """ Pipeline to put all microsites for all partners. """

    def run(self, **kwargs):
        """ Run the pipeline """
        partner_keys = Partner.all_partners(keys_only=True)
        for partner_key in partner_keys:
            pid = partner_key.string_id()

            yield MapperPipeline("put microsites",
                                 put_entity.__module__ + '.' + put_entity.__name__,
                                 "mapreduce.input_readers.DatastoreInputReader",
                                 params={
                                     "entity_kind": Microsite.__module__ + '.' + Microsite.__name__,
                                     "namespace": pid
                                 },
                                 shards=32
                                 )

            yield MapperPipeline("put microsite tombstones",
                                 put_entity.__module__ + '.' + put_entity.__name__,
                                 "mapreduce.input_readers.DatastoreInputReader",
                                 params={
                                     "entity_kind": MicrositeTombstone.__module__ + '.' + MicrositeTombstone.__name__,
                                     "namespace": pid
                                 },
                                 shards=32
                                 )
