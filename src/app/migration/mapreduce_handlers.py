""" Handlers for mapreduce jobs. """
from mapreduce import context, operation as op
from app.models.url_mappings import HostPidMapping, HostSlugMsidMapping


def migrate_HostPidMapping_hostname(entity):
    """ Migrates the hostnames on HostPidMapping. """
    
    params = context.get().mapreduce_spec.mapper.params
    from_hostname = params['from_hostname'].strip().lower()
    to_hostname = params['to_hostname'].strip().lower()
    
    if entity.host == from_hostname:
        
        prop_dict = entity.to_dict()
        prop_dict['host'] = to_hostname
        prop_dict['key'] = HostPidMapping.build_key(to_hostname)
        
        new_entity = HostPidMapping(**prop_dict)
        
        yield op.db.Put(new_entity)
        yield op.db.Delete(entity)
        yield op.counters.Increment('Entities migrated')

def migrate_HostSlugMsidMapping_hostname(entity):
    """ Migrates the hostnames on HostSlugMsidMapping. """
    
    params = context.get().mapreduce_spec.mapper.params
    from_hostname = params['from_hostname'].strip().lower()
    to_hostname = params['to_hostname'].strip().lower()
    
    if entity.hostslug == from_hostname or entity.hostslug.startswith(from_hostname + '/'):
        
        new_hostslug = entity.hostslug.replace(from_hostname, to_hostname, 1)
        slug = entity.hostslug[len(from_hostname) + 1:]
        prop_dict = entity.to_dict()
        prop_dict['hostslug'] = new_hostslug
        prop_dict['key'] = HostSlugMsidMapping.build_key(to_hostname, slug=slug)
        
        new_entity = HostSlugMsidMapping(**prop_dict)
        
        yield op.db.Put(new_entity)
        yield op.db.Delete(entity)
        yield op.counters.Increment('Entities migrated')


def migrate_vendastaFooter(partner):
    """ Migrates the partner's show_vendasta_footer to show_partner_footer """
    params = context.get().mapreduce_spec.mapper.params
    commit = params['commit']

    yield op.counters.Increment('Partners migrated')
    partner.show_partner_footer = getattr(partner, 'show_vendasta_footer', None)
    if partner.show_partner_footer:
        yield op.counters.Increment('show_vendasta_footer extracted')
        delattr(partner, 'show_vendasta_footer')
    if commit.lower() == 'true':
        yield op.db.Put(partner)
