state_machines:
  
  - name: AddPageIdToPage
  
    namespace: app.migration
    
    context_types:
      partner_key: google.appengine.ext.ndb.Key
      page_key: google.appengine.ext.ndb.Key
  
    states:
    
    - name: IteratePartners
      action: app.domain.fsm_util.IteratePartners
      initial: True
      final: True
      continuation: True
      transitions:
        - event: ok
          to: IteratePages
          
    - name: IteratePages
      action: app.domain.fsm_util.IteratePages
      continuation: True
      final: True
      transitions:
        - event: ok
          to: AddPageId
        
    - name: AddPageId
      action: AddPageId
      final: True


  - name: CreateSpidForSites
    namespace: app.migration
    queue: migration
    context_types:
      partner_key: google.appengine.ext.ndb.Key
      site_key: google.appengine.ext.ndb.Key

    states:
        - name: IteratePartners
          action: app.domain.fsm_util.IteratePartners
          initial: True
          final: True
          continuation: True
          transitions:
            - event: ok
              to: IterateSites

        - name: IterateSites
          action: app.domain.fsm_util.IterateSites
          continuation: True
          final: True
          transitions:
          - event: ok
            to: AddSpidToSite

        - name: AddSpidToSite
          action: AddSpidToSite
          final: True
