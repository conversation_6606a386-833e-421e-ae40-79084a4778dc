"""
module for pipeline
"""
import logging
import pipeline


# W0232: Class has no __init__ method
# E1002: Use of super on an old style class
# W0613: Unused argument 'kwargs'
# pylint-disable: W0232, E1002, W0613

class BasicPipeline(pipeline.Pipeline):
    """
    basic pipeline to give basic implementation of run_test, callback, finalized_test
    """
    def run(self, *args, **kwargs):
        """run"""
        super(BasicPipeline, self).run(*args, **kwargs)

    def callback(self, **kwargs):
        """override abstract method"""
        if self.async:
            raise NotImplementedError("async pipeline needs to implement callback method")

    def run_test(self, *args, **kwargs):
        """run_test"""
        return self.run(*args, **kwargs)

    def finalized_test(self,  *args, **kwargs):
        """finalized_test"""
        logging.info("finalized test with args %s and kwargs %s", args, kwargs)

    def start_test(self, *args, **kwargs):
        """start_test"""
        if 'base_path' not in kwargs:
            kwargs['base_path'] = '/_ah/pipeline'
        super(BasicPipeline, self).start_test(*args, **kwargs)
