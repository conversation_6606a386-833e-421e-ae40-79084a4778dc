"""
module for pipeline
"""
from google.appengine.ext import deferred
from app.constants import DOCUMENTS_QUEUE
from app.domain.constants import Keys
from app.domain.search_documents import add_microsite_search_document
from app.migration.pipelines import BasicPipeline
from app.models.microsite import Microsite
from app.models.partner import Partner
from mapreduce.mapper_pipeline import Map<PERSON><PERSON><PERSON>eline


def defer_add_search_doc(entity):
    """
    Add the search doc for a microsite.
    """
    defer_url = Keys.DEFER_URL_ROOT + 'MicroSite/AddSearchDocument/'
    deferred.defer(add_microsite_search_document, entity.msid, entity.pid, _url=defer_url, _queue=DOCUMENTS_QUEUE)


class ReindexSearchPipeline(BasicPipeline):
    """ Pipeline to reindex search docs for all partners. """

    def run(self, **kwargs):
        """ Run the pipeline """
        partner_keys = Partner.all_partners(keys_only=True)
        for partner_key in partner_keys:
            pid = partner_key.string_id()

            yield MapperPipeline("reindex microsites",
                                 defer_add_search_doc.__module__ + '.' + defer_add_search_doc.__name__,
                                 "mapreduce.input_readers.DatastoreInputReader",
                                 params={
                                     "entity_kind": Microsite.__module__ + '.' + Microsite.__name__,
                                     "namespace": pid
                                 },
                                 shards=32
                                 )
