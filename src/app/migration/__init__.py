""" Temporary code for data migration. """

import logging
from fantasm.action import FSMAction
from google.appengine.ext import ndb
from app.domain.social import SitesVSocial

UTF8_BOM = "\xEF\xBB\xBF"

# W0613:  8:AddPageId.execute: Unused argument 'obj'
# Fantasm interface
# pylint: disable=W0613

class AddPageId:
    """ Adds the pageid attribute to a Page entity. """

    def execute(self, context, obj):
        """ Adds a pageid attribute to the given page_key. """
        page_key = context['page_key']
        entity = page_key.get()
        if entity:
            entity.pageid = page_key.string_id()
            entity.put()


class AddSpidToSite(FSMAction):
    """ Create a social profile and adds a spid to given site """

    def execute(self, context, obj):
        """ create social profile and add spid to given site """
        partner_key = context.get('partner_key')
        if not partner_key:
            raise ValueError('partner_key is required')
        site_key = context.get('site_key')
        if not site_key:
            raise ValueError('site_key is required')

        partner, site = ndb.get_multi([partner_key, site_key])

        if not partner:
            raise ValueError('No matching partner(%s) found.' % partner_key)
        if not site:
            raise ValueError('No matching site(%s) found.' % site_key)

        if site.spid:
            logging.info("Site(%s) already has spid(%s), do nothing and move on.", site.msid, site.spid)
        else:
            vs = SitesVSocial(site.msid)
            spid = vs.create_social_profile(partner.spgid)
            site.account_group.social_profile_id = spid
            site.put()
            logging.info("Site(%s) is updated with spid(%s)", site.msid, spid)
