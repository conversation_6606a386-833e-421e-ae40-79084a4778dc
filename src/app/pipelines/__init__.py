""" Pipelines """


class PipelineStarter:
    """Wrapper to start a pipeline"""

    @classmethod
    def start_async(cls, func, *args, **kwargs):
        """start the given pipeline"""
        backoff_seconds = kwargs.pop('_backoff_seconds', None)
        max_attempts = kwargs.pop('_max_attempts', None)
        pipeline = func(*args, **kwargs)
        if backoff_seconds:
            pipeline.backoff_seconds = backoff_seconds
        if max_attempts:
            pipeline.max_attempts = max_attempts
        pipeline.start()
