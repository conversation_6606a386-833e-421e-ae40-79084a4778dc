"""Pipelines for sites"""
import json
import logging
import os

from google.appengine.api import images
from google.appengine.api.mail_errors import InvalidSenderError
from google.appengine.ext import ndb
from google.appengine.api import mail

from mapreduce import context, operation
from mapreduce.mapreduce_pipeline import MapperPipeline

from vpipeline import CommonPipeline
from vautil.cloudstorage import ensure_is_gcs_filename
import pipeline
from pipeline.common import Log

import settings
from app.constants import LOGO
from app.domain.microsite import delete_microsite
from app.domain.batch_import.util import UnifiedProvisionNotifier
from app.domain.batch_import_helpers.data_structures import MicrositeBatchPage
from app.domain.blob_mappings import add_image_to_microsite_from_url
from app.domain.exceptions import InvalidImageUrlException
from app.domain.social import SitesVSocial
from app.domain.vbc import VbcAccountGroup
from app.domain.microsite import Microsite
from app.domain.workflow.page import process_page_from_api
from app.models.microsite import Microsite as MicrositeModel

# All pipeline run methods will have a differing number of args, pylint:disable=W0221
from vpipeline.input_readers import CsvFileInputReader

EMAIL_TEMPLATE = """
Pipeline id: %s
Pipeline status: http://%s/mapreduce/pipeline/status?root=%s
"""

ACCOUNT_ENTITY_KIND = 'app.models.microsite.Microsite'


class CreateSitePipeline(CommonPipeline):
    """Workflow to ensure:
        - an account group has been created
        - the site has been registered on the corresponding social profile
        - the site has been added to the account group
        - the sites pages have been created
        - the logo has been added to a site
    """

    def run(self, site_key, job_id=None, notify_url=None, page_data=None, logo_url=None, skip_vbc=False):
        """
        :param site_key: The url_safe ndb entity key of the microsite
        :param job_id: Optional unified provisioning api argument for the create job
        :param notify_url: Optional unified provisioning api argument for the callback url
        :param page_data: Optional microsite page data
        :param logo_url: Optional logo_url of the microsite
        :param skip_vbc: Optional skip AddAccountToAccountGroup if pipeline is started by purchase webhook
        """
        if job_id:
            yield Log.info("jobId: '%s'", job_id)
        with pipeline.InOrder():
            yield RegisterForSocialProfile(site_key, job_id)
            if not skip_vbc:
                yield AddAccountToAccountGroup(site_key, job_id)
            if page_data:
                yield CreateSitePages(site_key, job_id, page_data)
            if logo_url:
                yield AddLogoToSite(site_key, job_id, logo_url)
            yield RegisterAsSocialService(site_key, job_id)
            yield FinalizeAccountCreation(site_key, job_id, notify_url)

    def finalized(self):
        """
        send an email if the pipeline failed to be completed
        """
        if self.was_aborted:
            subject = "Error creating microsite account"
            body = EMAIL_TEMPLATE % (self.root_pipeline_id, os.environ.get('SERVER_NAME'), self.root_pipeline_id)
            try:
                mail.send_mail(settings.SENDER_EMAIL, settings.MS_DEVOPS_EMAIL, subject, body)
            except InvalidSenderError:
                logging.error("Error creating microsite account, pipeline aborted email failed to send.")
                logging.info("%s", body)


class RegisterForSocialProfile(CommonPipeline):
    """Register the account on the social profile"""

    def run(self, site_key, job_id=None):
        """see above"""
        if job_id:
            yield Log.info("jobId: '%s'", job_id)
        site = ndb.Key(urlsafe=site_key).get()
        if site:
            social = SitesVSocial(site.msid, pid=site.pid)
            social.register_social_profile()


class AddAccountToAccountGroup(CommonPipeline):
    """Register the account on the account group"""

    def run(self, site_key, job_id=None):
        """see above"""
        if job_id:
            yield Log.info("jobId: '%s'", job_id)
        site = ndb.Key(urlsafe=site_key).get()
        if site:
            VbcAccountGroup.add_account_to_account_group(site.pid, site.msid, site.agid)


class CreateSitePages(CommonPipeline):
    """Create the pages specified on the create request"""

    def run(self, site_key, job_id=None, page_data=None):
        """see above"""
        if job_id:
            yield Log.info("jobId: '%s'", job_id)
        site = ndb.Key(urlsafe=site_key).get()
        if site:
            CreateSitePages.create_pages(site, page_data)

    @classmethod
    def create_pages(cls, site, page_data):
        """
        attempt to create the pages
        """
        for raw_page in page_data:
            try:
                if isinstance(raw_page, str):
                    raw_page = json.loads(raw_page)
                page = MicrositeBatchPage.from_dict(raw_page)
                process_page_from_api(page, Microsite.from_model(site))
            except Exception:
                e_msg = 'Error processing page for site "%s" and partner "%s". ' \
                        'Page will be skipped.' % (site.msid, site.pid)
                logging.exception(e_msg)


class AddLogoToSite(CommonPipeline):
    """Add the image logo to the site"""

    def run(self, site_key, job_id=None, logo_url=None):
        """see above"""
        if job_id:
            yield Log.info("jobId: '%s'", job_id)
        site = ndb.Key(urlsafe=site_key).get()
        if site:
            try:
                add_image_to_microsite_from_url(site.msid, site.pid, LOGO, logo_url)
            except (images.Error, InvalidImageUrlException):
                e_msg = 'Error adding logo "%s" to site "%s" for partner "%s". ' \
                        'Logo will not be added to site.' % (logo_url, site.msid, site.pid)
                logging.exception(e_msg)


class RegisterAsSocialService(CommonPipeline):
    """Register the account as a social service in core"""

    def run(self, site_key, job_id=None):
        """see above"""
        if job_id:
            yield Log.info("jobId: '%s'", job_id)
        site = ndb.Key(urlsafe=site_key).get()
        if site:
            social = SitesVSocial(site.msid, pid=site.pid)
            social.register_site_as_social_service()


class FinalizeAccountCreation(CommonPipeline):
    """Post to the unified provisioning notification url"""

    def run(self, site_key, job_id=None, notify_url=None):
        """see above"""
        if job_id:
            yield Log.info("jobId: '%s'", job_id)
            if notify_url:
                site = ndb.Key(urlsafe=site_key).get()
                if site:
                    UnifiedProvisionNotifier.post_to_notification_url(job_id, notify_url, site.msid)
        yield Log.info('Account creation completed for %s', site_key)


class BulkDeletePartnerNamespaceMicrositesPipeline(CommonPipeline):
    """
    Pipeline to bulk delete microsite provided a Cloud storage CSV for a given partner namespace
    """
    def run(self, gcs_filename, partner_id, shards=8, commit=False):
        """
        start mapper
        """
        if not gcs_filename:
            raise ValueError("gcs_filename is required.")
        if not partner_id:
            raise ValueError("partner_id is required.")
        yield MapperPipeline(
            'Bulk microsite delete by CSV input',
            'app.pipelines.microsite.base.delete_microsite_by_id',
            'vpipeline.input_readers.CsvFileInputReader',
            params={
                'input_reader': {
                    CsvFileInputReader.HEADER_PARAM: ['msid', 'agid'],
                    CsvFileInputReader.FILES_PARAM: [ensure_is_gcs_filename(gcs_filename)],
                    CsvFileInputReader.OUTPUT_SCHEMA_PARAM: CsvFileInputReader.DICT,
                },
                'commit': commit,
                'partner_id': partner_id
            },
            shards=shards
        )


@ndb.transactional
def delete_microsite_by_id(data):
    """ mapper handler to delete a microsite by ID. """
    params = context.get().mapreduce_spec.mapper.params
    commit = params['commit']
    pid = params['partner_id']

    msid = data['msid']
    agid = data['agid']

    microsite = MicrositeModel.get_by_msid(msid, pid)
    if microsite and microsite.agid == agid:
        if commit:
            delete_microsite(microsite.msid, pid=pid)
            yield operation.counters.Increment('Microsite deleted')
        else:
            yield operation.counters.Increment('Microsite to be deleted')
    else:
        yield operation.counters.Increment('Microsite not found')


class BulkDeleteMicrositesPipeline(CommonPipeline):
    """
    Pipeline to bulk delete microsite provided a Cloud storage CSV for given msid and pid
    """
    def run(self, gcs_filename, shards=8, commit=False):
        """
        start mapper
        """
        if not gcs_filename:
            raise ValueError("gcs_filename is required.")
        yield MapperPipeline(
            'Bulk microsite delete by CSV input',
            'app.pipelines.microsite.base.delete_microsite_by_id_and_pid',
            'vpipeline.input_readers.CsvFileInputReader',
            params={
                'input_reader': {
                    CsvFileInputReader.HEADER_PARAM: ['msid', 'pid'],
                    CsvFileInputReader.FILES_PARAM: [ensure_is_gcs_filename(gcs_filename)],
                    CsvFileInputReader.OUTPUT_SCHEMA_PARAM: CsvFileInputReader.DICT,
                },
                'commit': commit
            },
            shards=shards
        )


@ndb.transactional
def delete_microsite_by_id_and_pid(data):
    """ mapper handler to delete a microsite by msid and pid. """
    params = context.get().mapreduce_spec.mapper.params
    commit = params['commit']
    pid = data['pid']
    msid = data['msid']

    microsite = MicrositeModel.get_by_msid(msid, pid)
    if microsite:
        logging.info('deleteing microsite with msid: %s, pid: %s', msid, pid)
        if commit:
            delete_microsite(microsite.msid, pid=pid)
            yield operation.counters.Increment('Microsite deleted')
        else:
            yield operation.counters.Increment('Microsite to be deleted')
    else:
        yield operation.counters.Increment('Microsite not found')
