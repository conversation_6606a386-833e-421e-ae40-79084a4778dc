"""
Pipeline to restore a deleted partner and associated sites
"""
#pylint: skip-file

from google.appengine.datastore import entity_pb
from google.appengine.ext.ndb.model import ModelAdapter

from app.domain.social import SitesVSocial
from app.domain.vbc import VbcAccountGroup

from app.models.microsite import Microsite, Navigation
from app.models.partner import Partner
from app.models.url_mappings import HostSlugMsidMapping as HostSlugMsidMappingModel
from app.models.blob_mappings import MsidBlobMapping as MsidBlobMappingModel
from app.models.url_mappings import HostPidMapping as HostPidMapping
from app.models.page import Page

from mapreduce import context
from mapreduce import mapreduce_pipeline
from mapreduce import operation
from mapreduce.api.map_job import Mapper
from mapreduce.input_readers import GoogleCloudStorageInputReader
from mapreduce.pipeline_base import PipelineBase

from vrestore.utils import fixKeys

PARTNER_IDS = ["DW1"]


class RestorePartner(PipelineBase):
    """
    Restores a partner
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = 'backup.Partner(s~microsite-prod)/15709747964963018028073D94B310A'

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'restore partner',
            'app.pipelines.microsite.restore_partner_and_sites.RestorePartnerMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RestoreHostPidMappings(PipelineBase):
    """
    Restores a partner host pid mappings
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = 'backup.HostPidMapping(s~microsite-prod)/15709747974681343763281EA84ADA6'

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'restore partner host pid mappings',
            'app.pipelines.microsite.restore_partner_and_sites.RestoreHostPidMappingMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RestoreSites(PipelineBase):
    """
    Restores the sites
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = "backup.MicrositeModel(s~microsite-prod)/15709747969311695433204D04157F9"

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'restore microsite',
            'app.pipelines.microsite.restore_partner_and_sites.RestoreMicrositeMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RestoreMappings(PipelineBase):
    """
    Restores the site host mappings
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = "backup.HostSlugMsidMapping(s~microsite-prod)/1570974796597421202170031BF0944"

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'restore mappings',
            'app.pipelines.microsite.restore_partner_and_sites.RestoreMappingsMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RestoreNavigation(PipelineBase):
    """
    Restores the site navigation
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = "backup.Navigation(s~microsite-prod)/157097479584142553229069DF550C5"

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'restore navigation',
            'app.pipelines.microsite.restore_partner_and_sites.RestoreNavigationMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RestorePage(PipelineBase):
    """
    Restores the site page
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = "backup.Page(s~microsite-prod)/157097479611320122351810EEBE1BB"

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'restore page',
            'app.pipelines.microsite.restore_partner_and_sites.RestorePageMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RestoreBlobMappings(PipelineBase):
    """
    Restores the blob mappings
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = "backup.MsidBlobMapping(s~microsite-prod)/15709747971354029694265E742A44F"

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'restore blob mappings',
            'app.pipelines.microsite.restore_partner_and_sites.RestoreBlobMappingsMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RegisterSocialProfile(PipelineBase):
    """
    Register on the social profile
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = "backup.MicrositeModel(s~microsite-prod)/15709747969311695433204D04157F9"

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'register social profile',
            'app.pipelines.microsite.restore_partner_and_sites.RegisterSocialProfileMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RegisterAccountGroup(PipelineBase):
    """
    Register on the account group
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = "backup.MicrositeModel(s~microsite-prod)/15709747969311695433204D04157F9"

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'register account group',
            'app.pipelines.microsite.restore_partner_and_sites.RegisterAccountGroupMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RestorePartnerMapper(Mapper):
    """ Mapper to restore a partner """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        partner = ModelAdapter().pb_to_entity(pb)
        if partner.pid not in PARTNER_IDS:
            yield operation.counters.Increment("Skipping non DW1")
            return

        yield operation.counters.Increment("Saving partner")

        if commit:
            yield operation.db.Put(partner)


class RestoreHostPidMappingMapper(Mapper):
    """ Mapper to restore host pid mappings """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        pid_mapping = ModelAdapter().pb_to_entity(pb)
        if pid_mapping.pid not in PARTNER_IDS:
            yield operation.counters.Increment("Skipping non DW1")
            return

        yield operation.counters.Increment("Saving partner")

        if commit:
            yield operation.db.Put(pid_mapping)


class RestoreMicrositeMapper(Mapper):
    """ Mapper to restore sites for a partner """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        site = ModelAdapter().pb_to_entity(pb)
        if site.key.namespace() not in PARTNER_IDS:
            yield operation.counters.Increment("Skipping non DW1")
            return

        yield operation.counters.Increment("Saving site")

        if commit:
            yield operation.db.Put(site)


class RestoreMappingsMapper(Mapper):
    """ Mapper to restore sites for a partner """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        mapping = ModelAdapter().pb_to_entity(pb)
        if mapping.pid not in PARTNER_IDS:
            yield operation.counters.Increment("Skipping non DW1")
            return

        yield operation.counters.Increment("Saving mapping")

        if commit:
            yield operation.db.Put(mapping)


class RestoreNavigationMapper(Mapper):
    """ Mapper to restore sites navigation for a partner """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        navigation = ModelAdapter().pb_to_entity(pb)
        if navigation.key.namespace() not in PARTNER_IDS:
            yield operation.counters.Increment("Skipping non DW1")
            return

        yield operation.counters.Increment("Saving navigation")

        if commit:
            yield operation.db.Put(navigation)


class RestorePageMapper(Mapper):
    """ Mapper to restore sites navigation for a partner """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        navigation = ModelAdapter().pb_to_entity(pb)
        if navigation.key.namespace() not in PARTNER_IDS:
            yield operation.counters.Increment("Skipping non DW1")
            return

        yield operation.counters.Increment("Saving page")

        if commit:
            yield operation.db.Put(navigation)


class RestoreBlobMappingsMapper(Mapper):
    """ Mapper to restore sites for a partner """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        blob_mapping = ModelAdapter().pb_to_entity(pb)
        if blob_mapping.key.namespace() not in PARTNER_IDS:
            yield operation.counters.Increment("Skipping non DW1")
            return

        yield operation.counters.Increment("Saving blob mapping")

        if commit:
            yield operation.db.Put(blob_mapping)


class RegisterSocialProfileMapper(Mapper):
    """ Mapper to register on social profile """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        site = ModelAdapter().pb_to_entity(pb)
        if site.key.namespace() not in PARTNER_IDS:
            yield operation.counters.Increment("Skipping non DW1")
            return

        yield operation.counters.Increment("Register on social profile")

        if commit:
            social = SitesVSocial(site.msid, pid=site.pid)
            social.register_social_profile()


class RegisterAccountGroupMapper(Mapper):
    """ Mapper to register on the account group """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        site = ModelAdapter().pb_to_entity(pb)
        if site.key.namespace() not in PARTNER_IDS:
            yield operation.counters.Increment("Skipping non DW1")
            return

        yield operation.counters.Increment("Register on account group")

        if commit:
            VbcAccountGroup.add_account_to_account_group(site.pid, site.msid, site.agid)
