""" Pipelines for generating analytics report """
import csv
import logging
import os
import pickle

from google.appengine.api.mail_errors import InvalidSenderError

import pipeline
import io

from datetime import datetime, timedelta
from google.appengine.api import mail
from google.appengine.api import taskqueue
from pipeline.common import Log
from vpipeline import CommonPipeline

from app.domain.analytics_monthly import BatchMicrositeTrafficAdapter, BatchMicrositeEventsAdapter, \
    BatchMicrositeSourceAdapter, BatchMicrositeImageDetailAdapter, BatchMicrositeMobileViewsAdapter, \
    BatchMicrositePageViewsAdapter, NUM_REFERRERS
from app.domain.analytics_v3 import GoogleAnalyticsV3
from app.domain.url_mappings import lookup_hostslug_msid_mapping_for_msid
from app.models.partner import Partner
from app.models.microsite import Microsite
from app.pipelines.microsite.base import EMAIL_TEMPLATE
from settings import ENVIRONMENT_NAME, SENDER_EMAIL, MS_DEVOPS_EMAIL


# All pipeline run methods will have a differing number of args, pylint:disable=W0221

PULL_QUEUE_NAME = 'monthly-analytics-data'
TASK_LEASE_SECONDS = 10

# This becomes the number that are fetched from G Analytics.
# This number is limited because of the sources ('referrers') report. We can only get back 10,000
# entries from G Analytics, but we are not able to filter by a set of microsites and retrieve back only,
# say, 10 per site. Instead we have to choose a batch size here so that we hopefully get results for
# all the microsites in the maximum of 10,000. So, for 10,000 entries and a batch size of 50, we
# can have 10,000/50 = 200 referrers per microsite on average. Pathalogically, if one single microsite
# has 10,000 referrers on its own, we will not get back any results for anyone else.
# NOTE: batch size has been reduced from 50 to 45 due to the resulting url being too large.
BATCH_SIZE = 45


class GenerateMonthlyAnalyticsReportPipeline(CommonPipeline):
    """ Generate analytics report """

    def run(self, partner_id, batch_size=None):
        """ Run the analytics """
        partner_id = partner_id.upper()
        partner = Partner.get(partner_id)
        pull_queue_tag = self.root_pipeline_id
        batch_size = batch_size or BATCH_SIZE

        if partner and partner.google_analytics_id:
            cursor = None
            do_loop = True
            with pipeline.InOrder():
                while do_loop:
                    qr = Microsite.lookup_all(pid=partner_id, count=batch_size, cursor=cursor)
                    cursor = qr.cursor
                    do_loop = qr.has_more
                    ms_items = [{'msid': entity.msid, 'pmsid': entity.pmsid, 'cid': entity.customer_identifier}
                                for entity in qr.results]
                    yield GetMonthlyAnalyticsReport(ms_items, partner_id, partner.google_analytics_id,
                                                    pull_queue_tag)
                yield SaveMonthlyAnalyticsReport(partner_id, pull_queue_tag)
        else:
            yield Log.info("No partner or analytics id for partner: %s" % partner_id)

    def finalized(self):
        """
        send an email if the pipeline failed to be completed
        """
        if self.was_aborted:
            subject = "Error generating monthly analytics report"
            body = EMAIL_TEMPLATE % (self.root_pipeline_id, os.environ.get('SERVER_NAME'), self.root_pipeline_id)
            try:
                mail.send_mail(SENDER_EMAIL, MS_DEVOPS_EMAIL, subject, body)
            except InvalidSenderError:
                logging.error("Error generating monthly analytics report, pipeline aborted email failed to send.")
                logging.info("%s", body)


class GetMonthlyAnalyticsReport(CommonPipeline):
    """ Get monthly analytics report using analytics v3 api """

    def run(self, ms_items, partner_id, partner_google_analytics_id, pull_queue_tag):
        """ fetch the analytics results """
        msids = [item['msid'] for item in ms_items]
        lines = generate_analytics_data_lines(ms_items, partner_id, partner_google_analytics_id)
        if lines:
            # Push the generated lines to pull queue
            pull_queue = taskqueue.Queue(PULL_QUEUE_NAME)
            pull_queue.add(taskqueue.Task(payload=pickle.dumps(lines), method='PULL', tag=pull_queue_tag))
        yield Log.info("Fetched monthly analytics data for: '%s'", msids)


class SaveMonthlyAnalyticsReport(CommonPipeline):
    """ Save monthly analytics to GCS """

    def run(self, partner_id, pull_queue_tag):
        """ Get the result from pull queue and send it to GCS """
        pull_queue = taskqueue.Queue(PULL_QUEUE_NAME)
        tasks = get_all_tasks_from_queue(pull_queue, pull_queue_tag)

        now = datetime.utcnow()
        last_month = now - timedelta(days=now.day)
        # Saving to bucket -> analytics-report
        filename = "/analytics-report-{env}/monthly/{pid}/report-{year}-{month}.csv".format(
            env=ENVIRONMENT_NAME.lower(), pid=partner_id, year=last_month.year, month=last_month.month)
        gcs_buffer = cloudstorage_api.open(filename, mode='w', content_type='text/csv',
                                           options={'x-goog-acl': 'public-read'})
        csv_header = convert_list_to_csv_row(get_csv_header(NUM_REFERRERS))
        gcs_buffer.write(csv_header)
        for task in tasks:
            lines = pickle.loads(task.payload)
            for line in lines:
                csv_row = convert_list_to_csv_row(line)
                gcs_buffer.write(csv_row)
        gcs_buffer.close()

        delete_tasks(pull_queue, tasks)
        yield Log.info("GCS file written: '%s'", filename)


def get_last_month_start_end_date(now):
    """ Get start and end date for analytics report generation """
    end_of_last_month = now - timedelta(days=now.day)
    start_date = datetime(end_of_last_month.year, end_of_last_month.month, 1).date()
    end_date = datetime(end_of_last_month.year, end_of_last_month.month, end_of_last_month.day).date()
    return start_date, end_date


def get_all_tasks_from_queue(queue, tag, page_size=1000):
    """
    Get all the tasks from the pull queue, 1000 is the most you can get per call of lease tasks
    """
    tasks = []
    while True:
        new_tasks = queue.lease_tasks_by_tag(TASK_LEASE_SECONDS, page_size, tag=tag)
        tasks = tasks + new_tasks
        if len(new_tasks) < page_size:
            break
    return tasks


def delete_tasks(queue, tasks, page_size=1000):
    """
    Delete the tasks from the pull queue, 1000 at a time
    """
    while len(tasks) > 0:
        subset_of_tasks = tasks[:page_size]
        tasks = tasks[page_size:]
        queue.delete_tasks(subset_of_tasks)


def convert_list_to_csv_row(data):
    """ Convert list to csv string format """
    output = io.StringIO()
    writer = csv.writer(output)
    writer.writerow(data)
    return output.getvalue()


def generate_analytics_data_lines(ms_items, partner_id, partner_google_analytics_id):
    """ Generate analytics report lines for a batch of ms items"""
    start_date, end_date = get_last_month_start_end_date(datetime.utcnow())
    msids = [item['msid'] for item in ms_items]
    ga = GoogleAnalyticsV3(msids, partner_google_analytics_id, start_date=start_date, end_date=end_date)
    ga.login()

    # Fetch results for a batch of microsite entities
    slug_dict = fetch_hostslugs(partner_id, msids)
    traffic_data = getdata_traffic(msids, ga.get_traffic_data(use_msid_dimension=True))
    events_data = getdata_events(msids, ga.get_event_data(use_msid_dimension=True))
    image_detail_data = getdata_image_detail(msids, ga.get_image_click_data())
    mobile_views_data = getdata_mobile_views(msids, ga.get_mobile_traffic_data())
    page_views_data = getdata_page_views(msids, ga.get_page_views_data())
    sources_data = getdata_sources(msids, ga.get_source_data(), NUM_REFERRERS)

    lines = []
    for item in ms_items:
        line = []
        msid = item['msid']
        line.extend([msid, item.get('pmsid', ''), item.get('cid', ''), slug_dict.get(msid, '')])
        line.extend(traffic_data[msid])
        line.extend(events_data[msid])
        line.extend(image_detail_data[msid])
        line.extend(mobile_views_data[msid])
        line.extend(page_views_data[msid])
        line.extend(sources_data[msid])
        line.extend(['Monthly', start_date.isoformat(), end_date.isoformat()])
        lines.append(line)
    return lines


def fetch_hostslugs(pid, msids):
    """ Fetch the 'primary' (i.e, oldest, non-redirect) hostslug for a set of msids. """
    slug_dict = {}
    for msid in msids:
        mappings = lookup_hostslug_msid_mapping_for_msid(pid, msid)
        # remove the redirectors
        mappings = [mapping for mapping in mappings if mapping.redirect_hostslug is None]
        # sort by the oldest; will use the oldest for the report
        mappings.sort(key=lambda x: x.created)
        if len(mappings) > 0:
            slug_dict[msid] = mappings[0].hostslug
    return slug_dict


def get_csv_header(referrer_numbers):
    """ Get csv header """
    csv_header = ['MSID', 'Identifier', 'Customer ID', 'Slug', 'Visitors', 'New Visitors', 'Visits', 'Page Views',
                  'Image Clicks', 'Video Clicks', 'Map Clicks', 'Phone Clicks', 'Contact Clicks', 'Our Website Clicks',
                  'QR Clicks', 'Display Ad Clicks', 'Photo Clicks', 'Mobile Site Views', 'About Us Page Views',
                  'Yellow Ads Page Views', 'Text Ads Page Views', 'Contact Us Page Views', 'Video Page Views',
                  'Coupons Page Views', 'Photo Page Views', 'Hours of Operation Page Views']
    for i in range(referrer_numbers):
        csv_header.extend(['Referrer %d' % (i+1), 'Referrer %d Visits' % (i+1)])
    csv_header.extend(['Frequency', 'Start Date', 'End Date'])
    return csv_header


def getdata_traffic(msids, traffic_data):
    """ Returns a list of msid Traffic Data for the given msids stored in a dict keyed by msid """
    traffic_adapter = BatchMicrositeTrafficAdapter(traffic_data)
    stats = {}
    for msid in msids:
        out = []
        traffic_ms_dict = traffic_adapter.get(msid, {})
        out.append(traffic_ms_dict.get('visitors', 0))
        out.append(traffic_ms_dict.get('new_visits', 0))
        out.append(traffic_ms_dict.get('visits', 0))
        out.append(traffic_ms_dict.get('page_views', 0))
        stats[msid] = out
    return stats


def getdata_events(msids, events_data):
    """ Returns the events list for a list of msids stored in a dict keyed by msid """
    event_adapter = BatchMicrositeEventsAdapter(events_data)
    stats = {}
    for msid in msids:
        out = []
        event_ms_dict = event_adapter.get(msid, {})
        out.append(event_ms_dict.get('image_clicks', 0))
        out.append(event_ms_dict.get('video_clicks', 0))
        out.append(event_ms_dict.get('map_clicks', 0))
        out.append(event_ms_dict.get('phone_clicks', 0))
        out.append(event_ms_dict.get('contact_clicks', 0))
        out.append(event_ms_dict.get('website_clicks', 0))
        out.append(event_ms_dict.get('qr_clicks', 0))
        stats[msid] = out
    return stats


def getdata_image_detail(msids, image_click_data):
    """ Adds the image clicks to the custom report """
    image_adapter = BatchMicrositeImageDetailAdapter(image_click_data)
    stats = {}
    for msid in msids:
        out = []
        current_dict = image_adapter.get(msid, {})
        out.append(current_dict.get('yellow_clicks', 0))
        out.append(current_dict.get('photo_clicks', 0))
        stats[msid] = out
    return stats


def getdata_mobile_views(msids, traffic_data):
    """ Returns just the Mobile Page Views """
    mobile_adapter = BatchMicrositeMobileViewsAdapter(traffic_data)
    stats = {}
    for msid in msids:
        current_dict = mobile_adapter.get(msid, {})
        stats[msid] = [current_dict.get('mobile_views', 0)]
    return stats


def getdata_page_views(msids, page_views_data):
    """ returns the pageviews for directwest for each known page """
    pageview_adapter = BatchMicrositePageViewsAdapter(page_views_data)
    stats = {}
    for msid in msids:
        out = []
        event_ms_dict = pageview_adapter.get(msid, {})
        out.append(event_ms_dict.get('about-us', 0))
        out.append(event_ms_dict.get('yellow-ads', 0))
        out.append(event_ms_dict.get('text-ads', 0))
        out.append(event_ms_dict.get('contact-us', 0))
        out.append(event_ms_dict.get('videos', 0))
        out.append(event_ms_dict.get('coupons', 0))
        out.append(event_ms_dict.get('photos', 0))
        out.append(event_ms_dict.get('_mvh/hours', 0))
        stats[msid] = out
    return stats


def getdata_sources(msids, source_data, referrer_numbers):
    """ Returns the referrer information for a list of msids stored in a dict keyed by msid """
    source_adapter = BatchMicrositeSourceAdapter(source_data)
    stats = {}
    for msid in msids:
        out = []
        source_list = source_adapter.get(msid, [])
        for i in range(referrer_numbers):
            if len(source_list) > i:
                referrer = source_list[i][0]
                referrer_visits = source_list[i][1]
            else:
                referrer = ''
                referrer_visits = ''
            out.append(referrer)
            out.append(referrer_visits)
        stats[msid] = out
    return stats
