""" Pipeline for changing the customer ids. This pipeline is part of a pack that is meant to be run in
MS, SM, SR, and VBC.
!!!!!!!!!!!!!!!!VBC MUST RUN BEFORE MS.
******************************************!!!!!!!!!!!!WARNING!!!!!!!!!!!!!**********************************************
If by any chance you find this, note that running this will lock the unique and account(_group) entities for roughly up
to  one second per customer id to change.
***********************************************************************************************************************
"""
import logging

from google.appengine.ext import ndb

from vpipeline.pickles import PickleMapperDictInterface

from app.models.microsite import Microsite

# All pipeline run methods will have a differing number of args, pylint:disable=W0221


# Ignore interface not implemented warning pylint: disable=R0923
class ChangeCustomerIdsOnAccounts(PickleMapperDictInterface):
    """ Change customer ids """

    # pylint: disable=R0923
    def __call__(self, data_dict):
        """Give this picklemapperdictinterface a csv file path.
            path is usually in the form of gs://bucketname/filepath

        expected format:
        account_group_id,customer_id,partner_id
        AG-1234,cust-1234,pid"""

        partner_id = data_dict['partner_id'].upper()
        account_group_id = data_dict['account_group_id']
        customer_id = data_dict['customer_id']

        if not account_group_id or not customer_id or not partner_id:
            logging.warning("Skipped a Line(missing value): account_group_id: %s customer_id: %s partner_id: %s.",
                            account_group_id,
                            customer_id,
                            partner_id)
            return

        def txn():
            """Ensure the customer_id is set on vbc account group and set the customer_id. """
            microsite = Microsite.lookup_by_agid(agid=account_group_id, pid=partner_id)
            if not microsite:
                logging.warning("Skipped a Line account with agid %s not found", account_group_id)
                return
            if microsite.customer_identifier == customer_id:
                logging.info("Microsite with account_group %s already has customer id %s",
                             account_group_id,
                             customer_id)
                return

            microsite.account_group.customer_identifier = customer_id
            microsite.put()
            logging.info("updated: agid %s with customer_id %s", account_group_id, customer_id)

        ndb.transaction(txn, xg=True)
