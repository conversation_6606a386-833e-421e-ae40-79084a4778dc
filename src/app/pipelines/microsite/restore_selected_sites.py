"""
Pipeline to restore a deleted partner and associated sites
"""
#pylint: skip-file

from google.appengine.datastore import entity_pb
from google.appengine.ext.ndb.model import ModelAdapter

from app.domain.social import SitesVSocial
from app.domain.vbc import VbcAccountGroup

from app.models.microsite import Microsite, Navigation
from app.models.partner import Partner
from app.models.url_mappings import HostSlugMsidMapping as HostSlugMsidMappingModel
from app.models.blob_mappings import MsidBlobMapping as MsidBlobMappingModel
from app.models.url_mappings import HostPidMapping as HostPidMapping
from app.models.page import Page
from app.pipelines.microsite.orphaned_msids import MSIDS

from mapreduce import context
from mapreduce import mapreduce_pipeline
from mapreduce import operation
from mapreduce.api.map_job import Mapper
from mapreduce.input_readers import GoogleCloudStorageInputReader
from mapreduce.pipeline_base import PipelineBase

from vrestore.utils import fixKeys


class RestoreSites(PipelineBase):
    """
    Restores the sites
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = "backup.MicrositeModel(s~microsite-prod)/156744967694750680717634E74541"

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'restore microsite',
            'app.pipelines.microsite.restore_selected_sites.RestoreMicrositeMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RestoreMappings(PipelineBase):
    """
    Restores the site host mappings
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = "backup.HostSlugMsidMapping(s~microsite-prod)/15674496747683778941778818EFC6D"

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'restore mappings',
            'app.pipelines.microsite.restore_selected_sites.RestoreMappingsMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RestoreNavigation(PipelineBase):
    """
    Restores the site navigation
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = "backup.Navigation(s~microsite-prod)/156744967579232300116216314F2AC"

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'restore navigation',
            'app.pipelines.microsite.restore_selected_sites.RestoreNavigationMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RestorePage(PipelineBase):
    """
    Restores the site page
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = "backup.Page(s~microsite-prod)/1567449668252256434927246247086"

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'restore page',
            'app.pipelines.microsite.restore_selected_sites.RestorePageMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RestoreBlobMappings(PipelineBase):
    """
    Restores the blob mappings
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = "backup.MsidBlobMapping(s~microsite-prod)/156744967686440585358327ABD187E"

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'restore blob mappings',
            'app.pipelines.microsite.restore_selected_sites.RestoreBlobMappingsMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RegisterSocialProfile(PipelineBase):
    """
    Register on the social profile
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = "backup.MicrositeModel(s~microsite-prod)/156744967694750680717634E74541"

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'register social profile',
            'app.pipelines.microsite.restore_selected_sites.RegisterSocialProfileMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RegisterAccountGroup(PipelineBase):
    """
    Register on the account group
    """
    # argument number differs from parent class
    # pylint: disable=W0221
    def run(self, shard_count=32, commit=False):
        """ run """

        bucket = 'vbackup-microsite-prod'
        directory = "backup.MicrositeModel(s~microsite-prod)/156744967694750680717634E74541"

        params = {
            'input_reader': {
                GoogleCloudStorageInputReader.BUCKET_NAME_PARAM: bucket,
                GoogleCloudStorageInputReader.OBJECT_NAMES_PARAM: ['%s/*' % directory],
            },
            "commit": commit,
        }
        yield mapreduce_pipeline.MapperPipeline(
            'register account group',
            'app.pipelines.microsite.restore_selected_sites.RegisterAccountGroupMapper',
            input_reader_spec='mapreduce.input_readers.GoogleCloudStorageRecordInputReader',
            params=params,
            shards=256
        )


class RestoreMicrositeMapper(Mapper):
    """ Mapper to restore sites for a partner """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        site = ModelAdapter().pb_to_entity(pb)
        if site.msid not in MSIDS:
            yield operation.counters.Increment("Skipping site")
            return

        yield operation.counters.Increment("Saving site")

        if commit:
            yield operation.db.Put(site)


class RestoreMappingsMapper(Mapper):
    """ Mapper to restore sites for a partner """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        mapping = ModelAdapter().pb_to_entity(pb)
        if mapping.msid not in MSIDS:
            yield operation.counters.Increment("Skipping mapping")
            return

        yield operation.counters.Increment("Saving mapping")

        if commit:
            yield operation.db.Put(mapping)


class RestoreNavigationMapper(Mapper):
    """ Mapper to restore sites navigation for a partner """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        navigation = ModelAdapter().pb_to_entity(pb)
        if navigation.msid not in MSIDS:
            yield operation.counters.Increment("Skipping navigation")
            return

        yield operation.counters.Increment("Saving navigation")

        if commit:
            yield operation.db.Put(navigation)


class RestorePageMapper(Mapper):
    """ Mapper to restore sites navigation for a partner """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        navigation = ModelAdapter().pb_to_entity(pb)
        if navigation.msid not in MSIDS:
            yield operation.counters.Increment("Skipping page")
            return

        yield operation.counters.Increment("Saving page")

        if commit:
            yield operation.db.Put(navigation)


class RestoreBlobMappingsMapper(Mapper):
    """ Mapper to restore sites for a partner """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        blob_mapping = ModelAdapter().pb_to_entity(pb)
        if blob_mapping.msid not in MSIDS:
            yield operation.counters.Increment("Skipping blog mapping")
            return

        yield operation.counters.Increment("Saving blob mapping")

        if commit:
            yield operation.db.Put(blob_mapping)


class RegisterSocialProfileMapper(Mapper):
    """ Mapper to register on social profile """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        site = ModelAdapter().pb_to_entity(pb)
        if site.msid not in MSIDS:
            yield operation.counters.Increment("Skipping register social profile")
            return

        yield operation.counters.Increment("Register on social profile")

        if commit:
            social = SitesVSocial(site.msid, pid=site.pid)
            social.register_social_profile()


class RegisterAccountGroupMapper(Mapper):
    """ Mapper to register on the account group """

    def __call__(self, slice_ctx, val):
        """ process """
        params = context.get().mapreduce_spec.mapper.params
        commit = params['commit']

        pb = entity_pb.EntityProto(contents=val)
        fixKeys(pb, 's~microsite-prod')

        site = ModelAdapter().pb_to_entity(pb)
        if site.msid not in MSIDS:
            yield operation.counters.Increment("Skipping register on account group")
            return

        yield operation.counters.Increment("Register on account group")

        if commit:
            VbcAccountGroup.add_account_to_account_group(site.pid, site.msid, site.agid)
