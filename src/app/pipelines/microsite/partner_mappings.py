"""Pipelines for partner msid mappings"""

from google.appengine.ext import ndb

from mapreduce import operation
from mapreduce import context
from mapreduce.input_readers import DatastoreInputReader
from mapreduce.mapreduce_pipeline import MapperPipeline

from vpipeline import CommonPipeline

from app.models.url_mappings import HostSlugMsidMapping, HostPidMapping


# All pipeline run methods will have a differing number of args, pylint:disable=W0221
class BulkUpdateMsidMappingsPipeline(CommonPipeline):
    """
    Update all msid mappings for a partner to direct to new hostname
    """

    def run(self, partner_id, hostname, processing_rate=10):
        """Start mapper job"""

        params = {
            'input_reader': {
                DatastoreInputReader.ENTITY_KIND_PARAM: 'app.models.url_mappings.HostSlugMsidMapping',
                DatastoreInputReader.FILTERS_PARAM: [('pid', '=', partner_id)]
            },
            'processing_rate': processing_rate,
            'hostname': hostname
        }

        yield MapperPipeline(
            'Remap MSIDs to new microsite hostnames',
            'app.pipelines.microsite.partner_mappings.BulkUpdateMsidMappingsPipeline.remap_msid_hostnames',
            'mapreduce.input_readers.DatastoreInputReader',
            params=params,
            shards=8
        )

    @staticmethod
    def remap_msid_hostnames(mapping):
        """
        Create new mapping and redirect old mapping to new hostname
        """
        hostname = context.get().mapreduce_spec.mapper.params.get('hostname', None)

        # pylint: disable=no-value-for-parameter
        @ndb.transactional(xg=True)
        def tx(old_mapping, hostname, slug):
            """ Update an existing hostslug/msid mapping with new hostslug. """

            return HostSlugMsidMapping.update_mappings([old_mapping], hostname, slug=slug)

        split_hostslug = mapping.hostslug.split('/')
        if len(split_hostslug) > 1:

            slug = split_hostslug[-1]
            yield tx(mapping, hostname, slug)
            yield operation.counters.Increment('Hostnames Remapped')
        else:
            yield operation.counters.Increment('Mappings skipped')


class SetPartnerDefaultHostPipeline(CommonPipeline):
    """
    Update the default host for every partner, only update for partners with a single host
    """

    COUNTER_HOST_WAS_SET_AS_DEFAULT = "Updated host to default for partner with single host"
    COUNTER_HOST_WAS_NOT_SET_AS_DEFAULT = "Did not update for partner with multiple hosts"

    def run(self, commit=False):
        """Start mapper job"""

        params = {
            'input_reader': {
                DatastoreInputReader.ENTITY_KIND_PARAM: 'app.models.partner.Partner',
            },
            'commit': commit
        }

        yield MapperPipeline(
            'Set Default Mapping For Partner',
            'app.pipelines.microsite.partner_mappings.SetPartnerDefaultHostPipeline.set_default_mapping_for_partner',
            'mapreduce.input_readers.DatastoreInputReader',
            params=params,
            shards=16
        )

    @staticmethod
    def set_default_mapping_for_partner(partner):
        """
        Update the default host for a partner if it only has one host
        """
        params = context.get().mapreduce_spec.mapper.params
        commit = params.get('commit')
        host_query = HostPidMapping.lookup_by_pid(partner.pid, uses_slugs=True)
        if len(host_query.results) == 1:
            new_default_host = host_query.results[0]
            if commit:
                new_default_host.is_default = True
                yield operation.db.Put(new_default_host)
            yield operation.counters.Increment(SetPartnerDefaultHostPipeline.COUNTER_HOST_WAS_SET_AS_DEFAULT)
        else:
            yield operation.counters.Increment(SetPartnerDefaultHostPipeline.COUNTER_HOST_WAS_NOT_SET_AS_DEFAULT)
