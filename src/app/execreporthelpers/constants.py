""" Constants related to the Executive Report """
from enum import Enum


class BaseEnum(str, Enum):
    """
    Base Enum with an `all()` function which returns all values
    """

    @classmethod
    def all(cls):
        """
        Returns a list of values in the Enum
        """
        return [member.value for member in cls]


class ReportFrequencies(BaseEnum):
    """
    Valid executive report frequencies
    """
    WEEKLY = u'weekly'
    MONTHLY = u'monthly'


class CardWidths(BaseEnum):
    """
    Widths for various executive report cards
    """
    HALF = u'HALF'
    FULL = u'FULL'
    QUARTER = u'QUARTER'


class ValueFormats(BaseEnum):
    """
    Value formats for executive report cards
    """
    FRACTION = u'FRACTION'
    NUMBER = u'NUMBER'


class ReportColors(BaseEnum):
    """
    Valid executive report Colors
    """

    BLACK = u'BLACK'
    WHITE = u'WHITE'
    GRAY = u'GRAY'
    RED = u'RED'
    GREEN = u'GREEN'
    BLUE = u'BLUE'
    YELLOW = u'YELLOW'
    PURPLE = u'PURPLE'
    ORANGE = u'ORANGE'
