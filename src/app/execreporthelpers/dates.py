""" dates.py """
import datetime
from dateutil.relativedelta import relativedelta

import elastic
from .constants import ReportFrequencies


WEEKDAY_SUNDAY = 6  # datetime.date.weekday() value
CURRENT_PERIOD_BUCKET_KEY = 'current_period'
PREVIOUS_PERIOD_BUCKET_KEY = 'previous_period'


def get_most_recent_report_date(frequency):
    """
    Get most recent report date
    If frequency is WEEKLY will get most recent *past* Sunday
    If frequency is MONTHLY will get the first day of current month
    """
    report_date = datetime.datetime.utcnow()
    if frequency.lower() == 'weekly':
        if report_date.weekday() != 6:
            report_date = report_date - datetime.timedelta(days=report_date.weekday() + 1)
    elif frequency.lower() == 'monthly':
        if report_date.day != 1:
            report_date = report_date - datetime.timedelta(days=report_date.day - 1)
    return report_date


def _datetime_to_ms_since_epoch(date):
    """Convert a datetime object to milliseconds from epoch"""
    if isinstance(date, datetime.datetime):
        date = date.date()
    seconds_since_epoch = (date - datetime.date(1970, 1, 1)).total_seconds()
    return int(seconds_since_epoch) * 1000


def calculate_report_date_range_for_elastic_query(frequency, report_date):
    """
    Calculate and return the range of the report period as a tuple of
    (start_date, end_date), according to the frequency and report date.
    If the report frequency is "weekly", the report date is the *last* day of
    the period (i.e. last Sunday of the report week).
    E.g., a weekly report_date of 2017-07-16 means that the report should
    include 2017-07-10 to 2017-07-17.
    If the frequency is "monthly", the report date is the *first* day of the
    NEXT month, (i.e. the first day of the month AFTER the report period).
    E.g. a monthly report_date of 2017-06-01 means that the report should
    include data for 2017-05-01 to 2017-06-01.
    These dates may seem strange, but note that because of the way elasticsearch
    does queries, if you want to select all data from July 10th to July 16th, you
    actually need an end date of July 17th so that all of July 16th is included in
    the query.
    """
    validate_report_date(frequency, report_date)
    if frequency == "weekly":
        start_date = (report_date - relativedelta(weeks=1)) + relativedelta(days=1)
        end_date = report_date + relativedelta(days=1)
    elif frequency == "monthly":
        start_date = report_date - relativedelta(months=1)
        end_date = report_date
    return start_date, end_date


def validate_report_date(frequency, report_date):
    """ Validate report_date for given report frequency """
    if frequency == "weekly":
        if report_date.weekday() != WEEKDAY_SUNDAY:
            raise ValueError(
                u"Invalid report_date day of week for weekly frequency: {} != SUNDAY ({})".format(
                    report_date.weekday(), WEEKDAY_SUNDAY,
                ),
            )
    elif frequency == "monthly":
        if int(report_date.day) != 1:
            raise ValueError(
                u"Invalid report_date for monthly frequency: {} != 1".format(report_date.day),
            )
    else:
        raise ValueError(u"Invalid report frequency: {}".format(frequency))


def build_date_ranges(report_frequency, report_date):
    """
    Builds a list of elastic range objects, one for the current report period, one for the previous period
    """
    validate_report_date(report_frequency, report_date)

    if report_frequency == ReportFrequencies.WEEKLY:
        previous_report_date = report_date - relativedelta(weeks=1)
    else:
        previous_report_date = report_date - relativedelta(months=1)

    start_date, end_date = calculate_report_date_range_for_elastic_query(report_frequency, report_date)
    prev_start_date, prev_end_date = calculate_report_date_range_for_elastic_query(report_frequency,
                                                                                   previous_report_date)

    current_report_range = elastic.RangeItem(from_=_datetime_to_ms_since_epoch(start_date),
                                             to_=_datetime_to_ms_since_epoch(end_date))
    prev_report_range = elastic.RangeItem(from_=_datetime_to_ms_since_epoch(prev_start_date),
                                          to_=_datetime_to_ms_since_epoch(prev_end_date))

    return current_report_range, prev_report_range
