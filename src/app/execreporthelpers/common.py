""" Common functions used by exec report """
import logging

from google.appengine.ext import ndb

from .constants import CardWidths, ValueFormats


def build_basic_number_payload(unique_id, title, value, width=CardWidths.HALF.value, precision=0, description=None,
                               next_url=None, value_format=ValueFormats.NUMBER.value):
    """
    Builds the report data for a "BASIC_NUMBER" template type
    """
    return _build_base_number_template_payload('BASIC_NUMBER',
                                               unique_id,
                                               title,
                                               str(value),
                                               width,
                                               value_format,
                                               precision,
                                               description=description,
                                               next_url=next_url)


def build_number_with_change_payload(unique_id, title, value, change=0, width=CardWidths.HALF.value, precision=0,
                                     description=None, next_url=None, value_format=ValueFormats.NUMBER.value):
    """
    Builds the report data for a "NUMBER_WITH_CHANGE" template type
    """
    payload = _build_base_number_template_payload('NUMBER_WITH_CHANGE',
                                                  unique_id,
                                                  title,
                                                  str(value),
                                                  width,
                                                  value_format,
                                                  precision,
                                                  description=description,
                                                  next_url=next_url)
    payload[u'change'] = str(change)
    return payload


def _build_base_number_template_payload(template_type, unique_id, title, value, width, value_format, precision,
                                        description=None, next_url=None):
    """
    Build the base dictionary for a number card
    """
    payload = {
        u'template_type': template_type,
        u'title': title,
        u'value': str(value),
        u'unique_id': unique_id or title,
        u'width': width,
        u'format': value_format,
        u'precision': precision
    }
    if description:
        payload[u'description'] = description
    if next_url:
        payload[u'next_url'] = next_url
    return payload


def build_bar_chart_payload(unique_id, title, chart_data, next_url=None, width=CardWidths.FULL.value):
    """
    Builds a payload for the BAR_CHART exec report template

    :param chart_data: must be a list of dicts in the form
    [{
        "label": "First bar's label",
        "value": "42",
        "fill_percent": "70",
        "fill_color": "GREEN" # Valid colour types: BLACK, WHITE, GRAY, RED, GREEN, BLUE, YELLOW, PURPLE, ORANGE
    }]
    """
    payload = {
        u"template_type": u"BAR_CHART",
        u"unique_id": unique_id,
        u"title": title,
        u"width": width,
        u"bar_chart": chart_data,
    }
    if next_url:
        payload[u'next_url'] = next_url
    return payload


def build_call_to_action_payload(unique_id, title, description, next_url, next_url_label, width=CardWidths.FULL.value):
    """
    Builds a payload for the CALL_TO_ACTION exec report template
    """
    payload = {
        u"template_type": u"CALL_TO_ACTION",
        u"unique_id": unique_id,
        u"title": title,
        u"description": description,
        u"width": width,
        u"next_url": next_url,
        u"next_url_label": next_url_label
    }
    return payload


def build_info_list_item(header, body=None, footer=None, info_text=None, info_text_color=None, info_text_icon=None,
                         image_url=None):
    """
    Build a list item for the INFO_LIST card type
    """
    kwargs = {
        'body': body,
        'footer': footer,
        'info_text': info_text,
        'info_text_color': info_text_color,
        'info_text_icon': info_text_icon,
        'image_url': image_url,
    }
    payload = {
        u'header': header
    }
    payload.update({k: v for k, v in kwargs.iteritems() if v})
    return payload


def build_info_list_card_payload(unique_id, title, list_items, width=CardWidths.FULL.value, next_url=None):
    """
    Builds a payload for the INFO_LIST exec report template

    :param list_items: must be a list of build_list_item() values
    """
    payload = {
        u'title': title,
        u'template_type': u'INFO_LIST',
        u'unique_id': unique_id or title,
        u'width': width,
        u'list': list_items,
    }
    if next_url:
        payload[u'next_url'] = next_url
    return payload


def build_executive_report_payload(report_date, account_group_id, report_data, category):
    """
    Constructs the payload required to send data to the executive report.
    report_data can be built using the appropriate _build_TEMPLATE_TYPE_payload helper.
    """
    if not isinstance(report_data, list):
        report_data = [report_data]
    return {
        u'report_date': report_date.strftime('%Y-%m-%dT%H:%M:%SZ'),
        u'account_id': account_group_id,
        u'category': category,
        u'report_data': report_data
    }


def generate_report_data(*args):
    """
    Call each function in report_stat_functions and collect the results.
    """
    report_stat_functions = args[0] if len(args) > 0 else []
    report_stat_function_arguments = args[1:]
    report_stat_function_values = []
    logging.info(u'Generating executive report data for ' +
                 (u'{} ' * len(report_stat_function_arguments)).format(*report_stat_function_arguments))
    for func in report_stat_functions:
        result = None
        try:
            result = func(*report_stat_function_arguments)
        except Exception:
            logging.exception('Error executing %s', func.func_name)
        report_stat_function_values.append(result or None)
    report_data = []
    for (stat, func) in zip(report_stat_function_values, report_stat_functions):
        if isinstance(stat, ndb.Future):
            try:
                stat = stat.get_result()
            except Exception:
                logging.exception('Error executing %s', func.func_name)
                stat = None
        if not isinstance(stat, list):
            stat = [stat]

        logging.info('Exec report data for %s: %s', func.func_name, stat)
        report_data.extend(stat)
    return [datum for datum in report_data if datum]
