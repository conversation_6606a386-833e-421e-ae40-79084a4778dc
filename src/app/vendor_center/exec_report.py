""" exec_report.py """
import json
import logging

from google.appengine.api.urlfetch_errors import DeadlineExceededError
from google.appengine.ext.deferred import SingularTaskFailure, PermanentTaskFailure

import statsd
from app.models.microsite import AccountGroupModel
from ._authentication import build_vendor_center_url, make_signed_request


ACCEPTABLE_FREQUENCIES = ['weekly', 'monthly']


def send_report_data(payload, frequency):
    """ Sends the provided activity. First retrieves an oauth token. """
    if frequency not in ACCEPTABLE_FREQUENCIES:
        raise ValueError('frequency must be one of {}'.format(','.join(ACCEPTABLE_FREQUENCIES)))

    account_group_id = payload.get('account_id')
    account_group = AccountGroupModel.get(account_group_id)
    if not account_group:
        logging.warning('Account group for exec report generation not found: %s', account_group_id)
        return
    if account_group.deleted_flag:
        logging.warning('Account group for exec report generation is deleted: %s', account_group_id)
        return

    logging.info("Creating report data with payload: ")
    logging.info("%r", payload)

    url = build_vendor_center_url(f'/api/v1/executive_report/{frequency}/')

    try:
        response = make_signed_request(url, payload=json.dumps(payload), deadline=60)
    except DeadlineExceededError as e:
        _send_datadog_tick('deadline_exceeded')
        logging.warning(e.message)
        raise SingularTaskFailure()

    logging.info('Create report data response %s, %s', response.status_code, response.content)
    if response.status_code >= 500:
        _send_datadog_tick('unknown')
        raise SingularTaskFailure(response.content, response.status_code)
    elif response.status_code >= 400:
        if response.status_code == 409:
            logging.warning('Already sent this report data: %s', account_group_id)
            _send_datadog_tick('report_already_sent')
        elif response.status_code == 404:
            logging.warning('Account group not found by vendor center: %s', account_group_id)
            _send_datadog_tick('vendor_center_not_found')
        elif response.status_code == 403:
            logging.warning('Vendor center says the account does not have MS, %s', account_group_id)
            _send_datadog_tick('no_MS')
        else:
            logging.warning('Unknown 400-level error code: %s\n%s', response.status_code, response.content)
            _send_datadog_tick('unknown')
        raise PermanentTaskFailure(response.content, response.status_code)

    _send_datadog_tick('it_worked', successful=True)


def _send_datadog_tick(reason, successful=False):
    """ Send a tick to Datadog. """
    if successful:
        result = 'result:success'
    else:
        result = 'result:error'
    reason_tag = f'reason:{reason}'
    metric = statsd.StatsDMetric('executive_report', tags=[result, reason_tag])
    statsd.tick_metric(metric)
