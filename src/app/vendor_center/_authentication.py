""" authentication.py """

import logging
from urllib.parse import urlunparse

from google.appengine.api import urlfetch

import settings
from vauth.foundational_products.marketplace_sso._domain import get_auth_token, get_developer_central_host


def build_vendor_center_url(path, query=None):
    """
    Returns a full url to developer central for the current environment
    """
    scheme = 'http' if settings.IS_DEV_APPSERVER else 'https'
    return urlunparse((scheme, get_developer_central_host(), path, None, query, None))


def make_signed_request(url, method=urlfetch.POST, **kwargs):
    """
    Makes a signed request to a url with the payload passed in. Will retrieve an oauth token first.
    :param url: URL to make a request to
    :param method: The method to use for the request
    :param kwargs: Any other urlfetch specific params to pass in. Examples: deadline, payload
    :return: Response from urlfetch
    """
    oauth_token = get_auth_token()
    response = urlfetch.fetch(url,
                          method=method,
                          headers={'Content-Type': 'application/json', 'Authorization': 'Bearer ' + oauth_token},
                          **kwargs)
    if response.status_code == 401:
        logging.info("Got 401, retrying with fresh access token")
        oauth_token = get_auth_token(force_refresh=True)
        headers = {'Content-Type': 'application/json', 'Authorization': 'Bearer ' + oauth_token}
        response = urlfetch.fetch(url, headers=headers, **kwargs)

    return response
