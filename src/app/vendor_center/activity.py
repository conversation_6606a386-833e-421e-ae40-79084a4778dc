"""
Domain for interfacing with Developer Central
"""

import json
import logging

from ._authentication import build_vendor_center_url, make_signed_request


class ActivityPostFailedException(Exception):
    """ Raised when failing to POST activity to vendor center """
    pass


def send_activity(activity):
    """ Sends the provided activity. """
    logging.info("Sending Activity")
    logging.info("%r", activity)
    logging.info("===" * 20)

    url = build_vendor_center_url('/api/v1/activity/')
    payload = json.dumps(activity)

    response = make_signed_request(url, payload)

    logging.info('Activity response %s, %s', response.status_code, response.content)
    if response.status_code != 200:
        raise ActivityPostFailedException()
