""" product_navbar.py """
from urllib.parse import urlencode

import logging
from google.appengine.api import urlfetch

import settings
from ._authentication import build_vendor_center_url, make_signed_request


class ProductNavbarGetFailedException(Exception):
    """ Raised when failing to GET product navbar data from vendor center """


def get_product_navbar_data(account_group_id, app_id, user_id):
    """ Get product navbar data from Vendor Center """
    query = urlencode({
        'accountId': account_group_id,
        'appId': app_id,
        'userId': user_id
    })

    url = build_vendor_center_url(settings.DEVELOPER_CENTRAL_PRODUCT_NAVBAR_DATA_PATH, query=query)
    logging.info('Product navbar url: %s', url)

    response = make_signed_request(url, method=urlfetch.GET, deadline=30)
    if response.status_code != 200:
        raise ProductNavbarGetFailedException()

    return response
