"""
Helper functions to update hostname for list of msid with the desired hostname
"""
import logging

from google.appengine.ext import deferred

from app.domain.url_mappings import lookup_hostslug_msid_mapping_for_msid, remove_msid_for_hostname_slug, \
    add_hostslug_msid_mapping
from app.models.microsite import Microsite as MicrositeModel


def update_hostname(pid, msid, new_host, new_slug=None):
    """
    Updates the microsite hostname with new_host (along with new_slug if provided otherwise use old slug)
    If new_host provided is same as older host, new_slug should be different than old slug
    Prints out the old host/slug along with new host/slug
    """
    ms_model = MicrositeModel.get_by_msid(msid, pid)
    if not ms_model:
        logging.error("No microsite found for msid: %s, pid: %s", msid, pid)
        return

    if not new_host:
        logging.error("No new hostname provided for msid: %s, pid: %s", msid, pid)
        return

    host_slugs = lookup_hostslug_msid_mapping_for_msid(pid, msid)
    host_slugs = [hs for hs in host_slugs if hs.redirect_hostslug is None]

    for host_slug in host_slugs:
        if new_host != host_slug.host or (new_slug and new_slug != host_slug.slug):
            slug = new_slug or host_slug.slug
            try:
                # Add the new host/slug
                add_hostslug_msid_mapping(new_host, slug, pid, msid)
                # Removes the older host/slug and redirects it to new one
                remove_msid_for_hostname_slug(host_slug.host, slug=host_slug.slug,
                                              redirect_host=new_host, redirect_slug=slug)
                logging.info("Updating microsite msid:%s with hostname: %s", msid, new_host)
            except Exception:
                logging.exception("Error occurred while updating host/slug for msid: %s, pid:%s.", msid, pid)


def update_hostnames(pid, site_list):
    """
    Updates hostname of site for the partner: pid
    site_list is a list of (msid, new_hostname) tuples
    Usage:
    from app.scripts.update_hostname import print_hostnames, update_hostnames
    site_list = [
    ('MS-F3RJKD3C','vend2.localhost.com'),
    ('MS-SLCDTGX6','vend2.localhost.com'),
    ('MS-HH87QFXT','vend3.localhost.com')]
    update_hostnames('VEND', site_list)
    """
    for msid, host in site_list:
        deferred.defer(update_hostname, pid, msid, host)

    logging.info("%s microsite updated with new hostname/slug", len(site_list))


def print_hostnames(pid, site_list):
    """
    Prints hostname of site for the partner: pid
    site_list is a list of (msid, new_hostname) tuples
    Usage:
    from app.scripts.update_hostname import print_hostnames, update_hostnames
    site_list = [
    ('MS-F3RJKD3C','vend2.localhost.com'),
    ('MS-SLCDTGX6','vend2.localhost.com'),
    ('MS-HH87QFXT','vend3.localhost.com')]
    print_hostnames('VEND', site_list)
    """
    print(f"Printing {len(site_list)} sites for pid: {pid}")
    for msid, _ in site_list:
        new_host_slugs = lookup_hostslug_msid_mapping_for_msid(pid, msid)
        for host_slug in new_host_slugs:
            print("{}, {}, {}, {}, {}".format(pid, msid, host_slug.host, host_slug.slug,
                                                   host_slug.redirect_hostslug))
