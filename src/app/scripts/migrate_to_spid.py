"""
Functions to help with migrating social profile ids into account entities
"""

import logging

from google.appengine.ext import deferred

from app.models.microsite import Microsite
from app.models.partner import Partner
from app.domain.social import SitesVSocial


def migrate_microsites_to_spid(partner_list):
    """
    Looks through each microsite to see if it has a valid spid.
    If it doesn't then it gets core to create one for it and adds it to the entity

    Do a regex search for the below values to see those types of results
    'SPID MIGRATION - OKAY' - The microsite already has a working spid
    'SPID MIGRATION - DON'T MATCH' - should manually investigate any microsite spids that don't match those in core
    'SPID MIGRATION - REGISTERED' - The microsite is not registered with core so it was registered
    'SPID MIGRATION - MICROSITE MISSING' - The microsite does not have the spid registered in core so add it

    run the script in the interactive console using the below code:
    from app.scripts.migrate_to_spid import migrate_microsites_to_spid
    migrate_microsites_to_spid(['PID1', 'PID2', ...])

    :param: partner_list a list of pids under which to verify the microsite spids ['pid1', 'pid2', ...]
    """

    # get each partner's spgid
    partner_list = [{'pid': pid, 'spgid': Partner.build_key(pid).get().spgid} for pid in partner_list]

    # find the microsites without valid spids and give them valid spids
    num_microsites_processed = 0
    for partner in partner_list:
        microsites = Microsite.lookup_all(pid=partner['pid'], count=10000)
        for entry in microsites:

            # run the task
            print("processing " + entry.pid + ":" + entry.msid)
            num_microsites_processed += 1
            deferred.defer(_process, partner['spgid'], entry)

    print(f"see logs for processing details on {num_microsites_processed} microsites")


def _process(spgid, microsite):
    """ register microsite with new spid if it doesn't have a valid one

    :param spgid: social profile group id for the microsite's partner
    :param microsite: a microsite entity
    """
    # get the spid core has registered for this microsite
    core_spid = SitesVSocial(microsite.msid).spid

    # if the microsite has an spid listed, then try to get the profile associated this microsite from core
    # and check they are the same spid
    if microsite.spid and core_spid:
        if core_spid == microsite.spid:
            logging.info("SPID MIGRATION - OKAY Valid spid: %s", core_spid)
        else:
            # should manually investigate any microsite spids that don't match those in core
            logging.info("SPID MIGRATION - DON'T MATCH core spid: %s; microsite spid: %s", core_spid, microsite.spid)

    # The microsite is not registered with core so register it
    elif not microsite.spid and not core_spid:
        microsite.spid = SitesVSocial(microsite.msid).create_social_profile(spgid=spgid)
        logging.info("SPID MIGRATION - REGISTERED. spid:%s for pid:%s, msid:%s",
                     microsite.spid, microsite.pid, microsite.msid)
        microsite.put()

    # The microsite does not have the spid registered in core so add it
    elif not microsite.spid and core_spid:
        logging.info("SPID MIGRATION - MICROSITE MISSING. added spid:%s", core_spid)
        microsite.spid = core_spid
        microsite.put()

    # Core does not have the spid this microsite has in its entity
    elif microsite.spid and not core_spid:
        logging.info("SPID MIGRATION - CORE MISSING spid:%s", microsite.spid)
