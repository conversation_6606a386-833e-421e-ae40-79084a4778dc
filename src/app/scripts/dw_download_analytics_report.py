"""
Get monthly report from GCS and save it to ftp directory

This file should reside in the DW server where we save file for them to get via ftp
Copy this file and make it executable

$ chmod +x dw_download_analytics_report.py

We want to execute this file beginning of every month so that ftp is up to date with the monthly analytics report.
Add the execution of this file to crontab.

$ crontab -e

Copy following line to the crontab editor and save the cron
00 15 1 * * /home/<USER>/dw_download_analytics_report.py
"""
import urllib.request, urllib.parse, urllib.error
from datetime import datetime, timedelta


def save_file_to_ftp(partner_id=None, environment=None):
    """ Downloads analytics report from Google Cloud Storage and saves it to target path """
    now = datetime.utcnow()
    last_month = now - timedelta(days=now.day)
    year_month = f"{last_month.year}-{last_month.month}"
    partner_id = partner_id or 'DW1'
    environment = environment or 'prod'
    file_url = "http://storage.googleapis.com/analytics-report-{env}/monthly/{pid}/report-{year_month}.csv".format(
        env=environment, pid=partner_id, year_month=year_month)
    target_path = f"/ftproot/directwest/Analytics/SiteAnalyticsReport-Monthly-{year_month}.csv"
    urllib.request.urlretrieve(file_url, target_path)

save_file_to_ftp()

