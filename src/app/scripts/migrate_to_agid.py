"""
Functions to help with migrating account group ids into account entities
"""

import logging

from google.appengine.ext import deferred

from app.models.microsite import Microsite
from app.domain.social import SitesVSocial


def migrate_microsites_to_agid(partner_list):
    """
    Looks through each microsite to see if it has a valid agid. If it has one it will
    add it to the microsite entity. If not, it will say what happened but not add
    it to any account group.

    Do a regex search for the below values to see those types of results
    'AGID MIGRATION - INFO %s' - Some information about the migration
    'AGID MIGRATION - OKAY' - The microsite already has a working agid
    'AGID MIGRATION - NO AGID, INVESTIGATE' - wasn't associated to an account group
    'AGID MIGRATION - GROUPED' - the microsite entity was updated with it's agid

    run the script in the interactive console using the below code:
    from app.scripts.migrate_to_agid import migrate_microsites_to_agid
    migrate_microsites_to_agid(['PID1', 'PID2', ...])

    :param: partner_list a list of pids under which to verify the microsite agids ['pid1', 'pid2', ...]
    """

    #get each partner's spgid
    #partner_list = [{'pid': pid, 'spgid': Partner.get(Partner.build_key(pid)).spgid} for pid in partner_list]

    # find the microsites without valid agids and give them valid agids
    num_microsites_processed = 0
    for partner_id in partner_list:
        microsites = Microsite.lookup_all(pid=partner_id, count=10000)
        for microsite in microsites:
            # run the task
            print("processing " + partner_id + ":" + microsite.msid)
            num_microsites_processed += 1
            deferred.defer(_process, microsite)

    print(f"see logs for processing details on {num_microsites_processed} microsites")


def _process(microsite):
    """ register microsite with new agid if it doesn't have a valid one

    :param microsite: a microsite entity
    """
    # Grab accounts registered to this microsite's spid
    registrations = SitesVSocial(microsite.msid).lookup_social_registrations_for_site()
    logging.info("AGID MIGRATION - INFO: Found these registrations on the social profile: %s", registrations)
    agid = None
    # Set the agid to the account id for vbc
    for account in registrations:
        if account.get('uid', '') == 'vbc':
            agid = account.get('accountId', None)
    if agid:
        # If the microsite doesn't have an agid, let's use core to get one
        if not microsite.agid or microsite.agid != agid:
            microsite.agid = agid
            logging.info('AGID MIGRATION - GROUPED: Set microsite agid to %s for this msid: %s',
                         agid, microsite.msid)
            microsite.put()
        else:
            logging.info('AGID MIGRATION - OKAY: No changes made.')
    else:
        # Putting None in the agid so it is <null> in the datastore instead of <missing>
        microsite.agid = None
        microsite.put()
        logging.error("AGID MIGRATION - NO AGID, INVESTIGATE: Couldn't get agid for microsite '%s'",
                      microsite.msid)
