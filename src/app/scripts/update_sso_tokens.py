""" Script for updating one or many sso tokens """
import logging

from google.appengine.ext import deferred

from app.constants import DEFERRED_ROOT
from app.models.microsite import Microsite


def update_sso_tokens(pid, account_group_id_sso_token_list):
    """
    Update the sso token of the microsites within a pid
    :param agid_token_list: A list of tuples of (account group id, new sso token)
    Ex. One sso token
        update_sso_tokens('FRAN', ('AG-123', 'sso_token_123'))
    Ex. Many sso tokens
        update_sso_tokens('FRAN', [('AG-123', 'sso_token_123'), ('AG-124', 'sso_token_124')])
    """
    if not isinstance(account_group_id_sso_token_list, list):
        account_group_id_sso_token_list = [account_group_id_sso_token_list]
    for account_group_id_sso_token in account_group_id_sso_token_list:
        account_group_id, new_sso_token = account_group_id_sso_token
        logging.info('Deferring update_sso_token for pid/agid %s/%s to new token %s', pid, account_group_id,
                     new_sso_token)
        deferred.defer(update_sso_token, pid, account_group_id, new_sso_token,
                       _url=DEFERRED_ROOT + f'/update_sso_token/{account_group_id}')


def update_sso_token(pid, account_group_id, new_sso_token):
    """
    Updates the microsite's sso token specified by pid and account group id
    """
    microsite = Microsite.lookup_by_agid(account_group_id, pid=pid)
    logging.info('Updating microsite %s sso token from %s to %s', microsite.msid, microsite.sso_token, new_sso_token)
    microsite.sso_token = new_sso_token
    microsite.put()
