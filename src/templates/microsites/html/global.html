<!DOCTYPE html> {# HTML5 doctype: http://www.w3.org/TR/2008/WD-html5-diff-20080122/#doctype #}
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    {% include "html/includes/google_tag_manager.html" %}
    <title>{% block titletag %}{{ title }}{% endblock %}{% block after_title %}{% endblock %}</title>

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="referrer" content="origin">

    {% if hide_from_robots %}
    <meta name="robots" content="noindex">
    {% endif %}

    {% block shortcuticon %}
        {% if shortcut_url %}
            <link rel="apple-touch-icon" href="{{ shortcut_url }}"/>
            <link rel="apple-touch-icon-precomposed" href="{{ shortcut_url }}"/>
        {% else %}
            <link rel="apple-touch-icon" href="{{ '/static/images/default/shortcut.png'|vurl }}"/>
            <link rel="apple-touch-icon-precomposed" href="{{ '/static/images/default/shortcut.png'|vurl }}"/>
        {% endif %}
    {% endblock shortcuticon %}

    {% block meta %}{% endblock %}

    {% block favicon %}
        {% if favicon_url %}
            <link rel='shortcut icon' href='{{ favicon_url }}' type='image/x-icon' />
        {% else %}
            <link rel='shortcut icon' href='/favicon.ico' type='image/x-icon' />
        {% endif %}
    {% endblock favicon %}

    <link href="{{ '/static/css/vendor/gritter/jquery.gritter.min.css'|vff }}" rel="stylesheet" type="text/css" />
    <link href="{{ '/static/css/vendor/IcoMoon/legacy/style.css'|vurl }}" media="screen, projection" rel="stylesheet" type="text/css" />
    <link href="{{ '/static/css/vendor/IcoMoon/style.css'|vurl }}" media="screen, projection" rel="stylesheet" type="text/css" />
    <link href="{{ '/static/css/shared-styles.css'|vff }}" rel="stylesheet" type="text/css" />
    <link href="{{ '/static/css/global.css'|vurl }}" rel="stylesheet" type="text/css" />

    {% block platformcss %}{% endblock platformcss %}
    {% block css %}
        {% if css_url %} {# error pages do not always have a css_url #}
            <link href="{{ css_url }}" rel="stylesheet" type="text/css"/>
        {% endif %}
    {% endblock %}

    {% block jquery %}
        <script src="{{ '/static/js/jquery-1.9.1.min.js'|vff }}" type="text/javascript"></script>
        <script src="{{ '/static/js/jquery-ui-1.10.3.min.js'|vff }}" type="text/javascript"></script>
        <script src="{{ '/static/js/vendor/jquery.ui.widget.js'|vff }}" type="text/javascript"></script>
        <script src="{{ '/static/js/jquery.dialogOptions.min.js'|vff }}" type="text/javascript"></script>
    {% endblock %}

    {% block closeheadtag %}
        <script src="{{ '/static/js/knockout-3.2.0.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/knockout-postbox.min.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/inheritance.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/vendor/jquery.cookie.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/vendor/zero-clipboard/ZeroClipboard.min.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/vendor/jquery.gritter.min.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/custom.gritter.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/custom.ko.utils.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/moment.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/vform/forms.js'|vurl }}" type="text/javascript" charset="utf-8"></script>
    {% endblock %}

    {% block header_includes %}
        {% include "html/includes/google_analytics.html" %}
        {% include "html/includes/hotjar.html" %}
    {% endblock %}
    {% block google_analytics_header %}
        {% if google_analytics_id %}
            <script>
                (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
                (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
                m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
                })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

                ga('create', '{{ google_analytics_id }}', 'auto');
                ga('set', 'customDimension1', '{{ ua_profile }}');
                ga('set', 'customDimension2', '{{ msid }}');
                {% if siteid  %}
                    ga('set', 'customDimension3', '{{ siteid }}');
                {% endif %}
                {% if customerid  %}
                    ga('set', 'customDimension4', '{{ customerid }}');
                {% endif %}

                {% block google_analytics_track_page_view %}
                    {# NOTE: phone page views (including ajax) are tracked below on pageshow event #}
                    ga('send', 'pageview');
                {% endblock %}

                {% if origin_qr %}
                    ga('send', 'event', '{{ msid }}', 'QR');
                {% endif %}
            </script>
        {% endif %}
        <script type="text/javascript" src="/static/js/ga-utils.js"></script>
        {% if debug %}
            <script type="text/javascript" src="{{ '/static/js/analytics-debug.js'|vff }}"></script>
        {% endif %}
    {% endblock %}
    {% block JSON_LD %}{% endblock %}
</head>
<body id="{% block pageid %}{% endblock %}" class="ua-profile-{{ ua_profile }}">
    {% if google_tag_manager_auth_code %}
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id={{ google_tag_manager_container_id }}&gtm_auth={{ google_tag_manager_auth_code }}&gtm_preview={{ google_tag_manager_preview }}&gtm_cookies_win=x"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    {% endif %}

    <div id='page'>
        {% block page %}{% endblock %}
    </div>

{% block script %}
    <div id="chat">
        {% block chat %}{% endblock chat %}
    </div>

    <script type="text/javascript">
        $(function() {
            {% for flash in flashes %}
                {
                    var $title = 'Success';
                    var $text = 'Some text';
                    {% for key, value in flash.items() %}
                        {% if key == 'title' %}
                            $title = '{{ value }}';
                        {% elif key == 'text' %}
                            $text = '{{ value }}';
                        {% endif %}
                    {% endfor %}
                    if ($title == 'Success') {
                        $.gritter.success($text);
                    } else if ($title == 'Error') {
                        $.gritter.error($text);
                    } else {
                        $.gritter.info($text);
                    }
                }
            {% endfor %}
        });
    </script>
{% endblock %}

{% block closebodytag %}
    <!-- Platform: {% block platform_note %}{% endblock %} -->
    <!-- Theme: {% block theme_note %}{% endblock %} -->
    <!-- Template: {% block template_note %}{% endblock %} -->
{% endblock closebodytag %}

{% block google_analytics_footer %}

    {% if google_analytics_id %}
        {% block google_analytics_mobile_event_tracking %}
            {# allows page views to be tracked via pageshow event, including those retrieved via ajax #}
        {% endblock %}

        {# Universal Analytics script is already loaded in the header, no need for additional script here #}
    {% endif %}

    <div id="fb-root"></div>
    <script type="text/javascript">
        if ( window.self !== window.top )
        {
            var fb_script = document.createElement('script');
            fb_script.type = "text/javascript";
            fb_script.src = "//connect.facebook.net/en_US/all.js";
            document.body.appendChild(fb_script);

            window.fbAsyncInit = function(){
                FB.init({ appId: '{{ fb_app_id }}',
                    status: true,
                    cookie: true,
                    xfbml: true,
                    oauth: true
                });
                FB.Canvas.setSize();
            };
        }
    </script>

{% endblock %}

{% if debug %}
    <div id="debug-info">
        <div>
            <div>
                SPID: <a href="{{ social_profile_link }}" target="_BLANK">{{ spid }}</a><br />
                MSID: {{ msid }}<br />
                GAE Version: {{ version_id }}<br />
                PID: {{ pid }}<br />
                AGID: {{ agid }}<br />
                SPGID: {{ spgid }}<br />
                MarketID: {{ market_id }}<br />
                CustomerIdentifier: {{ customer_identifier }}<br />
                PMSID: {{ pmsid }}<br />
                <b>to hide this window, add ?debug=false to the url</b><br />
                <b>to show this window on prod, add ?debug=true to the url</b>
            </div>
        </div>
    </div>
{% endif %}

{% block pardot_tracking_code %}{% endblock %}
</body>
</html>
