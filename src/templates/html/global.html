<!DOCTYPE html> {# HTML5 doctype: http://www.w3.org/TR/2008/WD-html5-diff-20080122/#doctype #}
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    {% include "html/includes/google_tag_manager.html" %}
    <title>{% block titletag %}{{ title }}{% endblock %}{% block after_title %}{% endblock %}</title>

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    {% block shortcuticon %}
        {% if shortcut_url %}
            <link rel="apple-touch-icon" href="{{ shortcut_url }}"/>
            <link rel="apple-touch-icon-precomposed" href="{{ shortcut_url }}"/>
        {% else %}
            <link rel="apple-touch-icon" href="{{ '/static/images/default/shortcut.png'|vurl }}"/>
            <link rel="apple-touch-icon-precomposed" href="{{ '/static/images/default/shortcut.png'|vurl }}"/>
        {% endif %}
    {% endblock shortcuticon %}

    {% block meta %}{% endblock %}

    {% block favicon %}
        {% if favicon_url %}
            <link rel='shortcut icon' href='{{ favicon_url }}' type='image/x-icon' />
        {% else %}
            <link rel='shortcut icon' href='/favicon.ico' type='image/x-icon' />
        {% endif %}
    {% endblock favicon %}

    <link href="{{ '/static/css/vendor/gritter/jquery.gritter.min.css'|vff }}" rel="stylesheet" type="text/css" />
    <link href="{{ '/static/css/vendor/IcoMoon/legacy/style.css'|vurl }}" media="screen, projection" rel="stylesheet" type="text/css" />
    <link href="{{ '/static/css/vendor/IcoMoon/style.css'|vurl }}" media="screen, projection" rel="stylesheet" type="text/css" />
    <link href="{{ '/static/css/shared-styles.css'|vff }}" rel="stylesheet" type="text/css" />
    <link href="{{ '/static/js/vendor/sweetalert/sweetalert.css'|vff }}" rel="stylesheet" type="text/css" />
    <link href="{{ '/static/css/global.css'|vurl }}" rel="stylesheet" type="text/css" />

    <script>
        // Removing the code param (if it exists) in the javascript saves doing a server side redirect
        var new_search = window.location.search.replace(new RegExp('[?&]code=[^&#]*(#.*)?$'), '$1');
        // Also need to remove the next URL, because in this case it wouldn't have been used.
        new_search = new_search.replace(new RegExp('[?&]nextUrl=[^&#]*(#.*)?$'), '$1');
        window.history.replaceState({}, document.title, window.location.pathname + new_search);
    </script>

    {% block platformcss %}{% endblock platformcss %}
    {% block css %}
        {% if css_url %} {# error pages do not always have a css_url #}
            <link href="{{ css_url }}" rel="stylesheet" type="text/css"/>
        {% endif %}
    {% endblock %}

    {% block jquery %}
        <script src="{{ '/static/js/jquery-1.9.1.min.js'|vff }}" type="text/javascript"></script>
    {% endblock %}

    {% block closeheadtag %}
        <script src="{{ '/static/js/knockout-3.2.0.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/knockout-postbox.min.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/inheritance.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/vendor/jquery.cookie.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/vendor/jquery.gritter.min.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/custom.gritter.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/moment.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/static/js/vendor/sweetalert/sweetalert.min.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        <script src="{{ '/vform/forms.js'|vurl }}" type="text/javascript" charset="utf-8"></script>
        {% if debug %}
            <script src="{{ '/static/js/analytics-debug.js'|vff }}" type="text/javascript" charset="utf-8"></script>
        {% endif %}
    {% endblock %}

    {% block header_includes %}
        {% include "html/includes/google_analytics.html" %}
        {% include "html/includes/hotjar.html" %}
    {% endblock %}
    {% block google_analytics_header %}{% endblock %}
</head>
<body id="{% block pageid %}{% endblock %}" class="ua-profile-{{ ua_profile }}">
    {% if google_tag_manager_auth_code %}
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id={{ google_tag_manager_container_id }}&gtm_auth={{ google_tag_manager_auth_code }}&gtm_preview={{ google_tag_manager_preview }}&gtm_cookies_win=x"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    {% endif %}

    <div id='page'>
        {% block page %}{% endblock %}
    </div>

{% block script %}
    <script type="text/javascript">
        $(function() {
            {% for flash in flashes %}
                {
                    var $title = 'Success';
                    var $text = 'Some text';
                    {% for key, value in flash.items() %}
                        {% if key == 'title' %}
                            $title = '{{ value }}';
                        {% elif key == 'text' %}
                            $text = '{{ value }}';
                        {% endif %}
                    {% endfor %}
                    if ($title == 'Success') {
                        $.gritter.success($text);
                    } else if ($title == 'Error') {
                        $.gritter.error($text);
                    } else {
                        $.gritter.info($text);
                    }
                }
            {% endfor %}
        });
    </script>
{% endblock %}

{% block closebodytag %}{% endblock %}

{% block google_analytics_footer %}{% endblock %}

{% if debug %}
    <div id="debug-info">
        <div>
            <div>
                SPID: <a href="{{ social_profile_link }}" target="_BLANK">{{ spid }}</a><br />
                MSID: {{ msid }}<br />
                GAE Version: {{ version_id }}<br />
                PID: {{ pid }}<br />
                AGID: {{ agid }}<br />
                SPGID: {{ spgid }}<br />
                MarketID: {{ market_id }}<br />
                CustomerIdentifier: {{ customer_identifier }}<br />
                PMSID: {{ pmsid }}<br />
                <b>to hide this window, add ?debug=false to the url</b><br />
                <b>to show this window on prod, add ?debug=true to the url</b>
            </div>
        </div>
    </div>
{% endif %}

{% block pardot_tracking_code %}{% endblock %}
</body>
</html>
